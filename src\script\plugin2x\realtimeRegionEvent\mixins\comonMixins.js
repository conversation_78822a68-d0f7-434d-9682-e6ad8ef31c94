import { setLengendLength, formatNum } from '@/script/utils/index';
import commonMixins from '@/script/mixins/commonMixins.js';

const pieColorList = {
    '<=60s': '#5572C3',
    '61-120s': '#5B8FF9',
    '121-300s': '#6BDCAF',
    '301-600s': '#F6BD16',
    '601-1200s': '#FF9D4D',
    '>1200s': '#E8684A'
};
const trendOps = [
    { label: '>60s', value: 'over60List' },
    { label: '>120s', value: 'over120List' },
    { label: '>300s', value: 'over300List' },
    { label: '>600s', value: 'over600List' },
    { label: '>1200s', value: 'over1200List' }
];
const comonMixins = {
    data() {
        return {
            trendValue: 'over60List',
            trendOps: trendOps,
            countList: {},
            targetLineData: {},
            pieData: {},
            totalCount: {},
            oldTargetLineData: {},
            lengendLength: 221
        };
    },
    mixins: [commonMixins],
    computed: {
        startTime() {
            const time = this.searchParams.time;
            if (time && time.length) {
                return time[0];
            }
            return '';
        },
        endTime() {
            const time = this.searchParams.time;
            if (time && time.length) {
                return time[1];
            }
            return '';
        },
        left() {
            return `calc(50% + ${this.lengendLength}px)`;
        }
    },
    methods: {
        getRegionTaskCount(taskId) {
            const params = {
                // nowTime:new Date().format('yyyy-MM-dd HH:mm:ss')
                startTime: new Date().format('yyyy-MM-dd 00:00:00'), //查询开始时间
                endTime: new Date().format('yyyy-MM-dd HH:mm:ss') //查询结束时间
            };
            if (taskId) {
                params.taskId = taskId;
            }
            this.getPost(
                'post',
                'regionEventRecordCount',
                params,
                '订购任务数、区域数、数据量总数查询',
                (data) => {
                    this.countList = Object.assign({}, this.countList, data);
                }
            );
        },
        getTotalCount(taskId) {
            const params = {
                startTime: this.startTime,
                endTime: this.endTime
            };
            if (taskId) {
                params.taskId = taskId;
            }
            this.getPost('post', 'regionEventRecordCount', params, '数据量总数查询', (data) => {
                let newData = data;
                let totalRecords = formatNum(
                    Number(Number(newData.recordCount).toFixed(2).replace(/\.00$/g, ''))
                );
                newData.totalRecords = totalRecords;
                this.totalCount = newData;
            });
        },

        getTrendChart(taskId) {
            const params = {
                startTime: this.startTime,
                endTime: this.endTime,
                taskId: taskId || null
            };
            // if (this.searchParams.userId) {
            //     params.userId = this.searchParams.userId;
            // }
            this.getPost('post', 'eventTrendChart', params, '电子围栏趋势图查询', (data) => {
                if (!data || !data.over60List.length) {
                    this.oldTargetLineData = {};
                    this.targetLineData = {};
                    return;
                }
                this.oldTargetLineData = data;
                this.hadleTrendData(data);
            });
        },
        hadleTrendData(data) {
            let chartData = {
                title: '每分钟记录总量',
                xAxisData: data.recordList.map((item) => item.timePoint),
                yAxis: ['记录数（条）', '高时延占比（%）'],
                unit: ['条', '%'],
                legend: {
                    记录数: `平均：${data.avgNum}条  总计：${data.totalNum}条`
                },
                lengendRight: true,
                lineData: [
                    {
                        name: '记录数',
                        data: data.recordList.map((item) => item.value),
                        color: '#5586EA'
                    },
                    {
                        name: '高时延占比',
                        data: data[this.trendValue].map((item) => {
                            return Number(item.value).toFixed(2);
                        }),
                        color: '#5AD8A6'
                    }
                ]
            };
            this.lengendLength = setLengendLength(chartData);
            this.targetLineData = chartData;
        },
        changeTrend() {
            this.hadleTrendData(this.oldTargetLineData);
        },
        getPieData(taskId) {
            const params = {
                startTime: this.startTime,
                endTime: this.endTime,
                taskId: taskId
            };
            this.getPost('post', 'eventDelayChart', params, '电子围栏时延饼图查询', (data) => {
                const { delayChartList } = data;
                if (!delayChartList.length) {
                    this.pieData = {};
                    return;
                }
                const list = JSON.parse(JSON.stringify(this.initPieData));
                list.pieData = delayChartList.map((item) => {
                    return {
                        ...item,
                        itemStyle: {
                            color: pieColorList[item.name]
                        }
                    };
                });
                const percent = {};
                delayChartList.forEach((item) => {
                    percent[item.name] = Number(item.percent).toFixed(2) + '%';
                });
                list.percent = percent;
                this.pieData = list;
            });
        }
    }
};
export default comonMixins;
