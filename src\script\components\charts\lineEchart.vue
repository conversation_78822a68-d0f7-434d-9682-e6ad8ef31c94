<template>
    <div class="line">
        <empty v-show="isEmpty" type="black"></empty>
        <div :id="name" class="line-echart"></div>
    </div>
</template>

<script>
import * as echarts from 'echarts';
import empty from '../empty/index.vue';
import {lineChartsCon} from './chartsConfigure.js';
const initEcharts = (myChart,options,type,that) =>{
    const option = lineChartsCon(type,options);
    if(that.isEmpty){
        option.yAxis.forEach((item) => {
            item.min = 0;
            item.max = 10;
        });
    }
    myChart.clear();
    myChart.setOption(option);
};
export default {
    name:'line-echart',
    components:{
        empty
    },
    props:{
        /**
		 * 趋势图数据
         * {
            title:'每5分钟记录总量  记录时间：2023-03-20',
            xAxisData:['12:00','13:00','14:00','15:00','16:00','17:00','18:00','19:00','20:00','21:00','22:00','23:00'],
            yAxis:['数量（十亿）','一分钟平均时延（分）'],
            unit:['十亿','分'],
            lineData:[
                {
                    name:'记录数',
                    data:[512,434,666,123,113,443,124,123,375,223,344,566,356],
                    color:'#5586EA',
                },
                {
                    name:'时延',
                    data:[523,445,666,223,213,543,224,223,305,323,334,466,336],
                    color:'#5AD8A6',
                }
            ]
           },
		 */
        data: {
            type: Object,
            default: () => ({}),
        },
        name:{
            type: String,
            default: 'line'
        },
        type:{
            type:String,
            default:'singleLineArea',
        }
    },
    data() {
        return {
            myChart: '',
        };
    },
    computed:{
        isEmpty(){
            if(JSON.stringify(this.data) === '{}' || this.data.isNull){
                return true;
            }
            return false;
        }
    },
    watch: {
        data: {
            handler(newV) {
                if (newV.lineData) {
                    this.initEchart();
                }
            },
            deep: true,
        },
    },
    mounted() {
        if (this.data && this.data.lineData) {
            this.initEchart();
        }
    },
    methods: {
        initEchart() {
            this.$nextTick(() => {
                if(!this.myChart){
                    this.myChart = echarts.init(document.getElementById(this.name));
                }
                initEcharts(
                    this.myChart,
                    this.data,
                    this.type,
                    this
                );
            });
        },
        resize(){
            if(this.myChart){
                this.myChart.resize();
            }
        },
        hightLight(params){
            this.$nextTick(() => {
                if(params.downplayDataIndex !== null){
                    this.myChart.dispatchAction({
                            type: 'unselect',
                            seriesIndex: params.seriesIndex,
                            dataIndex: params.downplayDataIndex,
                    });
                    this.myChart.dispatchAction({
                            type: 'downplay',
                            seriesIndex: params.seriesIndex,
                            dataIndex: params.downplayDataIndex,
                    });
                    this.myChart.dispatchAction({
                            type: 'hideTip',
                    });
                }
                this.myChart.dispatchAction({
                    type: 'select',
                    seriesIndex: params.seriesIndex || [0],
                    dataIndex: params.highlightDataIndex || [0],
                });
                this.myChart.dispatchAction({
                    type: 'highlight',
                    seriesIndex: params.seriesIndex || [0],
                    dataIndex: params.highlightDataIndex || [0],
                });
                this.myChart.dispatchAction({
                    type: 'showTip',
                    seriesIndex: params.seriesIndex || [0],
                    dataIndex: params.highlightDataIndex || [0],
                });
            });
        }
    },
};
</script>

<style lang="less" scoped>
.line{
    width:100%;
    height:100%;
    position: relative;
    .empty{
        position: absolute;
        top:20%;
        left: 0%;
        color:#ccc;
    }
}
.line-echart{
    width:100%;
    height:100%;
}
</style>