<!-- 头部导航栏 -->
<template>
    <div class="main-router">
        <img src="@/img/main/sd_title_bg.png" alt="" class="main-router-bg" />
        <img
            src="@/img/main/sd_title_text.png"
            alt=""
            class="main-router-title"
            @click="handleTitleClick"
        />
        <div class="main-router-user">
            <img src="@/img/main/sd_user.png" alt="" class="user-icon" />
            <span class="user-name">{{ userInfo.describe }}</span>
            <img src="@/img/main/sd_down_icon.png" alt="" class="user-down" />
        </div>
        <div class="main-router-menu menu-left">
            <div class="menu-item">
                <img
                    :src="
                        nowSuperPage == '精准位置'
                            ? require('@/img/main/sd_menuLeft_highLight.png')
                            : require('@/img/main/sd_menuLeft.png')
                    "
                    alt=""
                    class="menu-item-icon"
                />
                <el-dropdown class="menu-item-title" placement="bottom" @command="handleCommand">
                    <span class="menu-item-text" :class="{ textNormal: nowSuperPage == '精准位置' }"
                        >精准位置</span
                    >
                    <img src="@/img/main/sd_down_icon.png" alt="" class="menu-item-down" />
                    <el-dropdown-menu
                        v-if="routerMenuList.精准位置.length > 0"
                        slot="dropdown"
                        class="sd-dropdown-menu"
                    >
                        <menu-recursive :items="routerMenuList.精准位置" @command="handleCommand" />
                    </el-dropdown-menu>
                </el-dropdown>
            </div>
            <div class="menu-item">
                <img
                    :src="
                        nowSuperPage == '电子围栏'
                            ? require('@/img/main/sd_menuLeft_highLight.png')
                            : require('@/img/main/sd_menuLeft.png')
                    "
                    alt=""
                    class="menu-item-icon"
                />
                <el-dropdown class="menu-item-title" placement="bottom" @command="handleCommand">
                    <span class="menu-item-text" :class="{ textNormal: nowSuperPage == '电子围栏' }"
                        >电子围栏</span
                    >
                    <img src="@/img/main/sd_down_icon.png" alt="" class="menu-item-down" />
                    <el-dropdown-menu
                        v-if="routerMenuList.电子围栏.length > 0"
                        slot="dropdown"
                        class="sd-dropdown-menu"
                    >
                        <menu-recursive :items="routerMenuList.电子围栏" @command="handleCommand" />
                    </el-dropdown-menu>
                </el-dropdown>
            </div>
        </div>

        <div class="main-router-menu menu-right">
            <div class="menu-item">
                <img
                    :src="
                        nowSuperPage == '能力展示'
                            ? require('@/img/main/sd_menuRight_highLight.png')
                            : require('@/img/main/sd_menuRight.png')
                    "
                    alt=""
                    class="menu-item-icon"
                />
                <el-dropdown
                    v-if="routerMenuList.能力展示.length > 0"
                    class="menu-item-title"
                    placement="bottom"
                    @command="handleCommand"
                >
                    <span class="menu-item-text" :class="{ textNormal: nowSuperPage == '能力展示' }"
                        >能力展示</span
                    >
                    <img src="@/img/main/sd_down_icon.png" alt="" class="menu-item-down" />
                    <el-dropdown-menu slot="dropdown" class="sd-dropdown-menu">
                        <menu-recursive :items="routerMenuList.能力展示" @command="handleCommand" />
                    </el-dropdown-menu>
                </el-dropdown>
                <div v-else class="menu-item-title">
                    <span class="menu-item-text" :class="{ textNormal: nowSuperPage == '能力展示' }"
                        >能力展示</span
                    >
                </div>
            </div>
            <div class="menu-item">
                <img
                    :src="
                        nowSuperPage == '系统维护'
                            ? require('@/img/main/sd_menuRight_highLight.png')
                            : require('@/img/main/sd_menuRight.png')
                    "
                    alt=""
                    class="menu-item-icon"
                />
                <el-dropdown
                    v-if="routerMenuList.系统维护.length > 0"
                    class="menu-item-title"
                    placement="bottom"
                    @command="handleCommand"
                >
                    <span class="menu-item-text" :class="{ textNormal: nowSuperPage == '系统维护' }"
                        >系统维护</span
                    >
                    <img src="@/img/main/sd_down_icon.png" alt="" class="menu-item-down" />
                    <el-dropdown-menu slot="dropdown" class="sd-dropdown-menu">
                        <menu-recursive :items="routerMenuList.系统维护" @command="handleCommand" />
                    </el-dropdown-menu>
                </el-dropdown>
                <div v-else class="menu-item-title">
                    <span class="menu-item-text" :class="{ textNormal: nowSuperPage == '系统维护' }"
                        >系统维护</span
                    >
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import MenuRecursive from './MenuRecursive.vue';
export default {
    components: {
        MenuRecursive
    },
    props: {
        routerMenuList: {
            type: Object,
            default: () => ({ 精准位置: [], 电子围栏: [], 能力展示: [], 系统维护: [] })
        }
    },
    data() {
        return {
            activeIndex: '',
            nowRouteNameList: []
        };
    },
    computed: {
        userInfo() {
            return this.$store.state.user;
        },
        nowSuperPage() {
            if (this.nowRouteNameList.length > 0 && this.activeIndex !== '') {
                const nowPage = this.nowRouteNameList.find(
                    (item) => item.value && item.value.includes(this.activeIndex)
                );
                return (nowPage && nowPage.super) || '';
            }
            return '';
        }
    },
    watch: {
        routerMenuList: {
            handler(val) {
                if (val) {
                    this.nowRouteNameList = this.getLeafNodes(val);
                    // this.setFirstPageActive(); // 注释掉，因为现在添加了门户首页，不需要设置默认页面
                }
            },
            immediate: true
        },
        // 监听sd_appId参数,指定打开嵌套应用
        '$route.query.sd_appId': {
            handler(newVal) {
                if (newVal) {
                    this.selectMenu(newVal);
                    // 创建一个新的query对象，去掉sd_appId参数,防止后面刷新出现问题
                    const newQuery = { ...this.$route.query };
                    delete newQuery.sd_appId;
                    this.$router.replace({
                        query: newQuery
                    });
                }
            },
            immediate: true
        }
    },
    mounted() {},
    methods: {
        selectMenu(value) {
            // 查找当前选中的菜单项
            const currentItem = this.nowRouteNameList.find((item) => item.value === value);
            if (currentItem) {
                // 构建菜单路径数组 [父级菜单名称, 子菜单名称]
                const menuPath = [currentItem.super, currentItem.label];
                this.$emit('jumpRouter', value, menuPath);
            } else {
                this.$emit('jumpRouter', value, []);
            }
        },
        setFirstPageActive() {
            //取第一个下拉框页面为默认页面
            const keys = ['精准位置', '电子围栏', '能力展示', '系统维护'];
            if (Object.keys(this.routerMenuList).length > 0) {
                for (let index = 0; index < keys.length; index++) {
                    const element = keys[index];
                    if (this.routerMenuList[element].length > 0) {
                        console.log(this.routerMenuList[element][0].label);
                        this.activeIndex = this.routerMenuList[element][0].value;
                        this.selectMenu(this.activeIndex);
                        break;
                    }
                }
            }
        },
        //获取所有叶子节点页面
        getLeafNodes(tree) {
            let leaves = [];
            Object.keys(tree).forEach((node) => {
                if (tree[node].length) {
                    leaves = leaves.concat(tree[node]);
                }
            });
            return leaves;
        },
        handleCommand(value) {
            // Dropdown 中 el-menu 的 select 事件与 el-dropdown 的 command 事件均会触发此方法
            this.activeIndex = value;
            this.selectMenu(value);
        },
        handleTitleClick() {
            this.activeIndex = '';
            this.$emit('jumpHomePage');
        }
    }
};
</script>
<style lang="less" scoped>
.main-router {
    height: 5.666667rem;
    position: relative;
    z-index: 10;
    // background: url('../../../../img/main/sd_title_bg.png') no-repeat center center/cover;

    &-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }

    &-title {
        position: absolute;
        top: 0.72rem;
        left: 50%;
        transform: translateX(-50%);
        width: 17.5rem;
        height: 2.333333rem;
        cursor: pointer;
    }

    &-user {
        position: absolute;
        top: 0.888889rem;
        right: 1.333333rem;
        height: 0.888889rem;
        display: flex;
        align-items: center;
        gap: 0.33rem;
        cursor: pointer;
        .user-icon {
            width: 0.888889rem;
            height: 0.888889rem;
        }
        .user-name {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 0.777778rem;
            color: #c9dfff;
        }

        .user-down {
            width: 0.555556rem;
            height: 0.555556rem;
        }
    }

    &-menu {
        position: absolute;
        bottom: 0.444444rem;
        display: flex;
        align-items: center;

        .menu-item {
            position: relative;
            &-icon {
                width: 14.666667rem;
                height: 1.833333rem;
            }

            &-title {
                position: absolute;
                top: 0.277778rem;
                left: 50%;
                transform: translateX(-50%);
                display: flex;
                align-items: center;
                gap: 0.222222rem;
                .menu-item-text {
                    font-family: YouSheBiaoTiHei;
                    font-size: 1rem;
                    color: #7babe0;
                    text-shadow: 0 0.22rem 0.56rem rgba(0, 4, 49, 0.6);
                    // font-style: italic;
                }
                .menu-item-down {
                    width: 0.666667rem;
                    height: 0.666667rem;
                }
            }
        }
    }
    .menu-left {
        left: 7.444444rem;
    }
    .menu-right {
        right: 7.444444rem;
    }
}

.textNormal {
    color: #ffffff !important;
}

.sd-dropdown-menu {
    background: rgba(0, 63, 139, 0.95) !important;
    box-shadow: 0 0.22rem 0.44rem 0 rgba(0, 0, 0, 0.5) !important;
    border-radius: 0.56rem !important;
    border: 0.06rem solid rgba(0, 149, 255, 0.5) !important;

    /deep/.popper__arrow {
        display: none !important;
    }

    .el-dropdown-menu__item {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 0.777778rem;
        color: #ffffff;
    }
}

/deep/ .el-dropdown-menu__item:hover {
    background-color: rgba(0, 149, 255, 0.4) !important;
}
</style>

<style lang="less">
.custom-group-submenu {
    .el-menu {
        display: flex;
        padding-left: 0.44rem;
        .el-menu-item-group {
            margin-right: 1.67rem;
            .el-menu-item-group__title {
                padding-left: 0 !important;
                margin: 0 0.56rem;
                font-size: 0.89rem;
                font-weight: bold;
                color: #333;
                border-bottom: 0.06rem solid #ccc;
            }
            .el-menu-item {
                font-size: 0.78rem;
                color: #000 !important;
                &:hover {
                    color: #0192ff !important;
                    background-color: transparent !important;
                }
            }
        }
    }
}
.custom-submenu {
    .el-menu {
        padding-left: 0.67rem;
        .el-menu-item {
            color: #000 !important;
            &:hover {
                color: #0192ff !important;
                background-color: transparent !important;
            }
        }
    }
}
</style>

<style lang="less">
html {
    font-size: calc(100vw * 18 / 1920);
}
</style>
