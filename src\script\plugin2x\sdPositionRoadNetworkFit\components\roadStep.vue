<template>
    <div class="road-step">
        <template v-if="data.length">
            <div v-for="(item, index) in data" :key="index" class="step">
                <div class="step-item">
                    <template v-if="!item.isRoad">
                        <div class="point-item">
                            <div
                                v-if="item.pointCnt === 1"
                                class="point_info_pointCnt begin-point"
                                :style="{ 'background-image': 'url(' + beginPointImg + ')' }"
                            ></div>
                            <div
                                v-else-if="isLastPoint(item)"
                                class="point_info_pointCnt end-point"
                                :style="{ 'background-image': 'url(' + endPointImg + ')' }"
                            ></div>
                            <div v-else class="point_info_pointCnt">{{ item.pointCnt }}</div>
                            <div class="bar">
                                <span class="bar-title" v-html="item.label"></span>
                                <span
                                    @click="openClosePointClick(item)"
                                    :style="{ 'background-image': 'url(' + obodeImg + ')' }"
                                    class="img"
                                ></span>
                            </div>
                        </div>
                        <div
                            :class="[
                                'point_info_div',
                                { isHightLight: clickPoint === item.pointCnt }
                            ]"
                            v-show="item.showPointInfo"
                            @click="moveToPoint($event, item)"
                        >
                            <div class="point_info_text">{{ getLngLat(item) }}</div>
                            <div class="point_info_text">{{ getEciInfo(item) }}</div>
                        </div>
                    </template>
                    <template v-else>
                        <div class="point-item">
                            <div class="point_info_cicle"></div>
                            <div class="bar">
                                <span class="bar-title" v-html="item.label"></span>
                                <span
                                    @click="openCloseClick($event)"
                                    :style="{ 'background-image': 'url(' + roadImg + ')' }"
                                    class="img"
                                ></span>
                            </div>
                        </div>
                        <div class="road-item-div road-open">
                            <el-tree
                                class="road-tree"
                                highlight-current
                                :check-on-click-node="true"
                                ref="key"
                                node-key="key"
                                :data="item.children"
                                :props="defaultProps"
                                @node-click="roadClick"
                                @node-collapse="roadExpand"
                                @node-expand="roadExpand"
                            >
                                <span class="custom-tree-node" slot-scope="{ node, data }">
                                    <span
                                        class="is-leaf el-tree-node__expand-icon el-icon-caret-right tree-icon"
                                        v-if="data.url && isShowTreeIcon"
                                        :style="{
                                            'background-image': 'url(' + data.url + ')',
                                            'background-size': '100% 100%'
                                        }"
                                    ></span>
                                    <span class="el-tree-node__label">{{ node.label }}</span>
                                </span>
                            </el-tree>
                        </div>
                    </template>
                </div>
            </div>
        </template>
        <empty v-else></empty>
    </div>
</template>
<script>
import obodeImg from '../../../../img/icon/point.png';
import roadImg from '../../../../img/icon/road.png';
import beginPointImg from '../../../../img/icon/begin_point.png';
import endPointImg from '../../../../img/icon/end_point.png';
import empty from '_com/empty/index.vue';
export default {
    name: 'roadStep',
    components: {
        empty
    },
    props: {
        data: {
            type: Array,
            default: () => []
        },
        isShowTreeIcon: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            obodeImg,
            roadImg,
            beginPointImg,
            endPointImg,
            defaultProps: {
                children: 'children',
                label: 'label'
            },
            clickPoint: null
        };
    },
    mounted() {},
    methods: {
        isLastPoint(item) {
            // 获取非道路类型的点
            const points = this.data.filter((d) => !d.isRoad);
            // 如果当前点的pointCnt等于最后一个点的pointCnt，则是最后一个点
            return points.length > 0 && item.pointCnt === points[points.length - 1].pointCnt;
        },
        getLngLat(item) {
            //如果返回的是0点，就变化避免报错
            let latitude = Number(item.loc.lat) || 0.000001;
            let longitude = Number(item.loc.lng) || 0.000001;
            return `驻点经纬度：${longitude.toFixed(7)},${latitude.toFixed(7)}`;
        },
        getEciInfo(item) {
            if (!item.ECI) return;
            // let {eciid=''}=item.eci;
            return `eciID：${item.ECI}`;
        },
        openClosePointClick(item) {
            item.showPointInfo = !item.showPointInfo;
        },
        openCloseClick(e) {
            let dom = $(e.target).parent().parent().parent().find('.road-item-div');
            if (dom.hasClass('road-open')) {
                dom.removeClass('road-open');
                dom.addClass('road-close');
            } else {
                dom.removeClass('road-close');
                dom.addClass('road-open');
            }
        },
        roadClick(i, node, dom) {
            if (!node.childNodes.length) {
                this.$emit('roadClick', i.index);
            }
        },
        /**
         *
         * @method 节点展开
         */
        roadExpand() {
            $('.bgGreen').removeClass('bgGreen');
        },
        moveToPoint(e, item) {
            this.clickPoint = item.pointCnt;
            this.$emit('moveToPoint', e, item);
        }
    }
};
</script>

<style lang="less" scoped>
.road-step {
    width: 100%;
    border-left: 1px solid rgba(46, 117, 253, 0.3);
    .step-item {
        transform: translate(-12px, 3px);
    }
    .step:first-child {
        transform: translateY(-6px);
    }
    .step + .step {
        padding-top: 10px;
    }
    .point-item {
        display: flex;
        align-items: center;
    }
    .point_info_pointCnt {
        border: 2px solid #d4e9ff;
        border-radius: 100px;
        width: 22px;
        height: 22px;
        font-size: 12px;
        display: inline-block;
        text-align: center;
        vertical-align: bottom;
        color: white;
        background: linear-gradient(90deg, #2a93ff 0%, #409eff 100%);
        box-shadow: 0px 2px 5px 1px rgba(11, 94, 255, 0.3);
        margin-right: 10px;

        &.begin-point,
        &.end-point {
            background: none;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            font-size: 0; /* 隐藏数字 */
            color: transparent; /* 隐藏数字 */
        }
    }
    .bar {
        flex: 1;
        background: #093e79;
        border-radius: 2px;
        display: flex;
        justify-content: space-between;
        height: 28px;
        padding: 0 10px;
        align-items: center;
    }
    .bar-title {
        font-family: PingFangSC, PingFang SC;
        font-size: 12px;
        font-weight: 500;
        color: #fff;
    }
    .img {
        width: 14px;
        height: 14px;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        cursor: pointer;
    }
    .point_info_div {
        margin: 10px 0 0 34px;
        cursor: pointer;
        padding: 5px;
        &.isHightLight {
            background: #0095ffff;
            border-radius: 2px;
        }
    }
    .point_info_text {
        font-size: 12px;
        color: #c9dfffff;
    }
    .point_info_cicle {
        width: 15px;
        height: 15px;
        border-radius: 100px;
        border: 2px solid #d4e9ff;
        background: #409eff;
        margin-right: 14px;
        margin-left: 4px;
    }
    .road-item-div {
        padding: 10px 0 0 34px;
        &.road-open {
            display: block;
        }
        &.road-close {
            display: none;
        }
    }
}
/deep/.el-tree-node__label {
    font-size: 12px;
}
</style>
<style lang="less">
.road-tree.el-tree {
    background-color: transparent !important;
    color: #c9dfffff !important;
    .el-tree-node {
        &__content {
            &:hover {
                background: rgba(0, 149, 255, 0.2) !important;
                border-radius: 2px;
                border: 1px solid rgba(0, 149, 255, 0.7);
            }
        }
        &.is-current > .el-tree-node__content {
            background: #0095ffff !important;
            border-radius: 2px;
        }
        &:focus > .el-tree-node__content {
            background-color: transparent;
        }
    }
}
</style>
