/**
 * 格式化日期，添加时间部分
 * @param {string} date 日期字符串
 * @param {boolean} isStart 是否是开始时间（默认true）
 * @returns {string} 格式化后的时间字符串，格式为 'YYYY-MM-DD HH:mm:ss'
 */
export function formatDate(date, isStart = true) {
    if (!date) return '';

    // 检查是否是有效的日期格式
    if (!/^\d{4}-\d{1,2}-\d{1,2}(\s\d{1,2}:\d{1,2}:\d{1,2})?$/.test(date)) {
        console.error('无效的日期格式:', date);
        return date;
    }

    // 检查是否已经包含时分秒
    if (date.includes(':')) {
        return date; // 已经包含时分秒，直接返回
    }

    // 补充时分秒
    return isStart ? `${date} 00:00:00` : `${date} 23:59:59`;
}

/**
 * 获取调整后的格式化时间
 * @param {Object} options 配置选项
 * @param {Date} [options.baseDate=new Date()] 基准时间
 * @param {number} [options.amount=0] 时间调整量
 * @param {string} [options.unit='day'] 时间单位，支持 'hour'、'day'、'month'、'year'
 * @param {boolean} [options.useCurrentTime=true] 是否使用当前时分秒
 * @param {boolean} [options.isStartTime] 是否作为时间范围的开始时间（当useCurrentTime=false时有效）
 * @returns {string} 格式化后的时间字符串 'YYYY-MM-DD HH:mm:ss'
 */
export function getFormattedTime({
    baseDate = new Date(),
    amount = 0,
    unit = 'day',
    useCurrentTime = true,
    isStartTime = true
} = {}) {
    const date = new Date(baseDate);

    // 根据单位调整时间
    switch (unit.toLowerCase()) {
        case 'hour':
            date.setHours(date.getHours() - amount);
            break;
        case 'day':
            date.setDate(date.getDate() - amount);
            break;
        case 'month':
            date.setMonth(date.getMonth() - amount);
            break;
        case 'year':
            date.setFullYear(date.getFullYear() - amount);
            break;
        default:
            throw new Error('Invalid unit. Supported units: hour, day, month, year');
    }

    // 如果不使用当前时分秒，则设置时间
    if (!useCurrentTime) {
        if (isStartTime) {
            date.setHours(0, 0, 0, 0);
        } else {
            date.setHours(23, 59, 59, 999);
        }
    }

    // 格式化时间
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * 获取时间范围 [startTime, endTime]
 * @param {Object} options 配置选项
 * @param {number} [options.amount=30] 时间调整量
 * @param {string} [options.unit='day'] 时间单位
 * @param {Date} [options.endDate=new Date()] 结束时间
 * @param {boolean} [options.useCurrentTime=true] 是否使用当前时分秒
 * @returns {string[]} 时间范围数组 ['YYYY-MM-DD HH:mm:ss', 'YYYY-MM-DD HH:mm:ss']
 */
export function getTimeRange({
    amount = 30,
    unit = 'day',
    endDate = new Date(),
    useCurrentTime = true
} = {}) {
    const startTime = getFormattedTime({
        baseDate: endDate,
        amount,
        unit,
        useCurrentTime,
        isStartTime: true
    });
    const endTime = getFormattedTime({
        baseDate: endDate,
        amount: 0,
        unit,
        useCurrentTime,
        isStartTime: false
    });
    return [startTime, endTime];
}
/*
console.log(getFormattedTime({ amount: 3, unit: 'day', useCurrentTime: false })); // 获取3天前的时间（不使用当前时分秒）
console.log(getTimeRange({ amount: 30, useCurrentTime: false })); // 获取最近30天范围（使用整天）
console.log(getTimeRange({ amount: 1, unit: 'month' })); // 获取最近1个月范围（使用当前时分秒）
*/
