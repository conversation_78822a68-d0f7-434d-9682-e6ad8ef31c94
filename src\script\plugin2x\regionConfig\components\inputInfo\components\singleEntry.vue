<template>
    <div class="single-entry">
        <div class="wrap" v-for="(item, inx) in formColumns" :key="inx">
            <sectionTitleOne
                :title="item.title"
                :fontSize="16"
                :height="36"
                :marginBottom="0"
            ></sectionTitleOne>
            <div class="content">
                <searchBar
                    class="search-bar-dark"
                    :formCols="item.children"
                    :form="form"
                    :isTransparentBg="true"
                ></searchBar>
            </div>
        </div>
    </div>
</template>

<script>
import searchBar from '_com/searchBar/searchBar.vue';
import sectionTitleOne from '_com/sectionTitle/sectionTitleOne.vue';
import { singleEntryColumns } from '@/script/constant/resourceCreate.js';
import { request } from '@/script/utils/request.js';
export default {
    name: 'single-entry',
    components: {
        searchBar,
        sectionTitleOne
    },
    props: {
        form: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            cityOps: [],
            layerIdOpts: []
        };
    },
    computed: {
        formColumns() {
            return singleEntryColumns({
                city: this.cityOps,
                layerIds: this.layerIdOpts
            });
        }
    },
    created() {
        this.initOptions();
    },
    methods: {
        async validate() {
            try {
                return await this.$refs.singleFormRef.validate();
            } catch (err) {
                return err;
            }
        },
        initOptions() {
            Promise.all([
                request('post', 'getCityDistrict', {}),
                request('post', 'getLayer', {})
            ]).then((res) => {
                const [cityRes, layerRes] = res;
                this.cityOps = cityRes.cityList;
                this.layerIdOpts = layerRes.layerList.map(({ name, value }) => ({
                    label: name,
                    value
                }));
            });
        }
    }
};
</script>

<style lang="less" scoped>
.single-entry {
    background-color: transparent;

    .wrap {
        margin-top: 16px;
        // margin-bottom: 32px;
        .content {
            margin-top: 12px;
            padding: 0 4px;

            .el-form-item {
                display: flex;
                flex-direction: column;
                margin-bottom: 2px;

                /deep/ .el-form-item__label {
                    margin-bottom: 0;
                    padding-right: 0px;
                    width: 100% !important;
                    text-align: left;
                }
                /deep/ .el-form-item__content .el-form-item__error {
                    left: auto;
                    right: 0;
                }
            }
        }
        &:not(:last-child)::after {
            content: '';
            display: block;
            width: 100%;
            height: 1px;
            margin-top: 12px;
            background: linear-gradient(
                to right,
                #00ffff 0px,
                #00ffff 10px,
                rgba(0, 149, 255, 0.5) 10px,
                rgba(0, 149, 255, 0.5) calc(100% - 10px),
                #00ffff calc(100% - 10px),
                #00ffff 100%
            );
        }
    }

    .title {
        position: relative;
        padding-left: 8px;
        line-height: 24px;
        font-weight: bold;
        font-size: 16px;
        font-weight: bold;
        color: #333333;

        &::before {
            content: '';
            position: absolute;
            width: 4px;
            top: 5px;
            left: 0;
            height: 14px;
            background: #0091ff;
            border-radius: 1px;
        }
    }
}

.w-full {
    width: 100%;
}
</style>
