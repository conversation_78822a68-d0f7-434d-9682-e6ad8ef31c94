<template>
    <div class="com-tool-bar">
        <!-- 工具栏 [{key,name,value,type,options}]-->
        <div class="tool-bar-box" v-for="(toolbarBox, i) in toolbarItems" :key="i">
            <div
                v-for="item in toolbarBox"
                :key="item.key"
                class="tool-bar-item"
                :style="{ 'margin-right': gutter }"
            >
                <!-- 文本 -->
                <div
                    :style="{
                        width: item.labelWidth
                    }"
                    v-if="item.label"
                >
                    <div class="tool-bar-item-label">
                        {{ item.label }}
                    </div>
                </div>
                <!-- 下拉选择框 -->
                <el-select
                    v-model="item.value"
                    v-if="item.type === 'select' || item.type === 'multipleSelect'"
                    :placeholder="item.placeholder"
                    :multiple="item.type === 'multipleSelect'"
                    :clearable="item.clearable"
                    :disabled="item.isDisabled"
                    :style="{ width: item.width || width }"
                    collapse-tags
                    :size="size"
                    popper-class="select-dropdown-dark popover-dark"
                    @change="changeToolBarItem(item)"
                    filterable
                >
                    <el-option
                        v-for="i in item.options"
                        :key="i.value || i"
                        :label="i.label || i"
                        :value="i.value || i"
                    >
                    </el-option>
                </el-select>
                <!-- 输入框 -->
                <div
                    :style="{
                        width: item.width || width
                    }"
                    v-else-if="item.type === 'input'"
                >
                    <el-input
                        v-model="item.value"
                        :placeholder="item.placeholder"
                        :size="size"
                        :suffix-icon="item.icon"
                        @change="changeToolBarItem(item)"
                        :style="{
                            ...item.styleClass
                        }"
                        clearable
                    ></el-input>
                </div>
                <!-- 按钮 -->
                <el-button
                    v-else-if="item.type === 'button'"
                    :class="{
                        active: isHighlight && highlight === item.name,
                        ...item.class
                    }"
                    :type="item.style"
                    :size="size"
                    :style="{
                        width: item.width || width,
                        'margin-right': '10px',
                        ...item.styleClass
                    }"
                    @click="changeToolBarItem(item)"
                >
                    {{ item.name }}
                    <i :class="item.icon" v-if="item.icon"></i>
                </el-button>
                <el-switch
                    v-else-if="item.type === 'switch'"
                    v-model="item.value"
                    :active-text="item.activeText"
                    :inactive-text="item.inactiveText"
                    @change="changeToolBarItem(item)"
                >
                </el-switch>
                <!-- 选择时间段 -->
                <el-date-picker
                    v-else-if="item.type === 'rangeDatePicker'"
                    v-model="item.value"
                    type="monthrange"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    @change="changeToolBarItem(item)"
                    clearable
                    popper-class="date-picker-dark popover-dark"
                ></el-date-picker>
                <el-date-picker
                    v-else-if="
                        item.type === 'daterange' ||
                        item.type === 'monthrange' ||
                        item.type === 'datetimerange'
                    "
                    clearable
                    v-model="item.value"
                    :type="item.type"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :disabled="item.isDisabled"
                    :format="item.format"
                    @change="changeToolBarItem(item)"
                    popper-class="date-picker-dark popover-dark"
                    :value-format="
                        item.type === 'daterange'
                            ? 'yyyy/MM/dd'
                            : item.type === 'datetimerange'
                            ? item.format
                            : item.valueFormat
                    "
                >
                </el-date-picker>
                <!-- 选择年 -->
                <el-date-picker
                    v-else-if="item.type === 'yearPicker'"
                    v-model="item.value"
                    type="year"
                    @change="changeToolBarItem(item)"
                    placeholder="选择年"
                    :value-format="item.valueFormat || 'yyyy'"
                    clearable
                    popper-class="date-picker-dark popover-dark"
                >
                </el-date-picker>
                <!-- 行政区列表 -->
                <el-cascader
                    class="cascaderSelect"
                    :style="{ width: item.width || '250px' }"
                    filterable
                    v-else-if="item.type === 'cascaderSelect'"
                    v-model="item.value"
                    :options="item.options"
                    :collapse-tags="item.isTags"
                    size="mini"
                    @change="changeToolBarItem(item)"
                    :placeholder="item.placeholder"
                    :show-all-levels="item.showAllLevels"
                    popper-class="cascader-menu-dark popover-dark"
                    :props="{
                        expandTrigger: 'hover',
                        ...defaultPros,
                        ...item.config
                    }"
                    clearable
                ></el-cascader>
                <!-- 自定义 -->
                <slot v-else-if="item.type === 'slot'" :name="item.field"></slot>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'ComToolBar',
    props: {
        // [[{key,name,value,type,options}],[]]
        toolbarItems: {
            type: Array,
            default: () => []
        },
        gutter: {
            type: String,
            default: () => '10px'
        },

        width: {
            type: String,
            default: () => '180px'
        },
        isHighlight: {
            type: Boolean,
            default: () => false
        },
        size: {
            type: String,
            default: () => 'medium'
        },
        ishightName: {
            type: String,
            default: () => '最新注册'
        }
    },
    data() {
        return {
            highlight: '最新注册',
            defaultPros: {
                // 目录树默认配置项
                children: 'childXzq',
                label: 'xzqmc',
                value: 'xzqdm'
            },
            pickerOptionsYear: {
                disabledDate(time) {
                    return time.getFullYear() < '1949';
                }
            },
            pickerOptionsDate: {
                disabledDate(time) {
                    return time.getTime() < new Date('1949-01-01').getTime();
                }
            },
            serviceIdTree: [],
            routeName: ''
        };
    },
    watch: {
        $route: {
            handler(to, form) {
                this.routeName = to.fullPath;
            },
            immediate: true
        },
        ishightName: {
            handler(newVal) {
                this.highlight = newVal;
            },
            immediate: true
        }
    },
    methods: {
        changeToolBarItem(item) {
            if (item.type === 'button') {
                this.highlight = item.name;
                this.$emit('update:ishightName', item.name);
            }

            this.$emit('changeToolBarItem', item);
        }
    }
};
</script>

<style lang="less" scoped>
.com-tool-bar {
    font-size: 14px;
    font-weight: 400;
    color: #333;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    line-height: 36px;
    margin-left: 11px;

    .tool-bar-box {
        display: flex;
        flex-wrap: wrap;

        .tool-bar-item {
            display: flex;
            height: 36px;
            line-height: 36px;
            margin-bottom: 14px;

            .tool-bar-item-label {
                height: 36px;
                line-height: 36px;
                padding-right: 12px;
                min-width: 60px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 14px;
                color: rgba(255, 255, 255, 0.65);
            }

            .el-button-grey {
                background: #f0f0f0 !important;
                color: #878787 !important;
            }

            .el-switch {
                height: 36px;
                line-height: 36px;
            }

            .active {
                color: #409eff;
            }

            .cascaderSelect {
                width: 300px;
            }
        }
    }
}
</style>
