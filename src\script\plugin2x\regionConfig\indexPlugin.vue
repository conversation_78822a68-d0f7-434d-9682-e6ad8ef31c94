<template>
    <div class="creation mtex-shandong-devops-dark-theme">
        <comNavTitle :navList="navList" />
        <!-- 主体 -->
        <div class="creation__main">
            <cornerCard class="operate">
                <inputInfo v-model="curEntryType" :form="form" @create="createResource" />
            </cornerCard>
            <div class="map">
                <!-- 地图 -->
                <gisMap
                    ref="gisCom"
                    :curType="curEntryType"
                    :city="form.city"
                    :layerList="layerList"
                    :isShowInnerCity="isBulkEntry"
                    :isJustShowLayer="isBulkEntry"
                    :isCreated="false"
                    :defExpansion="3"
                    :shapeType="form.shapeType"
                    isShowTool
                />
            </div>
        </div>
    </div>
</template>

<script>
import comNavTitle from '_com/comNavTitle.vue';
import cornerCard from '_com/cornerCard.vue';
import inputInfo from './components/inputInfo/index.vue';
import gisMap from './components/gis/gisComLast.vue';
import overlapsArea from './components/gis/layerResource/components/overlapsArea.vue';
import sharpInfo from './components/gis/layerResource/components/sharpInfo.vue';
import layerImg from '@/img/createResource/layer.png';
import layerActiveImg from '@/img/createResource/layerActive.png';
import sharpImg from '@/img/createResource/sharp.png';
import sharpActiveImg from '@/img/createResource/sharpActive.png';
import { request } from '@/script/utils/request.js';
import { toPointSequence } from '@/script/utils/method.js';
export default {
    name: 'creation',
    components: {
        comNavTitle,
        cornerCard,
        inputInfo,
        gisMap
    },
    props: {
        outsideParam: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            navList: this.outsideParam,
            curEntryType: 'singleEntry',
            form: {
                regionName: '',
                city: [],
                layerIds: [],
                shapeType: 2,
                describe: ''
            },
            regionId: '', // 当前图层id
            overlapAreas: [],
            curSingleParams: {},
            visible: false,
            inputVisible: false
        };
    },
    computed: {
        layerList() {
            return [
                {
                    label: '重叠区域',
                    prop: overlapsArea,
                    icons: [layerImg, layerActiveImg],
                    isShow: true,
                    attrs: {
                        overlapAreas: this.overlapAreas
                    }
                },
                {
                    label: '轮廓信息',
                    prop: sharpInfo,
                    icons: [sharpImg, sharpActiveImg],
                    isShow: this.curEntryType === 'singleEntry'
                }
            ];
        },
        isBulkEntry() {
            return this.curEntryType === 'bulkEntry';
        }
    },
    watch: {
        curEntryType() {}
    },
    methods: {
        overlapValid(params) {
            return this.$request.resCreateApi.overlapValid(params);
        },
        async createResource(type, fileData) {
            if (type === 'singleEntry') {
                const allAreas = this.$refs.gisCom.planeObjs.filter(Boolean);
                if (!allAreas.length) {
                    this.$message.error('请先绘制图形');
                    return;
                }
                const { regionName, city, layerIds, shapeType, describe } = this.form;
                const [ct, district] = city;
                const regionCoors = toPointSequence(allAreas[0].region.points);
                const circle = allAreas[0].region.circle;
                // 按多边形顺序排序
                const params = {
                    regionName,
                    shapeType,
                    city: ct,
                    district,
                    layerIds,
                    coorsType: 'WGS84',
                    regionCoors,
                    circle,
                    describe
                };
                const res = await request('post', 'addRegionShape', params);
                console.log('res~117', res);
                if (res.regionId) {
                    this.$message.success('创建成功');
                }
                // this.jumpResLayer();
            } else {
                const { file } = fileData;
                // 批量录入
                const params = new FormData();
                params.append('file', file.raw);
                request('post', 'addRegionShapeByExcel', params).then((res) => {
                    console.log('批量上传结果', res);
                    const falseMsg = res.falseMsg || [];
                    const successMsg = (res.successMsg || []).map((item) => item.rowMsg);
                    if (successMsg && successMsg.length) {
                        systemUtil.popupMessage('提示', [...falseMsg, ...successMsg].join('\n'));
                        this.$message.success('批量录入成功');
                        // 跳转到清单列表页面
                        // this.jumpResLayer();
                    } else {
                        systemUtil.popupMessage('提示', falseMsg.join('\n'));
                    }
                });
            }
        },
        formatCity(areas) {
            let city = '',
                district = '';
            for (const area of areas) {
                if (!city && !district) {
                    city = area[0];
                    district = area[1];
                }
            }
            return {
                city,
                district
            };
        },
        toggleInputResult(resetAll) {
            resetAll(true);
            this.inputVisible = true;
        },
        jumpResLayer() {
            let timer = setTimeout(() => {
                this.$router.push({
                    path: 'resManifest',
                    query: {
                        layerName: '专有资源'
                    }
                });
                clearTimeout(timer);
                timer = null;
            }, 1500);
        }
    }
};
</script>

<style lang="less" scoped>
@import url('../../../style/custom.less');
.creation {
    height: 100%;
    display: flex;
    flex-direction: column;

    &__main {
        flex: 1;
        display: flex;
        height: calc(100% - 64px);
        z-index: 2;

        .operate {
            flex-basis: 400px;
            margin: 16px;
            padding: 10px 4px;
            background-color: rgba(0, 42, 92, 0.9);
            border: 1px solid rgba(0, 149, 255, 0.5);
        }

        .map {
            position: relative;
            width: calc(100% - 400px);
            background-color: rgba(0, 42, 92, 0.9);
        }
    }
}
</style>
