<template>
    <div :class="['time-pick',{'border-hight':isShowPanel}]" @click="isShowPanel=!isShowPanel">
        <div class="value">{{value}}</div>
        <i :class="['el-icon-arrow-down','arrow',{'roate':isShowPanel}]"></i>
        <div v-show="isShowPanel" class="time-panel" v-click-outside="onClickOutside">
            <div class="splitline">Relative Time</div>
            <div class="bar">
                <div :class="['bar-item',{'active':item.name === currentTime}]"
                    v-for="(item,index) in barItemList"
                    :key="index"
                 @click="handlerTimeRange(item.time,item.name)">{{item.name}}</div>
            </div>
            <div class="splitline">Absolute Time</div>
            <div class="time-bar" @click.stop="() =>{}">
                <template v-if="!isSingleTime">
                    <span>开始时间</span>
                    <el-date-picker
                        v-model="startTime"
                        class="my-picker"
                        type="datetime"
                        size="mini"
                        popper-class="sd-devops-picker"
                        placeholder="选择日期"
                        value-format="yyyy-MM-dd HH:mm:00"
                        format="yyyy-MM-dd HH:mm"
                        @change="timeChange"
                        >
                    </el-date-picker>
                    <span>结束时间</span>
                    <el-date-picker
                        v-model="endTime"
                        type="datetime"
                        class="my-picker"
                        size="mini"
                        popper-class="sd-devops-picker"
                        value-format="yyyy-MM-dd HH:mm:00"
                        format="yyyy-MM-dd HH:mm"
                        placeholder="选择日期"
                        @change="timeChange">
                    </el-date-picker>
                </template>
                <template v-else>
                    <span>指定时间戳</span>
                    <el-date-picker
                        v-model="time"
                        class="sd-devops-picker"
                        type="datetime"
                        size="mini"
                        popper-class="sd-devops-picker"
                        placeholder="选择日期"
                        value-format="yyyy-MM-dd HH:mm:00"
                        format="yyyy-MM-dd HH:mm"
                        @change="timeChange"
                        >
                    </el-date-picker>
                </template>
                <div class="btn-bar">
                    <el-button type="primary" size="mini" @click="confirm">应用</el-button>
                    <el-button type="info" size="mini" @click.stop="reset">取消</el-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
const panelNotShowClass = ['time-pick','value','time-pick border-hight','el-icon-arrow-down arrow','el-icon-arrow-down arrow roate'];
export default {
    name:'timePickPanel',
    props:{
        isSingleTime:{
            type:Boolean,
            default:false,
        },
        widgetParams:{
            type:Object,
            default:() => ({}),
        }
    },
    data(){
        return{
            value:'最近24小时',
            isShowPanel:false,
            endTime:new Date(),
            startTime:new Date(),
            time:new Date(),
            currentTime:'最近24小时',
            timeRangeList:[
                {
                    name:'最近6小时',
                    time:360,
                },
                {
                    name:'最近24小时',
                    time:1440,
                },
                {
                    name:'最近7天',
                    time:10080,
                },
                {
                    name:'最近30天',
                    time:43200,
                },
            ],
            timeSingleList:[
                {
                    name:'前3小时',
                    time:180,
                },
                {
                    name:'前6小时',
                    time:360,
                },
                {
                    name:'前12小时',
                    time:720,
                },
                {
                    name:'前一天',
                    time:1440,
                },
            ]
        };
    },
    created(){
        const date = new Date();
        this.startTime = new Date(date.setDate(date.getDate() - 1));
        this.init(this.widgetParams);
    },
    computed:{
        barItemList(){
            let list = [];
            if(this.isSingleTime){
                 list = JSON.parse(JSON.stringify(this.timeSingleList));
            }else{
                list = JSON.parse(JSON.stringify(this.timeRangeList));
            }
            const minGranularity = this.widgetParams.minGranularity;
            if(minGranularity === 4){
                list= list.filter((item) => item.name.includes('天'));
            }

            const minRelativeTime = this.widgetParams.minRelativeTime;
            const minRelativeTimeInd =list.findIndex((item) => item.time === minRelativeTime);
            if(minRelativeTimeInd > -1){
                list = list.slice(minRelativeTimeInd);
            }
            return list;
        },
        handlerTimeValue(){
            if(this.isSingleTime){
                return '指定时间戳';
            }
            return '指定时间段';
        }
    },
    watch:{
        widgetParams:{
            handler(newV){
                this.init(newV);
            },
            deep:true,
        }
    },
    methods:{
        onClickOutside(event){
            event.stopPropagation();
            if(panelNotShowClass.includes(event.target.className)){
                return;
            }
            if(this.isShowPanel){
                this.isShowPanel = false;
            }
        },
        init(newV){
            if(JSON.stringify(newV) === '{}'){
                return;
            }
            if(newV.startTime){
                this.startTime = newV.startTime;
            }
            if(newV.endTime){
                this.endTime = newV.endTime;
            }
            if(newV.time){
                this.time = newV.time;
            }
            if(newV.relativeTime){
                const relativeTime = this.barItemList.filter((item) => item.time === newV.relativeTime)[0];
                this.currentTime = relativeTime.name;
                this.value=  relativeTime.name;
            }else{
                this.currentTime = this.handlerTimeValue;
                this.value=  this.handlerTimeValue;
            }
        },
        panelClick(){

        },
        handlerTimeRange(num,text){
            this.currentTime =text;
            this.value= this.currentTime;
            this.$emit('update:timeList',{
                    startTime:new Date(this.startTime).format('yyyy-MM-dd HH:mm:00'),
                    endTime:new Date(this.endTime).format('yyyy-MM-dd HH:mm:00'),
                    relativeTime:num,
                    time:new Date(this.time).format('yyyy-MM-dd HH:mm:00'),
                });
        },
        timeChange(){
            this.currentTime = this.handlerTimeValue;
        },
        reset(){
            this.isShowPanel = false;
        },
        confirm(){
            if(this.isSingleTime){
                if(!this.time){
                    this.$message.error('请选择指定时间戳!');
                    return;
                }
                this.value= this.currentTime;
                this.$emit('update:timeList',{
                    startTime:'',
                    endTime:'',
                    relativeTime:'',
                    time:new Date(this.time).format('yyyy-MM-dd HH:mm:00'),
                });
                this.isShowPanel = false;
                return;
            }
            if(!this.startTime){
                this.$message.error('请选择开始时间!');
                return;
            }
            if(!this.endTime){
                this.$message.error('请选择结束时间!');
                return;
            }
            if(new Date(this.startTime).getTime() > new Date(this.endTime).getTime()){
                this.$message.error('开始时间不能大于结束时间!');
                return;
            }
            this.value= this.currentTime;
            this.$emit('update:timeList',{
                startTime:new Date(this.startTime).format('yyyy-MM-dd HH:mm:00'),
                endTime:new Date(this.endTime).format('yyyy-MM-dd HH:mm:00'),
                relativeTime:'',
                time:'',
            });
            this.isShowPanel = false;
        },
    }
};
</script>

<style lang="less" scoped>
.time-pick{
    height: 28px;
    background: #000000;
    border-radius: 2px 2px 2px 2px;
    opacity: 1;
    padding: 0px 5px 0px 10px;
    display: flex;
    align-items: center;
    cursor: pointer;
    position: relative;
    border: 1px solid #999;
    .value{
        font-size: 12px;
        font-weight: 600;
        color: #999999;
        line-height: 18px;
        padding-right: 10px;
    }
    .arrow{
        color:#999999;
        transition: transform .3s,-webkit-transform .3s;
        font-weight: 600;
    }
    .roate{
        transform: rotateZ(180deg);
        
    }
    &.border-hight{
        border: 1px solid #409EFF;
    }
}
.time-panel{
    width: 220px;
    // height: 340px;
    background: rgba(48,48,48,1);
    border-radius: 2px 2px 2px 2px;
    position: absolute;
    top:40px;
    left:0px;
    z-index: 2000;
    padding: 10px 0px;
}
.splitline{
    position: relative;
    text-align: right;
    overflow: hidden;
    font-size: 12px;
    color: #999;
    margin: 0 10px;
}
.splitline::before,.splitline::after{
    content: '';
    display: inline-block;
    width: 100%;
    height: 1px;
    position: absolute;
    background: #999;
    top: 50%;
}
.splitline::before{
    margin-left: -10px;
    transform: translateX(-100%);
}
.splitline::after{
    margin-left: 10px;
}
.bar-item{
    height: 32px;
    width:100%;
    padding: 0px 10px;
    line-height: 32px;
    font-size: 14px;
    font-weight: 400;
    color: #999999;
    cursor:pointer;
    &.active{
        background: RGBA(63, 67, 68, 1);
        color:#fff;
    }
}
.time-bar{
    margin: 0 10px;
    padding: 10px 0px 0px;
    width:calc(100% - 20px);
    display: flex;
    flex-direction: column;
    span{
        font-size: 12px;
        line-height: 16px;
        font-weight: 400;
        color:#999
    }
}
.bar-item:hover{
    background: RGBA(63, 67, 68, 1);
    color:#fff;
}
.btn-bar{
    display: flex;
    justify-content: flex-end;
}
.my-picker{
    width:100%;
    margin: 10px 0px;
    /deep/.el-input__inner{
        background: RGBA(63, 67, 68, 1);
        color:#999;
        border: 1px solid RGBA(63, 67, 68, 1);
        padding-left: 30px ;
    }
}

</style>
<style lang="less">
.sd-devops-picker{
    .el-picker-panel__footer .el-button--text{
            display: none !important;
    }

    .el-input--small .el-input__inner{
        background: RGBA(63, 67, 68, 1);
        color: #fff;
        border: none;
    }
    .el-picker-panel__footer{
        background: rgba(48,48,48,1);
    }
    .el-time-spinner__item{
        line-height: 18px;
    }
    &.el-picker-panel{
        background: rgba(48,48,48,1);
        box-shadow: inset 0px 0px 18px 0px rgb(2 132 227 / 30%);
        border-radius: 2px;
        border: 1px solid rgba(48,48,48,1);
        z-index: 10011;
    }
    .el-picker-panel__body{
        color:#999;
    }
    .el-picker-panel__icon-btn{
        color:#999;
    }
    .el-time-panel__btn{
        color:#fff;
    }
    .el-time-spinner__item.active:not(.disabled){
        color:#fff;
    }
    .el-time-panel{
        background:#303030;
    }
}
</style>