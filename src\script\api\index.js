// const onlineUrl = 'http://192.168.1.142:18000/mtex/';
const onlineUrl = '/';
const getFullPath = (path) => {
    return onlineUrl + path;
};

export { getFullPath };
const files = require.context('./module', false, /\.js$/);
const Api = {};
console.log(files.keys());
files.keys().map((path) => {
    const moduleList = files(path).default || files(path);
    for (let key in moduleList) {
        Api[key] = moduleList[key];
    }
});
export default Api;
