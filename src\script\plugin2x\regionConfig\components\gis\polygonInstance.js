import * as gisUtils from './common/gisUtils.js';
import { menuList } from './common/gis';
export default class Polygon {
    constructor(i) {
        const h = i || 1;
        const baseVal = h / 100000;
        this.region = {
            prop: 'region',
            points: [], // 区域对应的轮廓坐标
            plane: null,
            maxH: baseVal + (1 / 200), // 动态设置图形高度，防止多个图形高度一致导致颜色渲染不均匀
            color: 0x0085f9,
        };
        this.i = i;
    }
    static initGisInfo(g, compInstance) {
        Polygon.g = g;
        Polygon.compInstance = compInstance;
        Polygon.layer = new g.layer();
        Polygon.layer.visible = true;
        g.gis.scene.add(Polygon.layer);

        // 多边形编辑完成事件
        g.layerList.areaEdit.onEditFinish.addEvent(Polygon.areaEditFinish, 'Polygon');
        // 多边形点击事件
        g.event.addClick(Polygon.layer, (data, event) => {
            if (compInstance.isJustShowLayer) return;
            const operateItem = data.object.operateType;
            compInstance.layerObj = compInstance.planeObjs[operateItem.i];

            if (event.button === 0) {
                // 鼠标左键
                Polygon.setCurOperateItem(operateItem);
                // 清除plane
                Polygon.layer.remove(data.object);
                // 绘制可编辑的多边形
                const mesh = gisUtils.areaEditCreate(g, {
                    name: '编辑图层',
                    color: operateItem.color,
                    points: operateItem.points,
                    justShow: compInstance.isJustShowLayer,
                });
                g.layerList.areaEdit.startEdit(mesh);
            } else if (event.button === 2) {
                // 右键功能-删除
                compInstance.menuList = menuList.filter((item) => item.prop === 'delPolygon');
                compInstance.isShowMenu = true;
            }
        });
    }
    static setCurOperateItem(curOperateItem) {
        Polygon.curOperateItem = curOperateItem;
    }
    static async areaEditFinish(data) {
        const { g, compInstance } = Polygon;
        const editPoints = gisUtils.three2world(g, data.points);
        data.name = '$123';
        g.layerList.areaEdit.removeByName('$123');

        const { prop } = Polygon.curOperateItem;
        // plane渲染多边形
        if (prop === 'region') {
            compInstance.layerObj.drawRegion(editPoints);
        }
    }
    static clearAll(isClearEvent = false) {
        if (isClearEvent) {
            const g = Polygon.g;
            g.event.removeClick(Polygon.layer);
            g.layerList.areaEdit.onEditFinish.removeEvent('Polygon');
        }

        Polygon.layer.removeAll();
    }
    drawRegion(editPoints, inx) {
        const g = Polygon.g;
        const { color, maxH } = this.region;
        const regionPoints = editPoints.map((item) => [item.lng, item.lat]);

        g.meshList.plane.opacity = 0.3;
        //传入数据创建平面
        const item = {
            ls: g.math.lsRegular(regionPoints) || [],
            layers: [{ maxH, color }],
            needFrame: true,
            frameColor: 0x1a7cff,
        };

        const data = [item];
        Object.assign(data, {
            needFrame: true,
            frameColor: 0x1a7cff,
        });
        this.region.plane = g.meshList.plane.create(data);
        // 自定义数据
        Object.assign(this.region, {
            points: editPoints,
            i: Number.isFinite(inx) ? inx : this.i,
        });
        this.region.plane.operateType = this.region;
        //图层添加模型
        Polygon.layer.add(this.region.plane);
        g.gis.needUpdate = true;
    }
    async selectMenu(prop) {
        const {  compInstance } = Polygon;
        if (prop === 'delPolygon') {
            const { region} = this;
            Polygon.layer.remove(region.plane);
            Polygon.currentClickHole = null;
            compInstance.planeObjs[this.i] = null;
            compInstance.layerObj = {};
            // compInstance.setAllBaseStations();
            // compInstance.setBasePoints();
        }
    }
}

