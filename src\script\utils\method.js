const getAreasToCity = (srcDistricts) => {
    const result = [];
    for (const { value, label, children } of srcDistricts) {
        const newItem = {
            label,
            value,
            children: [],
        };
        result.push(newItem);
        for (const { value, label } of children) {
            newItem.children.push({
                label,
                value,
            });
        }
    }
    return result;
};
const toPointSequence = (regionCoors = []) => {
    return regionCoors.map(({ lat, lng }) => `${lng},${lat}`)
        .join(';');
}

export {
    getAreasToCity,
    toPointSequence,
}