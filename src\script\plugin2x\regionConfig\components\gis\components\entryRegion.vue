<template>
  <div class="area-input-box">
    <div class="tip">区域</div>
    <el-input
      class="area-input"
      type="textarea"
      :rows="2"
      :placeholder="tips.placeholder"
      :autosize="{ minRows: 2, maxRows: 4 }"
      v-model="textarea"
    >
    </el-input>
    <div class="tip">空洞</div>
    <el-input
      class="area-input"
      type="textarea"
      :rows="2"
      :placeholder="tips.placeholder"
      :autosize="{ minRows: 2, maxRows: 4 }"
      v-model="holeArea"
    >
    </el-input>
    <div class="example">
      <div class="title">
        <i class="el-icon-circle-check success"></i>
        <span>范例：</span>
      </div>
      <div class="content">{{ tips.example }}</div>
    </div>
    <div class="area-input-footer">
      <el-button size="small" @click="cancel"> 取消 </el-button>
      <span class="split-box"> </span>
      <el-button type="primary" size="small" @click="setAreaCoors()">提交</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'entry-region',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    shapeType: {
      type: Number,
      default: 2,
    },
  },
  data() {
    return {
      textarea: '',
      holeArea: '',
    };
  },
  computed: {
    tips() {
      if (this.shapeType === 2) {
        return {
          placeholder:
            '请输入经纬度（以“,”间隔），至少三个，每个以“;”间隔，若要区分不同区域请以“|”间隔',
          example: '131.32643959259033,30.7321228043094|124.326439592593,31.73212280430',
        };
      }
      return {
        placeholder:
          '请输入圆心经纬度和半径（m），经纬度以“,”间隔，圆心和半径之间以“;”间隔，若要区分不同区域请以“|”间隔',
        example: '121.32643959259033,30.7321228043094;580',
      };
    },
  },
  methods: {
    cancel() {
      this.$emit('update:visible', false);
    },
    parseInput(input) {
      const [center, radius] = input.split(';');
      const [longitude, latitude] = center.split(',');
      return { 
        centerLongitude: parseFloat(longitude), 
        centerLatitude: parseFloat(latitude), 
        radius: parseFloat(radius)
      }
    },
    setAreaCoors() {
      let regionList = this.textarea.split('|') || [];
      const holeList = this.holeArea.split('|') || [];
      if (this.shapeType === 1) {
        regionList = regionList.map(item => this.parseInput(item)) ;
      }
      this.$emit('setAreaCoors', regionList, holeList);
    },
    clear() {
      this.textarea = '';
      this.holeArea = '';
    },
  },
};
</script>

<style lang="less" scoped>
.area-input-box {
  .tip {
    margin: 4px 0;
  }
  .example {
    margin: 12px 0;
    display: flex;
    .content {
      flex: 1;
      color: #666666;
    }
  }
  .area-input-footer {
    text-align: right;
  }
}
.success {
  color: #74c72a;
}
</style>
