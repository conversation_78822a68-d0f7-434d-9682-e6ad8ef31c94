<template>
    <SectionCard class="left-section-card" title="告警监控板" titleBg="title-bg1">
        <div class="alarm-monitor custom-scrollbar">
            <div class="alarm-item" v-for="(item, index) in alarmList" :key="index">
                <div class="alarm-icon">
                    <img src="../../../../img/effectivenessScreen/alarm-icon.png" alt="告警" />
                </div>
                <div class="alarm-content">
                    <div class="alarm-title">
                        <span class="text-ellipsis" :title="item.mailTitle">{{
                            item.mailTitle
                        }}</span>
                        <span class="text-ellipsis">{{ item.sendTime }}</span>
                    </div>
                    <el-tooltip
                        effect="dark"
                        placement="top-start"
                        :content="formatDescription(item.description)"
                        popper-class="alarm-tooltip  tooltip-dark"
                    >
                        <div class="alarm-desc text-ellipsis">
                            {{ formatDescription(item.description) }}
                        </div>
                    </el-tooltip>
                </div>
            </div>
        </div>
    </SectionCard>
</template>

<script>
import SectionCard from './SectionCard.vue';
import commonMixins from '@/script/mixins/commonMixins.js';

export default {
    name: 'AlarmMonitorCard',
    mixins: [commonMixins],
    components: {
        SectionCard
    },
    data() {
        return {
            alarmList: [],
            pageSize: 5,
            loading: false,
            hasMore: true,
            previousLength: 0
        };
    },
    created() {
        this.getAlarmMonitor();
    },
    mounted() {
        this.initScrollListener();
    },
    beforeDestroy() {
        this.removeScrollListener();
    },
    methods: {
        formatDescription(description) {
            return description.replace(/<br\s?\/?>/g, '\n');
            // return description;
        },
        initScrollListener() {
            const alarmMonitor = this.$el.querySelector('.alarm-monitor');
            if (alarmMonitor) {
                alarmMonitor.addEventListener('scroll', this.handleScroll);
            }
        },
        removeScrollListener() {
            const alarmMonitor = this.$el.querySelector('.alarm-monitor');
            if (alarmMonitor) {
                alarmMonitor.removeEventListener('scroll', this.handleScroll);
            }
        },
        handleScroll(e) {
            if (this.loading || !this.hasMore) return;

            const { scrollHeight, scrollTop, clientHeight } = e.target;
            // 当滚动到距离底部20px时触发加载
            if (scrollHeight - scrollTop - clientHeight <= 20) {
                this.loadMore();
            }
        },
        loadMore() {
            if (!this.hasMore) return;
            this.pageSize += 5;
            this.getAlarmMonitor();
        },
        getAlarmMonitor() {
            if (!this.hasMore) return;
            this.loading = true;
            this.getPost(
                'post',
                'alarmMonitor',
                { pageNo: 1, pageSize: this.pageSize },
                '能效展板-告警监控板查询',
                (res) => {
                    // 检查是否有新数据
                    if (res.length === this.previousLength) {
                        this.hasMore = false;
                    }
                    this.previousLength = res.length;
                    this.alarmList = res;
                    this.loading = false;
                }
            );
        }
    }
};
</script>

<style lang="less" scoped>
.alarm-monitor {
    --scrollbar-width: 0.1667rem;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 0.8889rem;
    gap: 0.8889rem;
    overflow-y: auto;

    .alarm-item {
        display: flex;
        align-items: flex-start;
        gap: 0.5556rem;
        min-height: 3.5556rem;
        background: linear-gradient(
            270deg,
            rgba(9, 155, 255, 0.04) 0%,
            rgba(9, 118, 255, 0.21) 100%
        );
        padding: 0.8889rem 0.5556rem;

        .alarm-icon {
            img {
                width: 2.6667rem;
                height: 2.6667rem;
            }
        }

        .alarm-content {
            flex: 1;

            .alarm-title {
                margin-bottom: 0.2778rem;
                display: flex;
                justify-content: space-between;
                align-items: center;
                gap: 0.5556rem;
                span:first-child {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 0.7778rem;
                    color: #ffffff;
                    line-height: 1.1111rem;
                    text-shadow: 0px 0px 0.2778rem rgba(30, 198, 255, 0.8);
                }
                span:last-child {
                    display: flex;
                    min-width: fit-content;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 0.6667rem;
                    color: #1ec6ff;
                }
            }

            .alarm-desc {
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 0.6667rem;
                color: #b0d7ff;
                line-height: 0.8889rem;

                :deep(br) {
                    content: '';
                    display: block;
                    margin: 0.2778rem 0;
                }
            }
        }
    }
}

:global(.alarm-tooltip) {
    max-width: 500px;
    line-height: 1.5;
    .el-tooltip__popper {
        font-size: 0.6667rem;
        white-space: pre-line;
    }
}
</style>
