<template>
    <div class="top-bar">
        <span class="top-bar-title">{{title}}</span>
        <div class="top-bar-right">
            <searchBar ref="formList" :formCols="formCols" :form="form">
                <template #search>
                    <el-button type="primary" size="small" @click="search">查询</el-button>
                </template>
            </searchBar>
        </div>
    </div>
</template>

<script>
import searchBar from './searchBar.vue';
export default {
    name:'topBar',
    components:{
        searchBar
    },
    props:{
        title:{
            type:String,
            default:'地市级常驻人员分布热力图',
        },
        formCols: {
            type: Array,
            default: () => [],
        },
        form: {
            type: Object,
            default: () => ({}),
        },
    },
    methods:{
        search(){
            this.$emit('search',this.$refs.formList.form);
        }
    }
};
</script>

<style lang="less" scoped>
.top-bar{
    width:100%;
    height:64px;
    background: #FFFFFF;
    box-shadow: 0px 0px 5px 1px rgba(13, 59, 128, 0.4);
    display:flex;
    justify-content:space-between;
    align-items:center;
    padding:0 24px;
    z-index: 20;
    position: absolute;
    &-title{
        font-size: 20px;
        font-weight: 600;
        color: #303133;
    }
    &-right{
        height:100%;
        display:flex;
        align-items:center;
        margin-top:10px;
    }
}
</style>