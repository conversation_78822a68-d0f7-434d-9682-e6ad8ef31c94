import {
    addLastStatus,
    toCoordinate,
    formatBaseStations,
    getDiffSet,
    getIntersectionSet,
    isImportCreated,
    positionCircleTest,
    isExistWithinCurCircle
} from './common/method.js';
import { menuList } from './common/gis.js';
export default class Circular {
    constructor(i) {
        const h = i || 1;
        this.region = {
            prop: 'circular',
            circle: {},
            plane: null,
            color: 0xACCEEF,
            frameColor: 0x1A7CFF,
            innerBaseStations: [],
        };
        this.hole = {
            prop: 'hole',
            points: [],
            plane: [],
            color: 0xffffff,
            maxH: h / 100000 + (1 / 100),
        };
        this.radiate = {
            prop: 'radiate',
            circle: {},
            plane: null,
            color: 0xe59f00,
            frameColor: 0x1A7CFF,
        };
        this.baseStations = [];
        this.distance = 3;  // 默认辐射距离
        this.i = i;
    }
    static initGisInfo(g, compInstance) {
        Circular.g = g;
        Circular.compInstance = compInstance;
        Circular.currentClickHole = null;

        Circular.holeLayer = new g.layer();
        Circular.holeLayer.visible = true;
        g.gis.scene.add(Circular.holeLayer);

        // 空洞点击事件
        g.event.addClick(Circular.holeLayer, (data, event) => {
            if (compInstance.isJustShowLayer) return;
            const operateItem = data.object.operateType;
            Circular.currentClickHole = operateItem.index;
            compInstance.layerObj = compInstance.planeObjs[operateItem.i];
            if (event.button === 0) {
                // 鼠标左键
                Circular.setCurOperateItem(operateItem);
                // 清除plane
                Circular.holeLayer.remove(data.object);
                // 绘制可编辑的多边形
                const mesh = g.layerList.areaEdit.createArea({
                    name: '编辑图层',
                    color: operateItem.color,
                    points: operateItem.points,
                    justShow: compInstance.isJustShowLayer,
                });
                g.layerList.areaEdit.startEdit(mesh);
            } else if (event.button === 2) {
                compInstance.menuList = menuList.filter((item) => item.prop === 'delHole');
                compInstance.isShowMenu = true;
            }
        });
        // 多边形编辑完成事件
        g.layerList.areaEdit.onEditFinish.addEvent(Circular.areaEditFinish, 'Circular');
    }
    static setCurOperateItem(curOperateItem) {
        Circular.curOperateItem = curOperateItem;
    }
    static async getBaseStations(circle, expansion, isInit = false, isMulti = false) {
        const compInstance = Circular.compInstance;
        if (!circle || !Object.keys(circle).length) {
            compInstance.$message.warning('区域不存在');
            return;
        }

        const params = {
            type: 1,
            shapeType: 2,
            isMultiRegion: 0,
            expansion, // 辐射距离，10：表示查看区域所有基站
            circle: {
                ...circle,
                radius: circle.radius,
            }
        };
        if (isInit && !compInstance.isMultiRegion) {
            const isCreated = compInstance.isCreated;
            const regionId = compInstance.baseInfo.regionId;
            Object.assign(params, {
                type: isCreated && regionId ? 0 : 1,
                regionId: regionId || undefined,
            });
        }
        const res = await compInstance.$request.resCreateApi.getRegionExpansionCells(params);
        const { regionOutList, regionInnerList, hollowOutCellList } = res;
        let regionInnerListData = regionInnerList;
        if (params.regionId) {
            regionInnerListData = regionInnerList.filter((item) => item.cellType === 2);
            if (!regionInnerListData.length) {
                regionInnerListData = regionInnerList;
            }
        }
        const innerBaseStations = formatBaseStations(regionInnerListData, isMulti ? 'noAdd' : 'added');
        const outBaseStations = formatBaseStations(regionOutList, 'noAdd');
        const hollowOutCellStations = formatBaseStations(hollowOutCellList || [], 'noAdd');
        const baseStations = [...innerBaseStations, ...outBaseStations, ...hollowOutCellStations];
        return {
            innerBaseStations,
            outBaseStations,
            baseStations,
        };
    }
    static clearAll(isClearEvent = false) {
        if (isClearEvent) {
            const g = Circular.g;
            g.event.removeClick(Circular.holeLayer);
            g.layerList.areaEdit.onEditFinish.removeEvent('Circular');
        }
        Circular.holeLayer.removeAll();
    }
    static areaEditFinish(data) {
        const { g, compInstance } = Circular;
        const editPoints = g.math.three2world(g, data.points);
        data.name = '$123';
        g.layerList.areaEdit.removeByName('$123');

        compInstance.layerObj.drawHole(editPoints);
        compInstance.setBasePoints(true);
    }
    static initCircular(circle, i) {
        const { g, compInstance } = Circular;
        const name = `circular-${i}`;
        const { centerLatitude, centerLongitude, radius } = circle;
        g.layerList.圈选.create({
            name,
            circleColor: 0x0085F9,
            circleOpacity: 0.5,
            circleFrame: true,
            circleFrameColor: 0x1A7CFF,
            cirCleShowClose: true,
            circleShowRadius: !compInstance.isJustShowLayer,
            radius,
            startPoint: { lat: centerLatitude, lng: centerLongitude },
        });
    }
    drawHole(editPoints) {
        const g = Circular.g;
        const { color, maxH } = this.hole;
        const points = editPoints.map((item) => [item.lng, item.lat]);
        const holePoints = g.math.lsRegular(points) || [];

        const data = [
            {
                ls: holePoints,
                layers: [{ maxH, color }],
            },
        ];
        Object.assign(data, {
            needFrame: true,
            frameColor: 0x666666,
        });
        const holePlane = g.meshList.plane.create(data);
        const holeObjs = Object.assign({}, this.hole, {
            points: editPoints,
            i: this.i,
            index: this.hole.plane.length,//一个区域多个空洞标识
        });
        holePlane.operateType = holeObjs;
        this.hole.plane.push(holePlane);
        Circular.holeLayer.add(holePlane);
        g.gis.needUpdate = true;
    }
    drawRadiate(circle) {
        const g = Circular.g;
        const { radius, centerLatitude, centerLongitude } = circle;
        const name = `circular-radiate-${this.i}`;
        const mesh = g.layerList.圈选.create({
            name,
            circleColor: 0xe59f00,
            circleOpacity: 0.2,
            circleFrame: true,
            circleFrameColor: 0xF39003,
            circleShowRadius: false,
            radius,
            startPoint: { lat: centerLatitude, lng: centerLongitude },
            ht: 0,
        });
        this.radiate.plane = mesh;
    }
    async setRadiateInfo() {
        const { g, compInstance, getBaseStations } = Circular;
        const { radiate, region, distance } = this;
        const { baseStations } = await getBaseStations(region.circle, distance);
        addLastStatus(this.baseStations, baseStations);
        this.baseStations = baseStations;
        radiate.circle = {
            ...region.circle,
            radius: region.circle.radius + distance,
        };
        radiate.plane && g.layerList['圈选'].removeByName(radiate.plane.name);
        this.drawRadiate(radiate.circle);
        compInstance.setBasePoints();
    }
    updateRadiation(distance) {
        if (distance && this.distance !== distance) {
            this.distance = distance;
            this.setRadiateInfo();
        }
    }
    async selectMenu(prop) {
        const { g, compInstance } = Circular;
        if (prop === 'delCircular') {
            const { region, hole, radiate } = this;
            g.layerList['圈选'].remove(region.plane);
            hole.plane.forEach((item) => {
                Circular.holeLayer.remove(item);
            });
            Circular.currentClickHole = null;
            radiate.plane && g.layerList['圈选'].removeByName(radiate.plane.name);
            compInstance.planeObjs[this.i] = null;
            compInstance.layerObj = {};
            // compInstance.setAllBaseStations();
            // compInstance.setBasePoints();
        }
    }
}