.content-bg {
    background: var(--normal-bg);
    padding: 16px;
}
.content-first {
    .search-bar-first {
        margin-left: auto;
        display: flex;
        /deep/.el-form-item {
            margin-bottom: 0;
        }
        /deep/.el-form-item__label {
            color: #ffffffa6;
            font-weight: 400;
        }
    }
    &-wrapper {
        display: flex;
        gap: 16px;
    }
    &-left {
        width: 50%;
        height: 100%;
        display: flex;
        flex-direction: column;
        padding: 16px;
        border-radius: 2px;
        background: var(--section-card-bg);
        border: 1px solid var(--section-card-border);
    }
    &-right {
        width: 50%;
        height: 100%;
        display: flex;
        flex-direction: column;
        padding: 16px;
        border-radius: 2px;
        background: var(--section-card-bg);
        border: 1px solid var(--section-card-border);
    }
    &-full {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        padding: 16px;
        border-radius: 2px;
        background: var(--section-card-bg);
        border: 1px solid var(--section-card-border);
    }
}
.section-title-two {
    position: relative;
    margin-bottom: 9px;
    &::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 1px;
        background: linear-gradient(
            to right,
            #00ffff 0px,
            #00ffff 10px,
            rgba(0, 149, 255, 0.5) 10px,
            rgba(0, 149, 255, 0.5) calc(100% - 10px),
            #00ffff calc(100% - 10px),
            #00ffff 100%
        );
        bottom: -8px;
        left: 0;
    }
}
