<template>
    <div class="event-sub mtex-shandong-devops-dark-theme">
        <comNavTitle :navList="headerNavList"></comNavTitle>
        <el-container class="main" v-loading="creatLoading">
            <el-aside width="240px">
                <cornerCard class="aside-box">
                    <el-menu :default-active="activeNav" class="el-menu-vertical border-radius4">
                        <el-menu-item
                            v-for="(item, index) in navList"
                            :key="index"
                            :index="item.name"
                            @click="goStep(item)"
                        >
                            <template slot="title">
                                <i v-if="!item.isSvg" :class="item.icon"></i>
                                <img
                                    class="menu-img"
                                    v-else-if="activeNav === item.name"
                                    :src="item.iconA"
                                    alt=""
                                />
                                <img class="menu-img" v-else :src="item.icon" alt="" />
                                <span>{{ item.name }}</span>
                            </template>
                        </el-menu-item>
                    </el-menu>
                </cornerCard>
            </el-aside>
            <cornerCard class="main-box">
                <div class="create-box custom-scrollbar" v-show="isCreated">
                    <div class="step" ref="step1">
                        <header class="step-title">步骤一：基本信息</header>
                        <div class="step-content">
                            <div class="step-content-box" style="margin-bottom: 16px">
                                <div class="item">
                                    <label class="item-label is-required">任务名称：</label>
                                    <div class="item-input">
                                        <el-input
                                            size="small"
                                            maxlength="50"
                                            show-word-limit
                                            v-model="formData.taskName"
                                            placeholder="请输入任务名称"
                                        ></el-input>
                                    </div>
                                </div>
                                <div class="item">
                                    <label class="item-label is-required">生效时间：</label>
                                    <div class="item-input">
                                        <el-date-picker
                                            class="daterange-layout"
                                            size="small"
                                            v-model="formData.time"
                                            type="datetimerange"
                                            value-format="yyyy-MM-dd HH:mm:ss"
                                            range-separator="至"
                                            start-placeholder="开始日期"
                                            end-placeholder="结束日期"
                                            popper-class="date-picker-dark popover-dark"
                                            :default-time="['00:00:00', '23:59:59']"
                                        >
                                        </el-date-picker>
                                    </div>
                                </div>
                            </div>
                            <div class="step-content-box">
                                <div class="item" style="height: auto">
                                    <label
                                        class="item-label no-required"
                                        style="float: left; margin-right: 4px"
                                        >任务备注：</label
                                    >
                                    <div class="item-input">
                                        <el-input
                                            type="textarea"
                                            maxlength="200"
                                            show-word-limit
                                            :autosize="{
                                                minRows: 2,
                                                maxRows: 6
                                            }"
                                            v-model="formData.comment"
                                            placeholder="请输入任务备注"
                                        ></el-input>
                                    </div>
                                </div>
                                <div class="item">
                                    <label class="item-label is-required">常驻类型：</label>
                                    <div class="item-input">
                                        <el-select
                                            size="small"
                                            v-model="formData.residentType"
                                            placeholder="请选择"
                                            popper-class="select-dropdown-dark popover-dark"
                                        >
                                            <el-option
                                                v-for="item in residentType"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            >
                                            </el-option>
                                        </el-select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="step" ref="step2">
                        <header class="step-title">
                            步骤二：选择区域
                            <span class="more" @click="goMoreArea"
                                >更多区域 <i class="el-icon-arrow-right"></i>
                            </span>
                        </header>
                        <div class="step-content">
                            <div class="step-content-box">
                                <div class="item area-x">
                                    <label class="item-label no-required">区域类型：</label>
                                    <div class="item-input">
                                        <el-select
                                            size="small"
                                            v-model="formData.areas.areaType"
                                            placeholder="请选择"
                                            popper-class="select-dropdown-dark popover-dark"
                                        >
                                            <el-option
                                                v-for="item in areaTypeOps"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            >
                                            </el-option>
                                        </el-select>
                                    </div>
                                </div>
                            </div>
                            <transfer-table
                                v-show="formData.areas.areaType === 1"
                                ref="transferTable"
                                :typeOption="typeOption"
                                @areaChange="areaChange"
                                @changSelectList="changSelectList"
                            ></transfer-table>
                        </div>
                    </div>
                    <div class="step" ref="step3" v-if="false">
                        <header class="step-title">步骤三：选择人群</header>
                        <div class="step-content">
                            <div class="step-content-box">
                                <div class="item">
                                    <label class="item-label no-required">人群对象：</label>
                                    <div class="item-input">
                                        <el-cascader
                                            disabled
                                            class="people"
                                            size="small"
                                            placeholder="全部用户"
                                            v-model="people"
                                            :options="peopleOps"
                                            :props="peopleProps"
                                            :show-all-levels="false"
                                            collapse-tags
                                            clearable
                                        ></el-cascader>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="step" ref="step5">
                        <header class="step-title">
                            步骤三：选择事件任务
                            <i class="infor">（必须选择一个事件） </i>
                        </header>
                        <div class="step-content">
                            <event ref="event"></event>
                        </div>
                    </div>
                    <div class="footer">
                        <el-button
                            class="submit-btn"
                            type="primary"
                            size="small"
                            @click="createTask"
                            >创建</el-button
                        >
                    </div>
                </div>
                <div class="output-box" v-show="!isCreated">
                    <output-com></output-com>
                </div>
            </cornerCard>
        </el-container>
    </div>
</template>
<script>
import event from './components/event.vue';
import comNavTitle from '_com/comNavTitle.vue';
import cornerCard from '_com/cornerCard.vue';
import transferTable from './components/transferTable.vue';
import outputCom from './components/outputCom.vue';
import commonMixins from '@/script/mixins/commonMixins.js';

const requiredOps = [
    { name: 'taskName', isrequired: true, mes: '任务名称必填', ref: 'step1' },
    {
        name: 'startTime',
        isrequired: true,
        mes: '生效开始时间必填',
        ref: 'step1'
    },
    {
        name: 'endTime',
        isrequired: true,
        mes: '生效结束时间必填',
        ref: 'step1'
    }
];
export default {
    name: 'eventSub',
    mixins: [commonMixins],
    components: {
        comNavTitle,
        cornerCard,
        transferTable,
        event,
        outputCom
    },
    props: {
        outsideParam: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            headerNavList: this.outsideParam,
            hasSelectList: [],
            creatLoading: false,
            userInfor: {},
            areaTypeList: {
                1: '自定义区域',
                2: '省份',
                3: '地市',
                4: '区县'
            },
            activeNav: '基本信息',
            navList: [
                {
                    name: '基本信息',
                    icon: 'el-icon-tickets',
                    id: 'normal',
                    ref: 'step1'
                },
                {
                    name: '选择区域',
                    icon: 'el-icon-map-location',
                    id: 'area',
                    ref: 'step2'
                },
                // {
                //     name: '选择人群',
                //     isSvg: true,
                //     icon: people,
                //     iconA: peopleA,
                //     id: 'suer2',
                //     ref: 'step3',
                // },

                {
                    name: '选择事件任务',
                    icon: 'el-icon-document-checked',
                    id: 'event',
                    ref: 'step5'
                }
                // {
                //     name: '输出查看',
                //     icon: 'el-icon-view',
                //     id: 'event',
                //     ref: 'step6',
                // },
            ],
            formData: {
                startTime: '',
                endTime: '',
                time: [],
                taskName: '',
                comment: '',

                userScope: {
                    userType: 1
                    // userDetail:[],
                    // interceptDetail:[],
                },
                areas: {
                    areaType: 1,
                    areaIds: []
                },
                events: [],
                serviceInfo: {
                    portraitParam: '', //用户画像表达式
                    portraitObject: {} //用于回显
                },
                province: '',
                city: '',
                district: '',
                residentType: 0
            },
            areaTypeOps: [{ label: '自定义区域 (至少选一个)', value: 1 }],
            residentType: [
                { label: '全部 ', value: 0 },
                { label: '去工作 ', value: 1 },
                { label: '去居住 ', value: 2 },
                { label: '去常驻（工作与居住均去） ', value: 3 },
                { label: '仅工作 ', value: 4 },
                { label: '仅居住 ', value: 5 },
                { label: '工作且居住 ', value: 6 }
            ],
            typeOption: {},
            people: [],
            peopleProps: { multiple: true },
            peopleOps: [
                {
                    value: 1,
                    label: '学校',
                    children: [
                        {
                            value: 2,
                            label: '学生'
                        },
                        {
                            value: 7,
                            label: '保洁'
                        },
                        {
                            value: 12,
                            label: '老师'
                        }
                    ]
                }
            ],
            isCreated: true
        };
    },
    mounted() {
        this.isCreated = true;
        // this.changeTop('step4');
    },

    methods: {
        changSelectList(val) {
            this.hasSelectList = val;
        },
        goMoreArea() {
            this.$message('功能正在开发中');
        },
        goStep(item) {
            this.activeNav = item.name;
            if (item.ref && item.ref === 'step6') {
                this.isCreated = false;
                return;
            }
            this.isCreated = true;
            this.changeTop(item.ref);
        },
        areaChange() {},
        changeTop(ref) {
            this.$refs[ref].scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        },
        showErrorMessage(mes = {}) {
            let options = {
                title: mes.title,
                content: mes.content
            };
            this.$popupMessageWindow(options);
        },
        // 校验
        handlerReq(params) {
            if (!params) {
                params = this.this.formData;
            }
            let flag = true,
                errList = [],
                stepList = [];
            requiredOps.forEach((item) => {
                if (item.isrequired && !params[item.name]) {
                    errList.push(item.mes);
                    stepList.push(item.ref);
                }
            });
            // 其他情况
            if (!params.areas.areaIds.length) {
                let err = {
                    1: '自定义区域未选择',
                    2: '省份未选择',
                    3: '地市未选择',
                    4: '区县未选择'
                };
                errList.push(err[params.areas.areaType]);
                stepList.push('step2');
            }
            if (params.serviceInfo.status) {
                errList.push(params.serviceInfo.infor);
                stepList.push('step4');
            }
            if (!params.events.length) {
                errList.push('事件未选择');
                stepList.push('step5');
            }
            if (errList.length) {
                flag = false;
                this.showErrorMessage({
                    title: '消息提示',
                    content: '创建失败 \n' + errList.join('\n')
                });
                this.changeTop(stepList[0]);
            }
            return flag;
        },
        showSuccessMessage() {
            this.$popupMessageWindow({
                title: '消息提示',
                content: '创建成功',
                submitBtnText: '确定',
                submitCallback: this.goListPage.bind(this)
            });
        },
        // 处理区域数据
        handleAreaList(params) {
            if (params.areas.areaType === 1) {
                params.areas.areaIds = this.$refs.transferTable.getCheckedTreeList();
            } else if (params.areas.areaType === 2 && this.formData.province) {
                params.areas.areaIds = [this.formData.province];
            } else if (params.areas.areaType === 3 && this.formData.city) {
                params.areas.areaIds = [this.formData.city];
            } else if (params.areas.areaType === 4 && this.formData.district) {
                params.areas.areaIds = [this.formData.district];
            }
        },

        handleParmas() {
            this.formData.events = this.$refs.event.getCheckedList();
            const params = {
                startTime: '',
                endTime: '',
                taskName: '',
                comment: '',
                userScope: {
                    userType: 1
                    // userDetail:[],
                    // interceptDetail:[],
                },
                areas: {
                    areaType: 1,
                    areaIds: []
                },
                events: [],
                serviceInfo: {
                    portraitParam: '', //用户画像表达式
                    portraitObject: {} //用于回显
                },
                residentType: 0
            };
            Object.keys(params).forEach((key) => {
                params[key] = this.formData[key];
            });

            if (this.formData.time.length !== 0) {
                params.startTime = this.formData.time[0];
                params.endTime = this.formData.time[1];
            }

            this.handleAreaList(params);

            return params;
        },

        // 创建任务
        createTask() {
            const params = this.handleParmas();
            if (!this.handlerReq(params)) {
                return;
            }
            this.creatLoading = true;
            this.getPost(
                'post',
                'addEvent',
                params,
                '事件任务添加',
                (res) => {
                    this.creatLoading = false;
                    this.showSuccessMessage();
                },
                () => {
                    this.creatLoading = false;
                }
            );
        },
        goListPage() {
            this.$store.commit('openAppId', {
                appId: 'af37a711-5134-d6e5-ed48-7f878a00d90d'
            });
        },
        initData() {
            this.formData = {
                startTime: '',
                endTime: '',
                time: [],
                taskName: '',
                comment: '',
                userScope: {
                    userType: 1
                },
                areas: {
                    areaType: 1,
                    areaIds: []
                },
                events: [],
                serviceInfo: {
                    portraitParam: '', //用户画像表达式
                    portraitObject: {} //用于回显
                },
                province: '',
                city: '',
                district: ''
            };
            this.changeTop('step1');
            this.activeNav = '基本信息';
            this.$refs.transferTable.initData();
            this.$refs.event.initData();
        }
    }
};
</script>
<style lang="less" scoped>
@import url('../../../style/custom.less');

.event-sub {
    width: 100%;
    height: 100%;

    .main {
        padding: 16px;
        height: calc(100% - 64px);

        .aside-box {
            z-index: 1;
            background: rgba(0, 42, 92, 0.9);
            border: 1px solid rgba(0, 149, 255, 0.5);
            height: 100%;

            .el-menu-vertical {
                background: transparent;
                border-right: none;
                border-radius: 4px;
                padding: 20px 9px;

                .menu-img {
                    display: inline-block;
                    width: 18px;
                    height: 18px;
                    margin-left: 3px;
                    margin-right: 7px;
                }

                .el-menu-item {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 14px;
                    color: #c9dfff;
                    padding: 0 16px;

                    i {
                        color: #c9dfff;
                    }

                    &.is-active {
                        color: #fff;
                        background-color: rgba(0, 149, 255, 0.6);

                        i {
                            color: #fff;
                        }
                    }
                    &:hover {
                        background-color: rgba(0, 149, 255, 0.2);
                    }
                }
            }
        }

        .main-box {
            margin-left: 16px;
            padding: 0;
            width: 0;
            flex: 1;
            height: 100%;
            background: var(--normal-bg);

            .create-box {
                width: 100%;
                height: 100%;
                padding: 16px;
                overflow-x: hidden;
                overflow-y: auto;
                .step {
                    padding: 16px;
                    position: relative;
                    margin-bottom: 16px;
                    &::after {
                        content: '';
                        position: absolute;
                        width: 100%;
                        height: 1px;
                        background: linear-gradient(
                            to right,
                            #00ffff 0px,
                            #00ffff 10px,
                            rgba(0, 149, 255, 0.5) 10px,
                            rgba(0, 149, 255, 0.5) calc(100% - 10px),
                            #00ffff calc(100% - 10px),
                            #00ffff 100%
                        );
                        bottom: 0px;
                        left: 16px;
                    }

                    &-title {
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 500;
                        font-size: 18px;
                        color: #ffffff;
                        line-height: 24px;
                        margin-bottom: 16px;

                        .infor {
                            display: inline-block;
                            font-style: normal;
                            font-size: 14px;
                            color: var(--normal-text-secondary);
                        }

                        .more {
                            float: right;
                            color: #409eff;
                            cursor: pointer;
                            font-size: 14px;
                        }
                    }

                    &-content {
                        border-bottom: 1px solid var(--normal-border);
                        padding-bottom: 16px;

                        &-box {
                            display: flex;
                            flex-direction: row;
                            align-items: flex-start;
                            justify-content: flex-start;

                            .item {
                                width: 50%;
                                height: 32px;
                                line-height: 32px;

                                &-label {
                                    font-family: PingFangSC, PingFang SC;
                                    font-weight: 400;
                                    font-size: 14px;
                                    color: rgba(255, 255, 255, 0.65);
                                    line-height: 16px;
                                    margin-bottom: 0px;

                                    &.is-required:before {
                                        content: '*';
                                        color: #f56c6c;
                                        margin-right: 4px;
                                    }

                                    &.no-required:before {
                                        content: '';
                                        margin-right: 10px;
                                    }
                                }

                                &-input {
                                    display: inline-block;
                                    min-width: 380px;

                                    .people {
                                        /deep/ .el-input__inner {
                                            height: 32px !important;
                                        }
                                    }

                                    .daterange-layout {
                                        .el-range-separator {
                                            width: 13%;
                                        }
                                    }
                                }

                                &.area-x {
                                    width: auto;
                                    margin-right: 60px;

                                    .item-input {
                                        min-width: 0px;
                                    }
                                }
                            }
                        }
                    }
                }
                .footer {
                    .submit-btn {
                        margin-left: 20px;
                        width: 120px;
                        text-align: center;
                        font-size: 16px;
                    }
                }
                /deep/.el-input__inner,
                /deep/.el-textarea__inner {
                    background: rgba(4, 52, 106, 0.6) !important;
                    border-radius: 2px !important;
                    border: 1px solid rgba(18, 139, 207, 0.6) !important;
                }
            }

            .output-box {
                width: 100%;
                height: 100%;
                overflow: hidden;
            }
        }
    }
}
</style>
