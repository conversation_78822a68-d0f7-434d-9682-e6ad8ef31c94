import axios from 'axios';
import api from '@/script/api/module/regionResource';
import { decodeResultUser, tryEncodeParam } from './resUtils';
window.cancelTokenCollect = {};
const removeCancelToken = (urlName, source) => {
    const targetIndex = window.cancelTokenCollect[urlName].findIndex((item) => item === source);
    window.cancelTokenCollect[urlName].splice(targetIndex, 1);
};
window.cancelRequest = (urlName) => {
    window.cancelTokenCollect[urlName] &&
        window.cancelTokenCollect[urlName].forEach((item) => {
            item.cancel('中断请求：' + urlName);
        });
};
const server = axios.create({
    timeout: 180000,
    baseURL: '/'
});
const encryption = true; // todo

server.interceptors.request.use(
    (config) => {
        if (config.url === 'auth/micro/verifyUserRolesByRoleName') {
            config.headers.common['token'] = localStorage.getItem('token');
        }
        if (config.method.toUpperCase() === 'POST') {
            config.headers['content-type'] = 'application/json';
            config.data = JSON.stringify({ ...config.data });
        }
        return config;
    },
    (err) => {
        Promise.reject(err);
    }
);

server.interceptors.response.use(
    (res) => {
        if (res.status === 200) {
            //判断是否正确返回
            let newRes = res.data;
            if (encryption) {
                if (newRes && newRes.encodeResp) {
                    newRes = decodeResultUser(res.data);
                }
            }
            // 判断是否为空
            if (newRes.success || newRes.returnCode === '000002') {
                return newRes.returnMsg;
            }
            //判断是否正确返回
            else if (newRes.success || newRes.returnCode === '000001') {
                return newRes.data;
            } else if (
                res.headers &&
                res.headers['content-type'] === 'application/vnd.ms-excel;charset=utf-8'
            ) {
                return res;
            }
            // 金库审批特别处理(成功)
            if (newRes.success || newRes.returnCode === '1') {
                return newRes.data;
            }
            return Promise.reject(newRes.msg || newRes.returnMsg);
        }
        return Promise.reject(newRes.msg || newRes.returnMsg);
    },
    (err) => {
        // 金库审批特别处理(触发)
        let newErr = null;
        if (encryption) {
            newErr = decodeResultUser(err.response.data);
        }
        if (err.response.status === 401 && newErr.returnCode === '400000') {
            return Promise.reject(newErr);
        }
        return Promise.reject(err);
    }
);
const request = (method, urlName, data = {}, downloadProgressFn, headers = {}) => {
    if (!window.cancelTokenCollect[urlName]) {
        window.cancelTokenCollect[urlName] = [];
    }
    if (urlName !== 'getRoles') {
        if (data instanceof FormData) {
            data.append('token', localStorage.getItem('token'));
        } else {
            data.token = localStorage.getItem('token');
        }
    }
    const CancelToken = axios.CancelToken;
    const source = CancelToken.source();
    window.cancelTokenCollect[urlName].push(source);
    // todo
    if (encryption) {
        data = tryEncodeParam(data);
    }
    if (method.toUpperCase() === 'GET') {
        return new Promise(function (resolve, reject) {
            server
                .get(api[urlName] || urlName, {
                    params: data,
                    cancelToken: source.token,
                    onDownloadProgress: downloadProgressFn
                })
                .then((res) => {
                    resolve(res);
                })
                .catch((err) => {
                    if (axios.isCancel(err)) {
                        console.log('Request canceled', err.message);
                    } else {
                        reject(err);
                    }
                });
        }).finally(() => {
            removeCancelToken(urlName, source);
        });
    } else if (method.toUpperCase() === 'POST') {
        return new Promise(function (resolve, reject) {
            server
                .post(api[urlName] || urlName, data, {
                    cancelToken: source.token,
                    onDownloadProgress: downloadProgressFn,
                    headers: {
                        ...headers
                    }
                })
                .then((res) => {
                    resolve(res);
                })
                .catch((err) => {
                    if (axios.isCancel(err)) {
                        console.log('Request canceled', err.message);
                    } else {
                        reject(err);
                    }
                });
        }).finally(() => {
            removeCancelToken(urlName, source);
        });
    }
};

const getFileRequst = (method, urlName, data = {}, config = {}) => {
    data.token = localStorage.getItem('token');
    // todo
    if (encryption) {
        data = tryEncodeParam(data);
    }
    return new Promise(function (resolve, reject) {
        server({
            method: method.toUpperCase(),
            url: api[urlName] || urlName,
            data: data,
            ...config
        })
            .then((res) => {
                resolve(res);
            })
            .catch((err) => {
                reject(err);
            });
    });
};
export { request, getFileRequst };
