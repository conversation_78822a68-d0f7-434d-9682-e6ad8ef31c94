<template>
    <div class="search-place panel">
        <el-input
            placeholder="请输入查询内容"
            v-model="addressInput"
            class="input-with-select"
            clearable
            @keyup.enter.native="poiSearch()"
        >
            <el-button
                class="search-btn"
                slot="append"
                icon="el-icon-search"
                @click="poiSearch()"
            ></el-button>
        </el-input>
        <el-card
            v-if="poiResultList.length"
            class="search-result"
            :class="{ 'closed-panel': isHidePanel }"
            shadow="always"
        >
            <ul class="tips-list vertical-scroll">
                <li
                    class="tips"
                    :class="{ active: poiActiveIndex === index }"
                    v-for="(item, index) in poiResultList"
                    :key="index"
                    @click="clickPoiList(item, index)"
                >
                    <div class="poi-name">{{ index + 1 }}.&nbsp;{{ item.NAME }}</div>
                    <div class="address">{{ item.ADDRESS }}</div>
                </li>
            </ul>
            <div v-if="isSearch" class="fold-btn" @click="isHidePanel = !isHidePanel">
                <em class="el-icon-caret-left"></em>
            </div>
        </el-card>
        <el-empty
            v-else-if="isSearch && !poiResultList.length"
            description="暂无搜索结果"
        ></el-empty>
    </div>
</template>
<script>
import Axios from 'axios';
export default {
    name: 'search-place',
    props: {
        isShow: {
            type: Boolean,
            default: false,
        },
        city: {
            type: Array,
            default: () => [],
        },
        gisCenterMove: {
            type: Function,
            default: () => {},
        },
    },
    data() {
        return {
            isSearch: false,
            poiResultList: [],
            pointData: {},
            poiActiveIndex: '',
            oldMeshIndex: '',
            addressInput: '',
            currentPage: 1,
            pageSize: 20,
            isHidePanel: false,
        };
    },
    created() {},
    methods: {
        // 搜索
        clickPoiList(item, index) {
            this.pointData = item;
            this.poiActiveIndex = index;
            this.oldMeshIndex = index;
            this.gisCenterMove(this.pointData);
        },
        //POI查询
        poiSearch() {
            const city = (this.city || []).flat();
            if (!city.length) {
                this.$exmessage('请先选择地市');
                return;
            }
            if (!this.pointData) {
                this.$message.warning('请先输入关键词');
                return;
            }
            this.$popupLoading({
                show: true,
                message: '加载中...',
            });
            const AxiosUrl =
                this.getMayType() === 'default'
                    ? `${location.origin}/wzlocMapSearch`
                    : systemUtil.rootPath;
            Axios({
                url: `${AxiosUrl}/SearchWebProject/PoiSearch`,
                method: 'get',
                headers: {
                    key: 'ad6609c5afe3741b',
                    Version: '1.0.0',
                    ReqNo: '1111',
                    'content-Type': 'application/xml',
                },
                responseType: 'blob',
                params: {
                    data_type: 'POI',
                    query_type: 'TQUERY',
                    protocol: 'json',
                    city: city[1],
                    keywords: this.addressInput || '',
                    page_num: this.pageSize,
                    page: this.currentPage,
                    qii: true,
                    key: 'ad6609c5afe3741b',
                },
            })
                .then((res) => {
                    let reader = new FileReader();
                    reader.readAsText(res.data, 'GBK');
                    const that = this;
                    reader.onload = function () {
                        that.handleResData(reader.result);
                    };
                    this.$popupLoading({ show: false });
                })
                .catch((err) => {
                    this.poiResultList = [];
                    this.$popupLoading({ show: false });
                    let options = {
                        title: '消息提示',
                        content: '消息内容！',
                        detail: `详细内容：${err}`,
                    };
                    this.$popupMessageWindow(options);
                });
        },
        getMayType() {
            let hostName = window.location.hostname;
            // // 判断是本地、测试内网还是生产地址。
            if (hostName.indexOf('localhost') >= 0 || hostName.indexOf('192.168') >= 0) {
                return 'tx';
            }
            return 'default';
        },
        // 处理查询返回数据
        handleResData(data) {
            this.isSearch = true;
            const resData = JSON.parse(data);
            this.poiResultList = resData.poi || [];
        },
    },
};
</script>
<style lang="less" scoped>
.search-place {
    margin-bottom: 0;
    background: #069aff;
    font-size: 12px;
    line-height: 18px;
    /deep/ .el-input-group__append {
        background: #069aff;
        border-color: #069aff;
        color: #fff;
    }
    /deep/ .el-input__inner {
        border: none;
    }
    .search-btn {
        padding: 12px;
        /deep/ i {
            color: #fff;
            font-weight: 600;
            font-size: 20px;
        }
    }
    .fold-btn {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: -28px;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 35px;
        width: 20px;
        background-color: #409eff;
        cursor: pointer;

        em {
            color: #ffff;
            font-size: 20px;
            transition: transform 0.5s;
        }
    }
    .search-result {
        position: absolute;
        top: 45px;
        left: 0;
        background: #fff;
        z-index: 10;
        font-size: 12px;
        line-height: 18px;
        width: 260px;
        height: 400px;
        overflow: inherit;
        ::v-deep .el-card__body {
            padding: 0px;
            height: 100%;
        }
        .tips-list {
            height: 100%;
            overflow-y: auto;
        }
        .tips {
            border-top: 1px solid #eaeaea;
            padding: 10px;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            &:hover {
                background: #e6f4ff;
            }
            &.active {
                background: #d3e9fb;
            }
            .poi-name {
                float: left;
                max-width: 200px;
                height: 22px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                margin-top: 1px;
                font-size: 14px;
                font-weight: 600;
            }
        }
        &.closed-panel {
            transform: translateX(-270px);
            .fold-btn {
                em {
                    transform: rotate(180deg);
                }
            }
        }
    }
}
</style>
