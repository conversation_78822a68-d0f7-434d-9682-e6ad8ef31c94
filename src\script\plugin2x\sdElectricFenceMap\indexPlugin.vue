<template>
    <!-- 电子围栏分布热力图 -->
    <div class="electric-fence-map mtex-shandong-devops-dark-theme">
        <comNavTitle :navList="navList">
            <searchBar
                class="searchBar"
                ref="formList"
                :formCols="formCols"
                :form="form"
                :isTransparentBg="true"
            >
                <template #search>
                    <el-button type="primary" size="small" @click="search">查询</el-button>
                </template>
                <template #reset>
                    <el-button class="reset-btn" size="small" @click="reset">重置</el-button>
                </template>
            </searchBar>
        </comNavTitle>
        <mtv-gis
            class="gis"
            ref="eleGisMap"
            :totaloptions="gistotalOptions"
            @onLoad="gisOnLoad"
        ></mtv-gis>
        <!-- 总人数 -->
        <div class="map-person-total">
            <div class="total-title">总人数</div>
            <div class="total-count">{{ totalCount }}</div>
        </div>
        <!-- 刷新按钮 -->
        <div class="map-public-bg map-btn-refresh" @click="refresh">
            <div class="refresh-container">
                <div class="refresh-icon"></div>
                <div class="refresh-label">刷新</div>
            </div>
        </div>
        <!-- 工具栏 -->
        <div class="map-public-bg map-tool">
            <div
                v-for="item in toolList"
                :key="item.label"
                class="tool-item"
                :class="{ active: currentTool(item) }"
                :style="{
                    '--tool-icon': `url(${item.icon})`,
                    '--tool-icon-active': `url(${item.activeIcon})`
                }"
                @click="item.click(item)"
            >
                <div class="tool-icon"></div>
                <div class="tool-label">{{ item.label }}</div>
            </div>
        </div>
        <!-- 图例 -->
        <heat-map-legend class="map-legend" title="图例" :values="legendValues" />
    </div>
</template>

<script>
import searchBar from '_com/searchBar/searchBar.vue';
import comNavTitle from '_com/comNavTitle.vue';
import heatMapLegend from '_com/heatMapLegend.vue';
import commonMixins from '@/script/mixins/commonMixins.js';
const positionImg = require('../../../img/position.png');
import { gistotalOptions, getMayType, changeGisColor } from '@/script/constant/gis.js';

export default {
    components: {
        searchBar,
        comNavTitle,
        heatMapLegend
    },
    props: {
        outsideParam: {
            type: Array,
            default: () => []
        }
    },
    mixins: [commonMixins],
    mounted() {},
    data() {
        return {
            navList: this.outsideParam,
            formCols: [
                [
                    {
                        type: 'el-input',
                        prop: 'regionName',
                        label: '电子围栏：',
                        labelWidth: '85px',
                        attrs: {
                            placeholder: '电子围栏关键字',
                            clearable: true,
                            class: 'displayPass'
                        },
                        rules: [
                            // { required: true, message: '必填', trigger: 'submit' }
                        ],
                        span: 16,
                        isDisabled: false,
                        isShow: true
                    },
                    {
                        type: 'tool',
                        prop: 'tool',
                        span: 8,
                        isShow: true,
                        labelWidth: '15px'
                    }
                ]
            ],
            form: {
                // cityId: '',
                regionName: ''
            },
            toolList: [
                {
                    icon: require('../../../img/icon/circle-select.png'),
                    activeIcon: require('../../../img/icon/circle-select-active.png'),
                    label: '圈选',
                    click: (val) => {
                        this.toolClick(val);
                    },
                    isActive: true
                },
                {
                    icon: require('../../../img/icon/box-select.png'),
                    activeIcon: require('../../../img/icon/box-select-active.png'),
                    label: '框选',
                    click: (val) => {
                        this.toolClick(val);
                    },
                    isActive: true
                },
                {
                    icon: require('../../../img/icon/erase.png'),
                    activeIcon: require('../../../img/icon/erase-active.png'),
                    label: '擦除',
                    click: (val) => {
                        this.toolClick(val);
                    },
                    isActive: false
                }
            ],
            activeTool: '',
            thisGis: null,
            gistotalOptions: gistotalOptions,
            gisLoaded: false,
            planeLayer: null, //轮廓layer
            hotLayer: null, //热力图layer
            circleLayer: null, //圆形layer
            hotList: [],
            nowCircle: null,
            nowBox: null,
            totalCount: 0,
            legendValues: ['0', '1500', '3000', '4500+']
        };
    },
    methods: {
        currentTool(tool) {
            return this.activeTool === tool.label && tool.isActive;
        },
        toolClick(tool) {
            this.activeTool = tool.label;
            this.clearGisData();
            this.totalCount = 0;
            if (tool.label === '擦除') {
                this.clearGisData();
            } else if (tool.label === '圈选') {
                this.thisGis.layerList.boxSelectLayer.outMode();
                this.thisGis.layerList['圈选'].startMode();
            } else if (tool.label === '框选') {
                this.thisGis.layerList['圈选'].outMode();
                this.thisGis.layerList.boxSelectLayer.startMode();
            }
        },
        gisOnLoad() {
            const gis = (this.thisGis = this.$refs['eleGisMap'].getEntity());
            // 设置底图
            if (getMayType() === 'default') {
                gis.tileLayerList['底图图层'].visible = false;
                //重写getUrl方法
                gis.tileLayerList['高德底图'].getUrl = (x, y, z) => {
                    return `${location.origin}/MapUrl/api/appmaptile?x=${x}&y=${y}&z=${z}`;
                };
            } else {
                gis.tileLayerList['高德底图'] && (gis.tileLayerList['高德底图'].visible = false);
            }

            gis.tool.proportional.visible = true;

            changeGisColor(gis);

            gis.downLoad.onDownloadFinish.addOneEvent(() => {
                this.gisLoaded = true;
                // 热力图layer
                this.hotLayer = new gis.layer();
                this.hotLayer.visible = true;
                this.hotLayer.name = 'hotmap';
                this.hotLayer.renderOrder = true;
                this.hotLayer.renderOrderIndex = 2;
                gis.gis.scene.add(this.hotLayer.Group);
                // 轮廓layer
                this.planeLayer = new gis.layer();
                this.planeLayer.name = 'planeLayer';
                this.planeLayer.visible = true;
                gis.gis.scene.add(this.planeLayer);
                // 圆形layer
                this.circleLayer = new gis.layer();
                this.circleLayer.name = 'circleLayer';
                this.circleLayer.visible = true;
                gis.gis.scene.add(this.circleLayer);

                //添加事件高亮
                gis.event.addHover(
                    this.circleLayer,
                    (th, event) => {
                        gis.meshList.circle.click(th);
                    },
                    () => {
                        gis.meshList.circle.clearClick();
                    }
                );
                gis.event.addHover(
                    this.planeLayer,
                    (th, event) => {
                        gis.meshList.plane.click(th);
                    },
                    () => {
                        gis.meshList.plane.clearClick();
                    }
                );
            });
            gis.cameraControl.onNeedDownLoad.addEvent(() => {
                if (this.hotLayer) {
                    this.hotLayer.removeAll();
                }
                this.reRenderHotMap(this.hotList);
            });

            // 圈选
            gis.layerList['圈选'].onCircleChoice.addEvent((data) => {
                this.activeTool = '';
                this.handleSelectArea({
                    shapeType: 1, //1-圆形 2-多边形
                    circle: {
                        radius: data.radius * 1000,
                        centerLongitude: data.startPoint.lng,
                        centerLatitude: data.startPoint.lat
                    }
                });
                this.nowCircle = data;
            });
            // 框选
            gis.layerList.boxSelectLayer.onDoubleClick.addEvent((data) => {
                this.activeTool = '';
                if (data && data.point && data.point.length > 0) {
                    const points = data.point.map((point) => ({
                        longitude: point.lng,
                        latitude: point.lat
                    }));

                    // 构建regionCoors字符串
                    const regionCoors = points
                        .map((point) => `${point.longitude},${point.latitude}`)
                        .join(';');

                    this.handleSelectArea({
                        shapeType: 2, //1-圆形 2-多边形
                        regionCoors: regionCoors
                    });
                }
                this.nowBox = data;
            });
        },
        search() {
            if (!this.gisLoaded) {
                this.$message('地图尚未初始化完成，请稍候重试');
                return;
            }
            if (!this.form.regionName) {
                this.$message.error('请先输入电子围栏关键字！');
                return;
            }
            this.clearGisData();
            this.$exloading1x();
            this.getPost(
                'post',
                'regionHotMap',
                {
                    ...this.form
                },
                '地市区域热力查询',
                (data) => {
                    this.hotList = [];
                    this.totalCount = 0;
                    data.cityRegionHeatList.forEach((item) => {
                        this.totalCount += item.totalUserCnt;
                        this.hotList = this.hotList.concat(item.heatList);
                        this.reRenderHotMap(item.heatList);
                        // 原型区域
                        if (item.shapeType === 1) {
                            const circlePosition = {
                                lat: item.circle.centerLatitude,
                                lng: item.circle.centerLongitude
                            };
                            this.drawCircle([
                                {
                                    ...circlePosition,
                                    radius: item.circle.radius,
                                    color: 0x0085f9,
                                    angle: 360,
                                    dir: 40
                                }
                            ]);
                            this.drawingText(circlePosition, item.regionName);
                        } else {
                            const GIS = this.$refs['eleGisMap'].getEntity();
                            let regionCoors = item.regionCoors.split(';');
                            regionCoors = regionCoors.map((val) => {
                                const list = val.split(',');
                                return list.map((num) => Number(num));
                            });
                            let regularData = GIS.math.lsRegular(regionCoors);
                            const position = GIS.math.getCenterOfGravityPoint(regularData);
                            const centerPoint = GIS.math.three2world({
                                x: position.x,
                                y: 0,
                                z: position.y
                            });
                            this.handlerPlane([regionCoors]);
                            this.drawingText(centerPoint, item.regionName);
                        }
                    });
                    this.$exloaded1x();
                    this.setFitView(this.hotList, this.hotList[0]);
                }
            );
        },
        reset() {
            this.form.cityId = '';
            this.activeTool = '';
            this.nowCircle = null;
            this.nowBox = null;
            if (this.gisLoaded) {
                this.clearGisData();
            }
        },
        clearGisData(isClearAll = true) {
            this.hotList = [];
            if (this.hotLayer) {
                this.hotLayer.removeAll();
            }
            if (this.planeLayer) {
                this.planeLayer.removeAll();
            }
            if (this.circleLayer) {
                this.circleLayer.removeAll();
            }
            if (isClearAll) {
                this.thisGis.layerList['圈选'].removeAll();
                this.thisGis.layerList.boxSelectLayer.removeAll();
            }
            this.thisGis.layerList.divLayer.removeAll();
        },
        getRadius() {
            const gis = this.$refs['eleGisMap'].getEntity();
            return Math.pow((38 - gis.getGisPosition().zoom) * 2, 2);
        },
        reRenderHotMap(data) {
            const radius = this.getRadius();
            const list = data.map((item) => {
                let opacity = item.personNum / 4500;
                return {
                    lat: Number(item.latitude),
                    lng: Number(item.longitude),
                    opacity: opacity > 1 ? 1 : opacity,
                    radius
                };
            });
            if (list.length) {
                this.renderHotMap(list);
            }
        },
        // 绘制热力图
        renderHotMap(list) {
            const gis = this.$refs['eleGisMap'].getEntity();
            let tuli = {
                legends: {
                    0.25: 'rgb(9,36,128)',
                    0.55: 'rgb(0,255,0)',
                    0.65: 'rgb(170,255,50)',
                    0.7: 'rgb(200,255,50)',
                    0.85: 'rgb(255,200,0)',
                    0.9: 'rgb(255,160,0)',
                    0.94: 'rgb(255,100,0)',
                    0.98: 'rgb(255,40,0)',
                    1.0: 'rgb(255,0,0)'
                },
                opacity: 1, //热力图最高不透明度，比如这里配置的红色，对应的渲染不透明度为1，本属性可进一步控制生成的图片的不透明度
                opacityHold: 1, //热力图图例不透明度超过该值的都设置为上面opacity的值，默认与opacity相同
                minOpacity: 0 //最小不透明度，即完全透明，代表没有数值的区域的不透明度，默认值为0，设置为其他>0的值则可为空白区域填充像素
            };
            //生成热力图材质
            let mat = gis.meshList.hotMap.getMaterial(tuli);
            const { leftDown, rightTop } = gis.math.getViewPointsLatLng();
            list.maxLat = rightTop.lat;
            list.minLat = leftDown.lat;
            list.minLng = leftDown.lng;
            list.maxLng = rightTop.lng;
            //传入数据材质生成热力图模型
            let mesh = gis.meshList.hotMap.create(list, mat);
            mesh.name = 'hotmap';
            //创建图层装载热力图模型
            this.hotLayer.add(mesh);
            //更新GIS
            gis.gis.needUpdate = true;
        },
        // 图层绘制
        handlerPlane(features) {
            let GIS = this.$refs['eleGisMap'].getEntity();
            if (!features) {
                return;
            }
            const data = [];
            for (let i = 0, latlngData; i < features.length; i++) {
                latlngData = features[i];
                //将数据转换为2D序列点数组
                let regularData = GIS.math.lsRegular(latlngData);
                let obj = {
                    ls: regularData,
                    ht: 0 * GIS.HM,
                    layers: [{ maxH: 0, color: 0x0085f9 }],
                    points: []
                };
                latlngData.forEach((list) => {
                    obj.points.push({ lat: list[1], lng: list[0] });
                });
                data.push(obj);
            }
            data.needFrame = true;
            data.frameWidth = 4;
            data.frameColor = '#33AEFF';
            GIS.meshList.plane.opacity = 0.1;
            let plane = GIS.meshList.plane.create(data);
            this.planeLayer.add(plane);
            GIS.gis.needUpdate = true;
        },
        // 绘制圆形
        drawCircle(data) {
            let GIS = this.$refs['eleGisMap'].getEntity();
            data.needFrame = true;
            data.frameWidth = 4;
            data.frameColor = '#33AEFF';
            GIS.meshList.circle.opacity = 0.1;
            //创建圆弧面
            let mesh = GIS.meshList.circle.create(data);
            this.circleLayer.add(mesh);
            GIS.gis.needUpdate = true;
        },
        drawingText(centerPoint, name) {
            const GIS = this.$refs['eleGisMap'].getEntity();
            const dom = {
                dom: `<div class="electric-box">
                        <div class="electric-box-left">
                            <img src="${positionImg}" alt="" />
                        </div>
                        <div class="electric-box-right">${name}</div>
                        <div class="triangle"></div>
                     </div>`,
                point: {
                    lat: centerPoint.lat,
                    lng: centerPoint.lng
                }
            };
            GIS.layerList.divLayer.addDiv(dom);
        },
        setFitView(datas, start) {
            const GIS = this.$refs.eleGisMap.getEntity();
            //计算数据的最大最小经纬度
            let maxLat = start.latitude,
                minLat = start.latitude,
                maxLng = start.longitude,
                minLng = start.longitude;
            for (let i = 0; i < datas.length; i++) {
                let p = datas[i];
                if (p.latitude > maxLat) {
                    maxLat = p.latitude;
                } else if (p.latitude < minLat) {
                    minLat = p.latitude;
                }
                if (p.longitude > maxLng) {
                    maxLng = p.longitude;
                } else if (p.longitude < minLng) {
                    minLng = p.longitude;
                }
            }
            //得到最大最小经纬度
            let points = [];
            points.push(
                { lat: maxLat, lng: maxLng },
                { lat: minLat, lng: maxLng },
                { lat: maxLat, lng: minLng },
                { lat: minLat, lng: minLng }
            );

            //计时2s后执行
            setTimeout(() => {
                //根据最大最小值计算GIS渲染的半径以及中心点；
                let easy = GIS.cameraControl.computeZoomByPoints(points, 1.0);

                //返回的数据中已经存在radius（半径）以及target（中心点），因为缩放需要的字段名称是targetPoint，这里赋值一下；
                easy.targetPoint = easy.target;

                //根据半径中心点进行缩放（可能是放大，可能是缩小，可用于制作钻取效果）
                GIS.cameraControl.gradualChangeRadius(easy);
            }, 2000);
        },
        refresh() {
            this.activeTool = '';
            console.log(this.nowCircle, this.nowBox);

            // 如果有圈选区域，重新请求接口
            if (this.nowCircle) {
                this.handleSelectArea(
                    {
                        shapeType: 1, //1-圆形 2-多边形
                        circle: {
                            radius: this.nowCircle.radius * 1000,
                            centerLongitude: this.nowCircle.startPoint.lng,
                            centerLatitude: this.nowCircle.startPoint.lat
                        }
                    },
                    true
                );
                return;
            }

            // 如果有框选区域，重新请求接口
            if (this.nowBox && this.nowBox.point && this.nowBox.point.length > 0) {
                const points = this.nowBox.point.map((point) => ({
                    longitude: point.lng,
                    latitude: point.lat
                }));

                // 构建regionCoors字符串
                const regionCoors = points
                    .map((point) => `${point.longitude},${point.latitude}`)
                    .join(';');

                this.handleSelectArea(
                    {
                        shapeType: 2, //1-圆形 2-多边形
                        regionCoors: regionCoors
                    },
                    true
                );
                return;
            }
        },
        // 绘制纯热力图（不绘制边框）
        drawPureHeatMap(data) {
            if (!this.gisLoaded) {
                this.$message('地图尚未初始化完成，请稍候重试');
                return;
            }
            this.clearGisData(false);
            this.hotList = [];

            // 处理热力图数据
            const heatList = data.heatList || [];
            this.hotList = heatList;
            this.totalCount = data.totalUserCnt || 0;

            // 绘制热力图
            this.reRenderHotMap(heatList);
        },
        // 处理区域选择（圈选或框选）
        handleSelectArea(params, isRefresh = false) {
            this.$exloading1x();
            this.getPost('post', 'regionHotMapBySelect', params, '电子围栏热力区域选择', (res) => {
                if (res) {
                    this.drawPureHeatMap(res);
                }
                this.$exloaded1x();
                if (isRefresh) {
                    this.$message.success('刷新成功');
                }
            });
        }
    }
};
</script>

<style lang="less" scoped>
@import url('../../../style/custom.less');
.electric-fence-map {
    width: 100%;
    height: 100%;
    position: relative;
    .gis {
        width: 100%;
        height: calc(100% - 80px);
    }
    .map {
        &-person-total {
            position: absolute;
            top: 96px;
            left: 0;
            width: 280px;
            height: 65px;
            padding-bottom: 10px;
            background-image: url('../../../img/sdElectricFenceMap/total-count-bg.png');
            background-repeat: no-repeat;
            background-size: 100% 100%;
            display: flex;
            gap: 20px;
            align-items: center;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 16px;
            color: #ffffff;
            line-height: 22px;
            .total-title {
                display: flex;
                align-items: center;
                margin-left: 24px;
                &::before {
                    content: '';
                    display: block;
                    width: 34px;
                    height: 44px;
                    margin-right: 20px;
                    background-image: url('../../../img/sdElectricFenceMap/person.png');
                    background-repeat: no-repeat;
                    background-size: 100% 100%;
                }
            }
            .total-count {
                font-family: DINAlternate, DINAlternate;
                font-weight: bold;
                font-size: 24px;
                color: #53ffff;
                line-height: 28px;
            }
        }
        &-public-bg {
            background: rgba(18, 61, 124, 0.9);
            border-radius: 2px;
            border: 1px solid rgba(0, 149, 255, 0.5);
        }
        &-btn-refresh {
            position: absolute;
            top: 96px;
            right: 13px;
            width: max-content;
            height: 44px;
            display: flex;
            align-items: center;
            .refresh-container {
                display: flex;
                gap: 6px;
                padding: 10px 12px;
                cursor: pointer;
                align-items: center;
                justify-content: center;
                .refresh-icon {
                    width: 20px;
                    height: 20px;
                    background-image: url('../../../img/sdElectricFenceMap/refresh.png');
                    background-repeat: no-repeat;
                    background-size: 100% 100%;
                }
                .refresh-label {
                    font-size: 12px;
                    color: #c9dfff;
                    line-height: 14px;
                }
                &:hover {
                    .refresh-icon {
                        background-image: url('../../../img/sdElectricFenceMap/refresh-active.png');
                    }
                    .refresh-label {
                        color: #01e6fe;
                    }
                }
            }
        }
        &-tool {
            position: absolute;
            top: 96px;
            right: 110px;
            width: max-content;
            height: 44px;
            display: flex;
            align-items: center;
            .tool-item {
                display: flex;
                gap: 6px;
                align-items: center;
                justify-content: center;
                padding: 10px 12px;
                cursor: pointer;
                position: relative;
                &:not(:last-child)::after {
                    content: '';
                    position: absolute;
                    right: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    height: 20px;
                    border-right: 1px dashed #c9dfff80;
                }
                .tool-icon {
                    width: 20px;
                    height: 20px;
                    background-image: var(--tool-icon);
                    background-repeat: no-repeat;
                    background-size: 100% 100%;
                }
                .tool-label {
                    font-size: 12px;
                    color: #c9dfff;
                    line-height: 14px;
                }
                &:hover {
                    .tool-icon {
                        background-image: var(--tool-icon-active);
                    }
                    .tool-label {
                        color: #01e6fe;
                    }
                }
                &.active {
                    .tool-icon {
                        background-image: var(--tool-icon-active);
                    }
                    .tool-label {
                        color: #01e6fe;
                    }
                }
            }
        }
        &-legend {
            // width: 86px;
            // height: 178px;
        }
    }
}
.searchBar {
    width: 510px;
    margin-left: auto;
    /deep/.el-select .el-input__inner {
        background-color: transparent;
        border: 1px solid rgba(18, 139, 207, 0.6);
        color: #fff;
    }
}
/deep/.electric-box {
    transform: translate(-50%, -50%);
    min-width: 158px;
    height: 36px;
    display: flex;
    align-items: center;
    position: relative;
    transform-origin: 50% 50%;
    &-left {
        width: 36px;
        height: 36px;
        background: #409eff;
        border-radius: 4px 0px 0px 4px;
        img {
            width: 16px;
            height: 20px;
            margin: 8px;
        }
    }
    &-right {
        background: #fff;
        font-size: 16px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        padding: 8px;
        height: 100%;
        border-radius: 0px 4px 4px 0px;
    }
    .triangle {
        position: absolute;
        bottom: -10px;
        left: 50%;
        width: 0;
        height: 0;
        border-top: 10px solid #fff;
        border-right: 10px solid transparent;
        border-left: 10px solid transparent;
    }
}
</style>
