<template>
    <div class="com-nav">
        <div class="com-nav-title">
            <template v-for="(item, index) in navList">
                <span class="com-nav-item" :key="index">{{ item }}</span>
                <span
                    class="com-nav-item separator"
                    :key="'separator' + index"
                    v-if="index < navList.length - 1"
                    >/</span
                >
            </template>
        </div>
        <div class="com-nav-info">
            <el-popover
                popper-class="info-icon"
                placement="bottom-start"
                width="300"
                trigger="hover"
                :content="infoText"
            >
                <img
                    slot="reference"
                    src="../../img/icon/info_circle.png"
                    alt=""
                    v-if="isShowInfo"
                />
            </el-popover>
        </div>
        <slot></slot>
    </div>
</template>
<script>
export default {
    name: 'comNavTitle',
    props: {
        navList: {
            type: Array,
            default: () => []
        },
        isShowInfo: {
            type: Boolean,
            default: false
        },
        infoText: {
            type: String,
            default: ''
        }
    },
    computed: {
        list() {
            return this.navList;
        }
    },
    data() {
        return {};
    },
    methods: {}
};
</script>
<style lang="less" scoped>
.com-nav {
    width: 100%;
    height: 70px;
    display: flex;
    align-items: center;
    padding-top: 10px;
    background: rgba(0, 50, 107, 0.8);
    box-shadow: inset 0px -1px 0px 0px rgba(15, 114, 175, 0.5);

    &-title {
        background: transparent;
        width: max-content;
        height: 100%;
        margin: 0 0 10px 20px;
        display: flex;
        align-items: center;

        .separator {
            margin: 0 8px;
        }

        .com-nav-item {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 16px;
            color: rgba(255, 255, 255, 0.45);
            line-height: 18px;
            text-align: left;
            font-style: normal;
            &:last-child {
                color: #fff;
            }
        }
    }
    &-info {
        background: transparent;
        height: 100%;
        margin: 0 0 10px 5px;
        display: flex;
        align-items: center;
    }
}
</style>
<style lang="less">
.info-icon.el-popper {
    background: rgba(36, 91, 156, 0.95) !important;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.5) !important;
    border: 1px solid rgba(0, 149, 255, 0.5) !important;
    color: #fff;
    &[x-placement^='top'] .popper__arrow {
        border-top-color: rgba(0, 149, 255, 0.5) !important;
        &::after {
            border-top-color: rgba(36, 91, 156, 0.95) !important;
        }
    }
    &[x-placement^='bottom'] .popper__arrow {
        border-bottom-color: rgba(0, 149, 255, 0.5) !important;
        &::after {
            border-bottom-color: rgba(36, 91, 156, 0.95) !important;
        }
    }
    &[x-placement^='left'] .popper__arrow {
        border-left-color: rgba(0, 149, 255, 0.5) !important;
        &::after {
            border-left-color: rgba(36, 91, 156, 0.95) !important;
        }
    }
    &[x-placement^='right'] .popper__arrow {
        border-right-color: rgba(0, 149, 255, 0.5) !important;
        &::after {
            border-right-color: rgba(36, 91, 156, 0.95) !important;
        }
    }
}
</style>
