<template>
    <cornerCard class="content-bg content-first">
        <section-title-one title="平台应用">
            <el-form class="search-bar-first">
                <el-form-item label="时间粒度：" label-width="100px">
                    <el-select
                        v-model="localForm.granularity"
                        size="small"
                        popper-class="select-dropdown-dark popover-dark"
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="item in granularityOpt"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="时间范围：" label-width="100px">
                    <date-picker
                        v-model="localForm.time"
                        :is-init-time="false"
                        :showOptionsShortcuts="false"
                        type="daterange"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                    ></date-picker>
                </el-form-item>
                <el-form-item label-width="16px">
                    <el-button type="primary" size="small" @click="search">查询</el-button>
                    <el-button type="primary" class="reset-btn" size="small" @click="reset"
                        >重置</el-button
                    >
                </el-form-item>
            </el-form>
        </section-title-one>
        <div class="content-first-wrapper">
            <div class="content-first-left">
                <section-title-two title="应用支撑频次"></section-title-two>
                <common-echart
                    class="left-chart"
                    :targetData="hasLoginData || true ? leftChartData : {}"
                    refName="app-support-freq"
                ></common-echart>
            </div>
            <div class="content-first-right">
                <section-title-two title="功能板块使用统计"></section-title-two>
                <div class="content-first-right-wrapper">
                    <div class="table-box">
                        <div class="table-box-title">热门功能板块</div>
                        <dark-table
                            class="table-box-content"
                            :columns="tableColumns"
                            :tableData="tableData"
                            :defaultSelectedRow="selectedRow"
                            @row-click="handleRowClick"
                        ></dark-table>
                    </div>
                    <div class="chart-box">
                        <div class="chart-box-title">板块点击次数趋势</div>
                        <common-echart
                            class="chart-box-content"
                            :targetData="hasDetailData || true ? rightChartData : {}"
                            refName="function-block-usage"
                        ></common-echart>
                    </div>
                </div>
            </div>
        </div>
    </cornerCard>
</template>
<script>
import commonComponentsMixin from './comMixin.js';
import darkTable from '@/script/components/darkTable.vue';
export default {
    name: 'PlatformApp',
    components: {
        darkTable
    },
    mixins: [commonComponentsMixin],
    props: {
        form: Object,
        granularityOpt: Array
    },
    data() {
        return {
            initialForm: {}, // 保存初始数据
            localForm: {
                granularity: '',
                time: []
            },
            leftChartData: {
                xAxisData: [],
                yAxisName: '单位:次',
                lineData: [
                    {
                        name: '登录数',
                        type: 'bar',
                        data: [],
                        unit: '次',
                        color: '#01E6FE'
                    }
                ]
            },
            rightChartData: {
                xAxisData: [],
                yAxisName: '单位:次',
                lineData: [
                    {
                        name: '点击次数',
                        type: 'bar',
                        data: [],
                        unit: '次',
                        color: '#01E6FE'
                    }
                ]
            },
            tableColumns: [
                {
                    label: '功能名称',
                    prop: 'name'
                },
                {
                    label: '点击次数(次)',
                    prop: 'clickCount',
                    width: 100
                }
            ],
            tableData: [],
            selectedRow: null,
            currentApiName: '', // 当前选中的API名称
            hasLoginData: false, // 是否有登录趋势数据
            hasDetailData: false // 是否有详情数据
        };
    },
    watch: {
        form: {
            handler(newVal) {
                // 当外部form变化时，更新本地表单
                this.localForm = JSON.parse(JSON.stringify(newVal));
            },
            immediate: true,
            deep: true
        }
    },
    mounted() {
        // 保存初始数据副本
        this.initialForm = JSON.parse(JSON.stringify(this.form));

        // 初始化时，如果有时间和粒度，则请求一次接口
        if (this.localForm.time && this.localForm.time.length === 2 && this.localForm.granularity) {
            this.search();
        }
    },
    methods: {
        // 查询按钮点击事件
        search() {
            if (!this.localForm.time || this.localForm.time.length !== 2) {
                this.$message.warning('请选择时间范围');
                return;
            }
            if (!this.localForm.granularity) {
                this.$message.warning('请选择时间粒度');
                return;
            }

            this.getLoginTrend();
            this.getWebAppRank();
        },

        // 重置按钮点击事件
        reset() {
            // 使用初始数据重置表单
            this.localForm = JSON.parse(JSON.stringify(this.initialForm));

            // 重置后执行一次查询
            if (
                this.localForm.time &&
                this.localForm.time.length === 2 &&
                this.localForm.granularity
            ) {
                this.search();
            }
        },

        // 获取应用支撑频次数据
        getLoginTrend() {
            const params = {
                startTime: this.formatDate(this.localForm.time[0], true),
                endTime: this.formatDate(this.localForm.time[1], false),
                timeType: this.localForm.granularity
            };

            this.$exloading1x();
            this.getPost('post', 'loginTrend', params, '系统成效-应用支撑频次', (res) => {
                // 默认设置为无数据
                this.hasLoginData = false;

                if (res && Array.isArray(res) && res.length > 0) {
                    // 处理图表数据
                    const xAxisData = [];
                    const data = [];

                    res.forEach((item) => {
                        xAxisData.push(item.time);
                        data.push(item.count);
                    });

                    if (xAxisData.length > 0 && data.length > 0) {
                        this.leftChartData.xAxisData = xAxisData;
                        this.leftChartData.lineData[0].data = data;
                        this.hasLoginData = true;
                    }
                }
                this.$exloaded1x();
            });
        },

        // 获取功能板块使用统计数据
        getWebAppRank() {
            const params = {
                startTime: this.formatDate(this.localForm.time[0], true),
                endTime: this.formatDate(this.localForm.time[1], false),
                timeType: this.localForm.granularity,
                topN: 10,
                name: ''
            };

            this.$exloading1x();
            this.getPost('post', 'webAppRank', params, '系统成效-热门功能板块', (res) => {
                if (res && Array.isArray(res) && res.length > 0) {
                    // 处理表格数据
                    this.tableData = res.map((item) => ({
                        name: item.appName,
                        clickCount: item.useCnt
                    }));

                    // 如果有数据，默认选中第一条查询详情
                    if (this.tableData.length > 0) {
                        this.selectedRow = 0;
                        this.handleRowClick(this.tableData[0]);
                    }
                } else {
                    // 没有数据时清空表格
                    this.tableData = [];
                    this.hasDetailData = false;
                }
                this.$exloaded1x();
            });
        },

        // 处理行点击事件，获取接口调用详情
        handleRowClick(row) {
            if (!this.localForm.time || this.localForm.time.length !== 2) {
                this.$message.warning('请选择时间范围');
                return;
            }

            this.currentApiName = row.name;

            const params = {
                startTime: this.formatDate(this.localForm.time[0], true),
                endTime: this.formatDate(this.localForm.time[1], false),
                timeType: this.localForm.granularity,
                name: row.name
            };

            this.$exloading1x();
            this.getPost('post', 'webAppDetail', params, '系统成效-板块点击次数趋势', (res) => {
                // 默认设置为无数据
                this.hasDetailData = false;

                if (res && Array.isArray(res) && res.length > 0) {
                    // 处理图表数据
                    const xAxisData = [];
                    const data = [];

                    res.forEach((item) => {
                        xAxisData.push(item.time);
                        data.push(item.count);
                    });

                    if (xAxisData.length > 0 && data.length > 0) {
                        this.rightChartData.xAxisData = xAxisData;
                        this.rightChartData.lineData[0].data = data;
                        this.rightChartData.lineData[0].name = `${row.name}`;
                        this.hasDetailData = true;
                    }
                }
                this.$exloaded1x();
            });
        }
    }
};
</script>

<style lang="less" scoped>
@import '../PanelCommon.less';
// 如果大体样式不变只需关注class="content-first-wrapper"下的样式
.content-first-wrapper {
    width: 100%;
    height: 460px;
    .content-first-right-wrapper {
        width: 100%;
        height: 0;
        flex: 1;
        display: flex;

        .table-box {
            width: 300px;
            height: 100%;
            display: flex;
            flex-direction: column;
            padding: 10px 10px 0 0;
            position: relative;
            &::after {
                content: '';
                position: absolute;
                top: 0;
                left: 100%;
                transform: translateX(100%);
                display: block;
                width: 1px;
                height: calc(100% + 16px);
                background-color: #0095ff;
            }
            &-title {
                font-size: 14px;
                font-weight: 500;
                font-family: 'PingFangSC, PingFang SC';
                color: #fff;
                margin-bottom: 10px;
            }
            &-content {
                width: 100%;
                height: 0;
                flex: 1;
            }
        }
        .chart-box {
            width: 0;
            height: 100%;
            display: flex;
            flex-direction: column;
            flex: 1;
            padding: 10px 10px 0 16px;
            &-title {
                font-size: 14px;
                font-weight: 500;
                font-family: 'PingFangSC, PingFang SC';
                color: #fff;
                margin-bottom: 10px;
            }
            &-content {
                width: 100%;
                height: 0;
                flex: 1;
            }
        }
    }
    .left-chart {
        width: 100%;
        height: 0;
        flex: 1;
        padding-top: 10px;
    }
}
</style>
