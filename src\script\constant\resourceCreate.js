const singleEntryColumns = (mapOpts = {}) => [
    {
        title: '区域名称',
        prop: 'resName',
        children: [
            {
                prop: 'regionName',
                label: '区域名称：',
                type: 'el-input',
                labelWidth: '90px',
                span: 24,
                attrs: {
                    maxlength: 50,
                    'show-word-limit': true
                },
                isShow: true
            },
            {
                prop: 'city',
                label: '地市：',
                type: () => import('../components/selectDistrict.vue'),
                labelWidth: '90px',
                span: 24,
                attrs: {
                    filterable: true,
                    options: mapOpts.city,
                    props: {
                        label: 'name',
                        value: 'value',
                        children: 'children',
                        'popper-class': 'select-dropdown-dark popover-dark'
                    }
                },
                isShow: true
            },
            {
                prop: 'layerIds',
                label: '区域类型：',
                type: 'select',
                labelWidth: '90px',
                span: 24,
                opts: mapOpts.layerIds,
                attrs: {
                    multiple: true,
                    'collapse-tags': true,
                    'popper-class': 'select-dropdown-dark popover-dark'
                },
                isShow: true
            },
            {
                prop: 'shapeType',
                label: '轮廓类型：',
                type: 'select',
                labelWidth: '90px',
                span: 24,
                opts: [
                    { label: '多边形', value: 2 },
                    { label: '圆形', value: 1 }
                ],
                attrs: {
                    'popper-class': 'select-dropdown-dark popover-dark'
                },
                isShow: true
            }
        ]
    },
    {
        title: '配置信息',
        prop: 'configInfo',
        children: [
            {
                prop: 'describe',
                label: '资源描述：',
                type: 'el-input',
                labelWidth: '90px',
                span: 24,
                attrs: {
                    type: 'textarea',
                    rows: 5,
                    maxlength: 500,
                    resize: 'none',
                    'show-word-limit': true
                },
                isShow: true
            }
        ]
    }
];

const baseStationTableCols = (renderHeader) => {
    const columns = [
        {
            prop: 'LACCELL',
            label: 'LACCELL'
        },
        {
            prop: 'gen',
            label: '网络格式',
            width: 80
        },
        {
            prop: 'isArea',
            label: '是否区域内',
            width: 114,
            className: 'custom-cell',
            renderHeader: (h, scope) =>
                renderHeader(h, {
                    ...scope,
                    menu: [
                        {
                            label: '是否区域内',
                            value: null
                        },
                        {
                            label: '是',
                            value: '是'
                        },
                        {
                            label: '否',
                            value: '否'
                        }
                    ]
                })
        },
        {
            prop: 'cellBoundary',
            label: '距离',
            width: 76,
            className: 'custom-cell',
            renderHeader: (h, scope) =>
                renderHeader(h, {
                    ...scope,
                    menu: [
                        {
                            label: '距离',
                            value: null
                        },
                        {
                            label: '<0.1km',
                            value: 0.1
                        },
                        {
                            label: '<0.2km',
                            value: 0.2
                        },
                        {
                            label: '<0.5km',
                            value: 0.5
                        },
                        {
                            label: '<1km',
                            value: 1
                        },
                        {
                            label: '>10km',
                            value: 10
                        }
                    ]
                })
        }
    ];
    return columns;
};
export { singleEntryColumns, baseStationTableCols };
