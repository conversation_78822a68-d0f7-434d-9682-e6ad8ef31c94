<template>
    <div id="space-resource">
        <main-router
            @jumpRouter="jumpRouter"
            @jumpHomePage="jumpHomePage"
            :routerMenuList="routerMenuList"
        ></main-router>
        <div
            class="main-content"
            :style="{ background: needWhiteBgAppId.includes(appIdStr) ? '#fff' : '#012249' }"
        >
            <embed-app
                v-if="appIdStr"
                :key="appIdStr"
                class="page-route"
                :appId="appIdStr"
                :outsideParam="activeMenuNavList"
            ></embed-app>
            <!-- <home-page v-else class="home-page"></home-page> -->
            <effectivenessScreen v-else class="effectiveness-screen"></effectivenessScreen>
        </div>
    </div>
</template>
<script>
import mainRouter from './components/mainRouter.vue';
import homePage from './components/homePage.vue';
import effectivenessScreen from '../effectivenessScreen/indexPlugin.vue';
import { request } from '@/script/utils/request.js';
import { styleMixin } from '@/script/mixins/styleMixin.js';

export default {
    components: {
        mainRouter,
        homePage,
        effectivenessScreen
    },
    name: 'sdMain',
    mixins: [styleMixin],
    data() {
        return {
            appIdStr: '',
            activeMenuNavList: [],
            routerMenuList: { 精准位置: [], 电子围栏: [], 能力展示: [], 系统维护: [] },
            // 需要过滤掉的菜单
            noNeedMenuList: {
                精准位置: [],
                电子围栏: [],
                能力展示: [],
                系统维护: ['应用定义', 'mtex_v2_api', '低码平台']
            },
            // 需要白色背景的菜单
            needWhiteBg: {
                精准位置: [],
                电子围栏: [],
                能力展示: [],
                系统维护: ['机构人员管理']
            },
            needWhiteBgAppId: [],
            // 需要放在最前面的菜单
            needPutFirst: {
                精准位置: [],
                电子围栏: [],
                能力展示: [],
                系统维护: ['能力监控']
            }
        };
    },
    mounted() {},
    created() {
        this.creatMenuList();
    },
    computed: {
        userInfo() {
            return frameService.getUser();
        }
    },
    methods: {
        // 创建菜单（递归支持多级结构）
        creatMenuList() {
            const menuObj = { 精准位置: [], 电子围栏: [], 能力展示: [], 系统维护: [] };
            const sysMenuList = this.$store.state.menusGroupArray || [];

            const buildNodes = (children = [], superName) => {
                const filterNames = this.noNeedMenuList[superName] || [];
                const whiteBgNames = this.needWhiteBg[superName] || [];
                const firstItems = this.needPutFirst[superName] || [];

                let nodes = children
                    .filter((c) => !filterNames.includes(c.name))
                    .flatMap((child) => {
                        if (Array.isArray(child.children) && child.children.length) {
                            const childNodes = buildNodes(child.children, superName);
                            // 只有在仍有有效子节点时才保留这一层
                            if (childNodes.length > 0) {
                                return [
                                    {
                                        name: child.name,
                                        label: child.name,
                                        children: childNodes
                                    }
                                ];
                            }
                            // 否则丢弃该节点
                            return [];
                        }
                        // 叶子节点
                        const leaf = {
                            name: child.name,
                            label: child.name,
                            value: child.appId,
                            super: superName
                        };
                        if (whiteBgNames.includes(child.name)) {
                            this.needWhiteBgAppId.push(leaf.value);
                        }
                        return [leaf];
                    });

                // 优先项排序
                if (firstItems.length) {
                    nodes.sort((a, b) => {
                        const ai = firstItems.indexOf(a.name);
                        const bi = firstItems.indexOf(b.name);
                        function getValue(value) {
                            if (value === -1) {
                                return Infinity;
                            }
                            return value;
                        }
                        return getValue(ai) - getValue(bi);
                    });
                }
                return nodes;
            };

            sysMenuList.forEach((top) => {
                const superName = top.name;
                if (superName in menuObj) {
                    menuObj[superName] = buildNodes(top.children || [], superName);
                }
            });

            this.routerMenuList = JSON.parse(JSON.stringify(menuObj));
        },
        jumpRouter(value, navList = []) {
            // this.openSubPath(value, '', {});
            this.appIdStr = value;
            this.activeMenuNavList = navList;
            // 埋点记录子应用打开记录
            request('post', '/mtex/systemActionLog/saveLog', {
                opType: '打开应用',
                opAppId: value
            }).catch((err) => {
                console.warn('saveLog request failed', err);
            });
        },
        jumpFirstPage() {
            this.jumpRouter('sdPositionVisual');
        },
        jumpHomePage() {
            this.appIdStr = '';
            this.activeMenuNavList = [];
        }
    }
};
</script>
<style lang="less" scoped>
#space-resource {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
    background: #012249;
}

.main-content {
    width: 100%;
    height: calc(100% - 5.666667rem);
    background: #012249;
    position: relative;

    .page-route {
        width: 100%;
        height: 100%;
    }
    .home-page {
        width: 100%;
        height: 100%;
    }
    .effectiveness-screen {
        width: 100%;
        height: 100%;
    }
}
/* 添加过渡效果 */
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.2s ease;
}
.fade-enter,
.fade-leave-to {
    opacity: 0.8;
}
</style>
