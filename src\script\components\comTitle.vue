<template>
    <h1 class="com-title">
        <span class="title">
            {{ title }}
            <slot name="title-tips"></slot>
        </span>
        <div class="right-solt">
            <slot name="right"></slot>
        </div>
    </h1>
</template>
<script>
export default {
    name: 'comTitle',
    props: {
        title: {
            type: String,
            default: '',
        },
    },
    data() {
        return {};
    },
    methods: {},
};
</script>
<style lang="less" scoped>
.com-title {
    font-size: 16px;
    padding: 0 16px 0 20px;
    color: var(--color_1);
    background: var(--back_1);
    width: 100%;
    height: 50px;
    font-weight: 600;
    line-height: 50px;
    position: relative;
    border-bottom: 1px solid #e9e9e9;
    margin: 0;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    .title {
        text-indent: 10px;
        position: relative;
        &:before {
            position: absolute;
            top: 17px;
            left: 0px;
            content: '';
            color: #2774f4;
            background: #2774f4;
            width: 2px;
            height: 17px;
            z-index: 1;
        }
    }
    .right-solt {
        display: inline-flex;
        flex-direction: row;
        align-items: center;
    }
}
</style>
