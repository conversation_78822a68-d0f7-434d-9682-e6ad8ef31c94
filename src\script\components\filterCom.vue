<template>
    <div class="custom-table-header">
        <span class="title marginR">{{ comName }}</span>
        <el-popover
            v-if="ops.hasFitler"
            popper-class="filter-box popover-dark"
            placement="bottom"
            width="200"
            trigger="click"
            v-model="visible"
            @show="showIcon"
            @hide="showIcon"
        >
            <div class="com-box">
                <el-input v-model="inputValue" size="mini" placeholder="在筛选项中搜索"
                    ><i slot="prefix" class="el-input__icon el-icon-search"></i
                ></el-input>
                <div class="divider"></div>
                <el-checkbox
                    :indeterminate="isIndeterminate"
                    v-model="checkAll"
                    @change="handleCheckAllChange"
                    >全选</el-checkbox
                >
                <el-checkbox-group
                    class="checkbox-group"
                    v-model="checkeds"
                    @change="handleCheckedChange"
                >
                    <el-checkbox
                        v-for="(item, index) in data"
                        :label="item.value"
                        :key="index"
                        class="check-item"
                        :class="{
                            'active-check': activeList.includes(item.label)
                        }"
                        >{{ item.label }}</el-checkbox
                    >
                </el-checkbox-group>
                <div class="divider"></div>
                <slot name="footerBtns">
                    <div class="footer-btns">
                        <el-button
                            type="text"
                            size="mini"
                            :disabled="!checkeds.length"
                            @click="btnClick('refresh')"
                            >重置</el-button
                        >
                        <el-button type="primary" size="mini" @click="btnClick('filter')"
                            >确定</el-button
                        >
                    </div>
                </slot>
            </div>
            <div slot="reference">
                <i
                    :class="{
                        'el-icon-arrow-down': !iconIsShow,
                        'el-icon-arrow-up': iconIsShow
                    }"
                ></i>
            </div>
        </el-popover>
        <div class="caret-wrapper sort-box" v-if="ops.hasSort">
            <i
                class="sort-caret ascending"
                :class="[nowType === 'asc' ? 'icon-active-bottom' : '']"
                @click="sortClick('asc')"
            ></i>
            <i
                class="sort-caret descending"
                :class="[nowType === 'dec' ? 'icon-active-top' : '']"
                @click="sortClick('dec')"
            ></i>
        </div>
    </div>
</template>
<script>
export default {
    name: 'filterCom',
    props: {
        data: {
            type: Array,
            default: () => [
                { label: '低', value: '低' },
                { label: '中', value: '中' },
                { label: '高', value: '高' }
            ]
        },
        comName: {
            type: String,
            default: '紧急状态'
        },
        ops: {
            type: Object,
            default: () => ({
                hasFitler: true,
                hasSort: true
            })
        },
        filterType: {
            type: String,
            default: ''
        }
    },

    data() {
        return {
            visible: false,
            iconIsShow: false,
            checkAll: false,
            isIndeterminate: true,
            checkeds: [],
            inputValue: '',
            activeList: [],
            timer: null,
            nowType: ''
        };
    },
    watch: {
        inputValue: {
            handler(val) {
                if (this.timer) {
                    clearTimeout(this.timer);
                    this.timer = null;
                }
                if (val) {
                    this.timer = setTimeout(this.handlerActive.bind(this, val), 500);
                } else {
                    this.removeActive();
                }
            }
        }
    },
    methods: {
        handlerActive(val) {
            console.log('val: ', val);
            let arr = [];
            this.data.forEach((item) => {
                if (item.label.includes(val)) {
                    arr.push(item.label);
                }
            });
            this.activeList = arr;
        },
        removeActive() {
            this.activeList = [];
        },
        showIcon() {
            this.iconIsShow = !this.iconIsShow;
        },
        handleCheckAllChange(val) {
            if (val) {
                this.checkeds = this.data.map((item) => item.value);
            } else {
                this.checkeds = [];
            }
            this.isIndeterminate = false;
        },
        handleCheckedChange(val) {
            let checkedCount = val.length;
            this.checkAll = checkedCount === this.data.length;
            this.isIndeterminate = checkedCount > 0 && checkedCount < this.data.length;
        },
        refreshChecks() {
            this.checkeds = [];
        },
        closePopover() {
            this.visible = false;
            this.inputValue = '';
        },
        btnClick(type = 'filter') {
            if (type === 'refresh') {
                this.refreshChecks();
                return;
            }
            this.closePopover();

            this.$emit('fitler-click', this.checkeds, this.filterType);
        },
        // 排序
        sortClick(type) {
            if (this.nowType && this.nowType === type) {
                this.nowType = '';
            } else {
                this.nowType = type;
            }
            this.$emit('sort-click', this.filterType, this.nowType);
        },
        init() {
            this.visible = false;
            this.inputValue = '';
            this.checkeds = [];
            this.nowType = '';
        }
    }
};
</script>
<style lang="less" scoped>
@import url('../../style/custom.less');
.custom-table-header {
    display: flex;
    align-items: center;
    .marginR {
        margin-right: 5px;
    }
}
.filter-box {
    cursor: pointer;
    .divider {
        height: 1px;
        width: 100%;
        margin: 8px 0;
        background-color: var(--section-card-border);
        position: relative;
    }

    .checkbox-group {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;
        padding-left: 20px;
        .check-item {
            width: 80%;
        }
        .active-check {
            background: #093e79;
            font-size: 14px;
            font-weight: 600;
        }
    }
    .footer-btns {
        display: flex;
        // padding: 10px;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
    }
}
.sort-box {
    // display: inline-flex;
    // flex-direction: column;
    // justify-content: center;
    // align-items: center;
    // color: #606266;
    .sort-caret.icon-active-bottom {
        border-bottom-color: #5cb6ff;
    }
    .sort-caret.icon-active-top {
        border-top-color: #5cb6ff;
    }
}
</style>
