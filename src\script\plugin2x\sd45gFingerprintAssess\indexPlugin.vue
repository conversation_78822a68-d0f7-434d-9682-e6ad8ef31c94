<template>
    <!-- 指纹库评估 -->
    <div class="fingerprint-assess mtex-shandong-devops-dark-theme">
        <comNavTitle :navList="navList">
            <el-radio-group class="radio-group" v-model="searchParams.type" size="small">
                <el-radio-button v-for="(item, index) in btnGroup" :key="index" :label="item.value">
                    {{ item.name }}
                </el-radio-button>
            </el-radio-group>
        </comNavTitle>

        <cornerCard class="assess">
            <el-form
                class="assess-search"
                :model="searchParams"
                label-width="80px"
                size="small"
                inline
            >
                <el-row>
                    <el-col :span="7">
                        <el-form-item label="地市:" prop="city" label-width="60px">
                            <el-select
                                v-model="searchParams.city"
                                placeholder="请选择"
                                popper-class="select-dropdown-dark popover-dark"
                            >
                                <el-option
                                    v-for="item in cityList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="精度等级:" prop="level" label-width="120px">
                            <el-select
                                v-model="searchParams.level"
                                placeholder="请选择"
                                popper-class="select-dropdown-dark popover-dark"
                            >
                                <el-option
                                    v-for="item in levelList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="9">
                        <el-form-item>
                            <el-button type="primary" size="small" @click="getTableData"
                                >查询
                            </el-button>
                            <el-button type="primary" size="small" @click="reset" class="reset-btn"
                                >重置
                            </el-button>
                            <el-button
                                type="primary"
                                size="small"
                                icon="el-icon-upload2"
                                @click="exportData"
                                class="reset-btn export-btn"
                                >导出
                            </el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-table
                class="assess-table table-dark"
                ref="selTable"
                size="small"
                :data="tableData"
                stripe
            >
                <el-table-column prop="city" label="地市" />
                <el-table-column prop="factory" label="厂家" />
                <el-table-column prop="eci" label="小区ECI" />
                <el-table-column prop="name" label="小区名称" />
                <el-table-column prop="updateTime" label="更新时间" />
                <el-table-column prop="samplePoint" label="采样点数" />
            </el-table>
            <el-pagination
                class="assess-page pagination-dark"
                popper-class="pagination-size-dark popover-dark"
                size="mini"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="page.num"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="page.size"
                layout="prev, pager, next, sizes, jumper, total"
                :total="total"
                :pager-count="5"
            >
            </el-pagination>
        </cornerCard>
        <mtv-gis
            class="gis"
            ref="fingerprintGisMap"
            :totaloptions="gistotalOptions"
            @onLoad="gisOnLoad"
        ></mtv-gis>
        <!-- 工具栏 -->
        <div class="map-public-bg map-tool">
            <div
                v-for="item in toolList"
                :key="item.label"
                class="tool-item"
                :class="{ active: currentTool(item) }"
                :style="{
                    '--tool-icon': `url(${item.icon})`,
                    '--tool-icon-active': `url(${item.activeIcon})`
                }"
                @click="item.click(item)"
            >
                <div class="tool-icon"></div>
                <div class="tool-label">{{ item.label }}</div>
            </div>
        </div>
        <!-- 图例 -->
        <finger-print-legend class="map-legend" title="图例" :values="legendValues" />
        <!-- 栅格详情窗 -->
        <div class="grid-detail-window" ref="gridDetailWindow" v-show="showGridDetailWindow">
            <div class="grid-detail-window-title">
                <span>栅格详情</span>
            </div>
            <main class="grid-detail-window-main">
                <div
                    class="grid-detail-window-main-item"
                    v-for="item in gridDetailData"
                    :key="item.name"
                >
                    <span class="grid-detail-window-main-item-value">{{ item.value }}</span>
                    <span class="grid-detail-window-main-item-name">{{ item.name }}</span>
                </div>
            </main>
        </div>
    </div>
</template>

<script>
import comNavTitle from '_com/comNavTitle.vue';
import cornerCard from '_com/cornerCard.vue';
import fingerPrintLegend from '_com/fingerPrintLegend.vue';
import commonMixins from '@/script/mixins/commonMixins.js';
import { gistotalOptions, getMayType, changeGisColor } from '@/script/constant/gis.js';
import { request } from '@/script/utils/request.js';

const petalMarks = {
    redLight: require('../../../img/sd45gFingerprintAssess/red-light.png'),
    yellowLight: require('../../../img/sd45gFingerprintAssess/yellow-light.png'),
    greenLight: require('../../../img/sd45gFingerprintAssess/green-light.png'),
    redDark: require('../../../img/sd45gFingerprintAssess/red-dark.png'),
    yellowDark: require('../../../img/sd45gFingerprintAssess/yellow-dark.png'),
    greenDark: require('../../../img/sd45gFingerprintAssess/green-dark.png')
};

export default {
    components: {
        comNavTitle,
        cornerCard,
        fingerPrintLegend
    },
    props: {
        outsideParam: {
            type: Array,
            default: () => []
        }
    },
    mixins: [commonMixins],
    mounted() {},
    data() {
        return {
            navList: this.outsideParam,
            cityList: [],
            levelList: [
                {
                    label: '≤50米精度',
                    value: '1'
                },
                {
                    label: '≤100米精度',
                    value: '2'
                },
                {
                    label: '≤200米精度',
                    value: '3'
                }
            ],
            searchParams: {
                type: '4G',
                city: '',
                level: ''
            },
            btnGroup: [
                {
                    name: '4G',
                    value: '4G'
                },
                {
                    name: '5G',
                    value: '5G'
                }
            ],
            toolList: [
                {
                    icon: require('../../../img/icon/circle-select.png'),
                    activeIcon: require('../../../img/icon/circle-select-active.png'),
                    label: '圈选',
                    click: (val) => {
                        this.toolClick(val);
                    },
                    isActive: true
                },
                {
                    icon: require('../../../img/icon/box-select.png'),
                    activeIcon: require('../../../img/icon/box-select-active.png'),
                    label: '框选',
                    click: (val) => {
                        this.toolClick(val);
                    },
                    isActive: true
                },
                {
                    icon: require('../../../img/icon/erase.png'),
                    activeIcon: require('../../../img/icon/erase-active.png'),
                    label: '擦除',
                    click: (val) => {
                        this.toolClick(val);
                    },
                    isActive: false
                }
            ],
            activeTool: '',
            thisGis: null,
            gistotalOptions,
            gisLoaded: false,
            gridLayer: null,
            planeLayer: null, // 轮廓layer
            circleLayer: null, // 圆形layer
            nowCircle: null,
            nowBox: null,
            totalCount: 0,
            legendValues: [
                { color: '#00FA9F', text: '0-50%' },
                { color: '#FFD83C', text: '50-80%' },
                { color: '#FF1E1E', text: '80-100%' }
            ],
            tableData: [
                {
                    city: '济南',
                    factory: '华为',
                    eci: '1234567890',
                    name: '济南市历下区',
                    updateTime: '2021-01-01 12:00:00',
                    samplePoint: 100,
                    sampleRate: 50
                },
                {
                    city: '青岛',
                    factory: '华为',
                    eci: '1234567890',
                    name: '青岛市市南区',
                    updateTime: '2021-01-01 12:00:00',
                    samplePoint: 100,
                    sampleRate: 50
                },
                {
                    city: '烟台',
                    factory: '华为',
                    eci: '1234567890',
                    name: '烟台市芝罘区',
                    updateTime: '2021-01-01 12:00:00',
                    samplePoint: 100,
                    sampleRate: 50
                }
            ],
            page: {
                num: 1,
                size: 10
            },
            total: 100,
            showGridDetailWindow: false,
            gridDetailData: [
                {
                    name: '采样点总数',
                    value: '1200'
                },
                {
                    name: '模型训练数量',
                    value: '1000'
                },
                {
                    name: '验证数量',
                    value: '800'
                },
                {
                    name: '≤50米精度数量',
                    value: '600'
                },
                {
                    name: '≤50米精度占比',
                    value: '20%'
                }
            ]
        };
    },
    created() {
        request('post', 'getCityDistrict', {}).then((res) => {
            this.cityList = res.cityList.map((item) => ({
                label: item.name,
                value: item.value
            }));
        });
    },
    methods: {
        gisOnLoad() {
            const gis = (this.thisGis = this.$refs['fingerprintGisMap'].getEntity());
            // 设置底图
            if (getMayType() === 'default') {
                gis.tileLayerList['底图图层'].visible = false;
                //重写getUrl方法
                gis.tileLayerList['高德底图'].getUrl = (x, y, z) => {
                    return `${location.origin}/MapUrl/api/appmaptile?x=${x}&y=${y}&z=${z}`;
                };
            } else {
                gis.tileLayerList['高德底图'] && (gis.tileLayerList['高德底图'].visible = false);
            }

            gis.tool.proportional.visible = true;

            changeGisColor(gis);
            this.drawPetals();
            this.thisGis.gis.needUpdate = true;

            // 圈选
            gis.layerList['圈选'].onCircleChoice.addEvent((data) => {
                this.activeTool = '';
                this.handleSelectArea({
                    shapeType: 1, //1-圆形 2-多边形
                    circle: {
                        radius: data.radius * 1000,
                        centerLongitude: data.startPoint.lng,
                        centerLatitude: data.startPoint.lat
                    }
                });
                this.nowCircle = data;
            });
            // 框选
            gis.layerList.boxSelectLayer.onDoubleClick.addEvent((data) => {
                this.activeTool = '';
                if (data && data.point && data.point.length > 0) {
                    const points = data.point.map((point) => ({
                        longitude: point.lng,
                        latitude: point.lat
                    }));

                    // 构建regionCoors字符串
                    const regionCoors = points
                        .map((point) => `${point.longitude},${point.latitude}`)
                        .join(';');

                    this.handleSelectArea({
                        shapeType: 2, //1-圆形 2-多边形
                        regionCoors: regionCoors
                    });
                }
                this.nowBox = data;
            });
        },
        clearGisData(isClearAll = true) {
            if (this.planeLayer) {
                this.planeLayer.removeAll();
            }
            if (this.circleLayer) {
                this.circleLayer.removeAll();
            }
            if (isClearAll) {
                this.thisGis.layerList['圈选'].removeAll();
                this.thisGis.layerList.boxSelectLayer.removeAll();
            }
            if (this.gridLayer) {
                this.gridLayer.removeAll();
            }
            this.thisGis.layerList.divLayer.removeAll();
        },
        getTableData() {
            console.log('searchParams', this.searchParams);
        },
        reset() {
            this.searchParams = {
                type: '4G',
                city: '',
                level: ''
            };
        },
        exportData() {
            console.log('exportData');
        },
        currentTool(tool) {
            return this.activeTool === tool.label && tool.isActive;
        },
        toolClick(tool) {
            this.activeTool = tool.label;
            this.clearGisData();
            if (tool.label === '擦除') {
                this.clearGisData();
            } else if (tool.label === '圈选') {
                this.thisGis.layerList.boxSelectLayer.outMode();
                this.thisGis.layerList['圈选'].startMode();
            } else if (tool.label === '框选') {
                this.thisGis.layerList['圈选'].outMode();
                this.thisGis.layerList.boxSelectLayer.startMode();
            }
        },
        handleSizeChange(size) {
            this.page.size = size;
            console.log('size', size);
            // this.getTableData();
        },
        handleCurrentChange(num) {
            this.page.num = num;
            console.log('num', num);
            // this.getTableData();
        },
        // 处理区域选择（圈选或框选）
        handleSelectArea(params, isRefresh = false) {
            this.$exloading1x();
            this.getPost('post', 'regionHotMapBySelect', params, '指纹库评估区域选择', (res) => {
                if (res) {
                    // 处理响应数据
                    this.processAreaData(res);
                }
                this.$exloaded1x();
                if (isRefresh) {
                    this.$message.success('刷新成功');
                }
            });
        },
        // 处理区域数据
        processAreaData(data) {
            // 在这里处理区域选择后的数据
            console.log('处理区域数据', data);
        },
        updateGridDetailWindow({ x, y, data }) {
            this.$refs['gridDetailWindow'].style.left = `${x + 24}px`;
            this.$refs['gridDetailWindow'].style.top = `${y}px`;
            this.showGridDetailWindow = true;
            console.log('更新栅格详情窗', data);
        },
        drawPetals(data = []) {
            data = [
                {
                    lng: 117.11811,
                    lat: 36.68484,
                    ht: 0.2,
                    width: 30,
                    name: 'redDark',
                    url: petalMarks.redDark
                },
                {
                    lng: 117.11811 + 0.1,
                    lat: 36.68484 + 0.1,
                    ht: 0.2,
                    width: 30,
                    name: 'yellowDark',
                    url: petalMarks.yellowDark
                }
            ];
            if (!data || data.length === 0) {
                return;
            }
            this.gridLayer = new this.thisGis.layer();
            this.gridLayer.name = 'petals';
            this.thisGis.gis.scene.add(this.gridLayer.Group);
            this.gridLayer.visible = true;
            let material = this.thisGis.meshList.img.getMaterial({
                url: data[0].url,
                opacity: 1
            });
            data.autoScale = true;
            let imgMesh = this.thisGis.meshList.img.create(data, material);
            this.gridLayer.add(imgMesh);
            this.thisGis.gis.needUpdate = true;

            this.thisGis.dom[0].addEventListener('click', (event) => {
                //拾取
                //参数1：拾取类型 这里固定传入2，（0，1）是GIS内部使用的；
                //参数2：计算用 e: 鼠标事件对象，array，需要参与拾取的图层数组，可以传入多个图层。
                this.showGridDetailWindow = false;
                let obj = this.thisGis.math.rayCaster(2, { e: event, array: [this.gridLayer] });
                if (obj !== undefined) {
                    //获取选中的物体的数据index
                    let index = this.thisGis.meshList.img.getIndex(obj);
                    this.thisGis.meshList.img.changeDir(imgMesh, index, 120);
                    this.thisGis.gis.needUpdate = true;

                    const { clientX, clientY } = event;
                    this.updateGridDetailWindow({ x: clientX, y: clientY, data: data[index] });
                }
            });
        }
    }
};
</script>

<style lang="less" scoped>
@import url('../../../style/custom.less');
.fingerprint-assess {
    width: 100%;
    height: 100%;
    position: relative;
    .radio-group {
        margin: 0 24px 0 auto;
    }
    .assess {
        z-index: 10;
        width: 40%;
        position: absolute;
        top: 86px;
        bottom: 16px;
        left: 16px;
        background: rgba(0, 42, 92, 0.9);
        border: 1px solid rgba(0, 149, 255, 0.5);
        padding: 16px;
        display: flex;
        flex-direction: column;
        &-search {
            margin-bottom: 16px;
            /deep/ .el-form-item {
                margin-bottom: 0;
                display: flex;
                &__label {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 14px;
                    color: rgba(255, 255, 255, 0.65);
                    text-align: center;
                }
                &__content {
                    width: 100%;
                    display: flex;
                }
            }
        }
        &-table {
            height: 0;
            flex: 1;
            width: 100%;
        }
        &-page {
            padding-top: 16px;
            /deep/ .el-pagination__sizes,
            /deep/ .el-pagination__jump {
                margin-left: 0;
                margin-right: 0;
            }
        }
    }
    .gis {
        width: 100%;
        height: calc(100% - 80px);
    }
    .map {
        &-person-total {
            position: absolute;
            top: 96px;
            left: 0;
            width: 280px;
            height: 65px;
            padding-bottom: 10px;
            background-image: url('../../../img/sdElectricFenceMap/total-count-bg.png');
            background-repeat: no-repeat;
            background-size: 100% 100%;
            display: flex;
            gap: 20px;
            align-items: center;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 16px;
            color: #ffffff;
            line-height: 22px;
            .total-title {
                display: flex;
                align-items: center;
                margin-left: 24px;
                &::before {
                    content: '';
                    display: block;
                    width: 34px;
                    height: 44px;
                    margin-right: 20px;
                    background-image: url('../../../img/sdElectricFenceMap/person.png');
                    background-repeat: no-repeat;
                    background-size: 100% 100%;
                }
            }
            .total-count {
                font-family: DINAlternate, DINAlternate;
                font-weight: bold;
                font-size: 24px;
                color: #53ffff;
                line-height: 28px;
            }
        }
        &-public-bg {
            background: rgba(18, 61, 124, 0.9);
            border-radius: 2px;
            border: 1px solid rgba(0, 149, 255, 0.5);
        }
        &-btn-refresh {
            position: absolute;
            top: 96px;
            right: 13px;
            width: max-content;
            height: 44px;
            display: flex;
            align-items: center;
            .refresh-container {
                display: flex;
                gap: 6px;
                padding: 10px 12px;
                cursor: pointer;
                align-items: center;
                justify-content: center;
                .refresh-icon {
                    width: 20px;
                    height: 20px;
                    background-image: url('../../../img/sdElectricFenceMap/refresh.png');
                    background-repeat: no-repeat;
                    background-size: 100% 100%;
                }
                .refresh-label {
                    font-size: 12px;
                    color: #c9dfff;
                    line-height: 14px;
                }
                &:hover {
                    .refresh-icon {
                        background-image: url('../../../img/sdElectricFenceMap/refresh-active.png');
                    }
                    .refresh-label {
                        color: #01e6fe;
                    }
                }
            }
        }
        &-tool {
            position: absolute;
            top: 96px;
            right: 16px;
            width: max-content;
            height: 44px;
            display: flex;
            align-items: center;
            .tool-item {
                display: flex;
                gap: 6px;
                align-items: center;
                justify-content: center;
                padding: 10px 12px;
                cursor: pointer;
                position: relative;
                &:not(:last-child)::after {
                    content: '';
                    position: absolute;
                    right: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    height: 20px;
                    border-right: 1px dashed #c9dfff80;
                }
                .tool-icon {
                    width: 20px;
                    height: 20px;
                    background-image: var(--tool-icon);
                    background-repeat: no-repeat;
                    background-size: 100% 100%;
                }
                .tool-label {
                    font-size: 12px;
                    color: #c9dfff;
                    line-height: 14px;
                }
                &:hover {
                    .tool-icon {
                        background-image: var(--tool-icon-active);
                    }
                    .tool-label {
                        color: #01e6fe;
                    }
                }
                &.active {
                    .tool-icon {
                        background-image: var(--tool-icon-active);
                    }
                    .tool-label {
                        color: #01e6fe;
                    }
                }
            }
        }
        &-legend {
        }
    }
}
.searchBar {
    width: 510px;
    margin-left: auto;
    /deep/.el-select .el-input__inner {
        background-color: transparent;
        border: 1px solid rgba(18, 139, 207, 0.6);
        color: #fff;
    }
}
/deep/.electric-box {
    transform: translate(-50%, -50%);
    min-width: 158px;
    height: 36px;
    display: flex;
    align-items: center;
    position: relative;
    transform-origin: 50% 50%;
    &-left {
        width: 36px;
        height: 36px;
        background: #409eff;
        border-radius: 4px 0px 0px 4px;
        img {
            width: 16px;
            height: 20px;
            margin: 8px;
        }
    }
    &-right {
        background: #fff;
        font-size: 16px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        padding: 8px;
        height: 100%;
        border-radius: 0px 4px 4px 0px;
    }
    .triangle {
        position: absolute;
        bottom: -10px;
        left: 50%;
        width: 0;
        height: 0;
        border-top: 10px solid #fff;
        border-right: 10px solid transparent;
        border-left: 10px solid transparent;
    }
}
.export-btn {
    margin-left: auto;
}
.grid-detail-window {
    position: fixed;
    width: 330px;
    height: 200px;
    background-image: url('../../../img/sd45gFingerprintAssess/bg-grid-detail.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    z-index: 1000;
    &-title {
        position: absolute;
        top: 2px;
        left: 5px;
        right: 5px;
        height: 46px;
        background-image: url('../../../img/sd45gFingerprintAssess/header-grid-detail.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        span {
            display: block;
            margin: 6px 0 0 33px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 14px;
            color: #ffffff;
            line-height: 20px;
            text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.3);
        }
    }
    &-main {
        position: absolute;
        top: 48px;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 24px;
        padding-top: 0;
        display: grid;
        gap: 8px;
        grid-template-columns: 1fr 1fr 1fr;
        &-item {
            display: flex;
            gap: 4px;
            flex-direction: column;
            justify-content: center;
            &-value {
                font-family: DIN, DIN;
                font-weight: bold;
                font-size: 20px;
                color: #ffffff;
                line-height: 24px;
            }
            &-name {
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 12px;
                color: #c9dfff;
                line-height: 17px;
            }
        }
    }
}
</style>
