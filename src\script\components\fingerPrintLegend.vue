<template>
    <div class="finger-public-bg finger-legend">
        <p>{{ title }}</p>
        <div class="legend-content">
            <div class="legend-content-item" v-for="(value, index) in values" :key="index">
                <span class="legend-content-item-color" :style="{ background: value.color }"></span>
                <span class="legend-content-item-text">{{ value.text }}</span>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'heatMapLegend',
    props: {
        title: {
            type: String,
            default: '图例'
        },
        values: {
            type: Array,
            default: () => [
                { color: '#00FA9F', text: '0-50%' },
                { color: '#FFD83C', text: '50-80%' },
                { color: '#FF1E1E', text: '80-100%' }
            ]
        }
    }
};
</script>

<style lang="less" scoped>
.finger-legend {
    position: absolute;
    bottom: 13px;
    right: 13px;
    min-width: 100px;
    min-height: 150px;
    display: flex;
    flex-direction: column;
    padding: 16px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: #ffffff;
    line-height: 16px;
    .legend-content {
        height: 0;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        gap: 10px;
        &-item {
            height: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            &-color {
                width: 16px;
                height: 16px;
            }
            &-text {
                width: max-content;
                height: 100%;
            }
        }
    }
}
.finger-public-bg {
    background: rgba(0, 42, 92, 0.9);
    border: 1px solid rgba(0, 149, 255, 0.5);
}
</style>
