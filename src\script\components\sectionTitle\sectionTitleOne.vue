<template>
    <div class="section-title-one" :style="sectionStyle">
        <div class="tri-play-icon">
            <div class="triangle"></div>
            <div class="triangle"></div>
            <div class="triangle"></div>
        </div>
        <span class="title" :style="titleStyle">
            {{ title }}
        </span>
        <div class="slot-container">
            <slot></slot>
        </div>
    </div>
</template>
<script>
export default {
    name: 'sectionTitleOne',
    props: {
        title: {
            type: String,
            default: ''
        },
        fontSize: {
            type: Number,
            default: 18
        },
        height: {
            type: Number,
            default: 40
        },
        marginBottom: {
            type: Number,
            default: 10
        }
    },
    data() {
        return {};
    },
    computed: {
        titleStyle() {
            // 当fontSize为18px时，line-height应为36px，保持2倍关系
            const lineHeight = this.fontSize * 2 + 'px';

            return {
                fontSize: this.fontSize + 'px',
                lineHeight: lineHeight
            };
        },
        sectionStyle() {
            return {
                height: this.height + 'px',
                marginBottom: this.marginBottom + 'px'
            };
        }
    },
    methods: {}
};
</script>
<style lang="less" scoped>
.section-title-one {
    display: flex;
    align-items: center;
    gap: 12px;
    // 三角形播放图标效果
    .tri-play-icon {
        display: inline-flex;
        align-items: center;
        height: 100%;
        .triangle {
            background-color: #00f5f2;
            &:nth-child(1) {
                width: 10px;
                height: 20px;
                clip-path: polygon(0% 0%, 100% 50%, 0% 100%, 0% 50%);
            }
            &:nth-child(2) {
                width: 8px;
                height: 16px;
                clip-path: polygon(0% 0%, 100% 50%, 0% 100%, 50% 50%);
            }
            &:nth-child(3) {
                width: 6px;
                height: 12px;
                clip-path: polygon(0% 0%, 100% 50%, 0% 100%, 70% 50%);
            }
        }
    }
    .title {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #fff;
        font-style: normal;
    }
    .slot-container {
        width: 0;
        flex: 1;
        height: 100%;
        display: flex;
    }
}
</style>
