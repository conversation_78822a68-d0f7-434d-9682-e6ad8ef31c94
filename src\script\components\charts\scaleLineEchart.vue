<template>
    <div class="out-box">
        <el-empty
            v-if="JSON.stringify(targetLineData) === '{}'"
            description="暂无数据"
            :image="require('../../../img/noDataMon.png')"
        >
        </el-empty>
        <div v-else :id="name" class="scale-line-echart"></div>
    </div>
</template>

<script>
import * as echarts from 'echarts';
import { intNum, binaryTran, formatNum } from '../../utils/index.js';
const initEcharts = (myChart, options) => {
    const option = {
        title: {
            left: 'center',
            top: 20,
            text: options.title,
            textStyle: {
                color: '#fff',
                fontSize: 12
            }
        },
        tooltip: {
            confine: true,
            trigger: 'axis',
            // triggerOn: 'click', //触发方式
            formatter: function (params) {
                let res = `${params[0].name}<br/>`;
                params.forEach((item, index) => {
                    let newVal = params[index].data;
                    let returnStr = `${newVal}&nbsp;${options.unit[index]}`;
                    if (options.tooltipHandler && options.tooltipHandler[item.seriesName]) {
                        returnStr = `${newVal} MiB (约 ${binaryTran(newVal)})`;
                    } else {
                        newVal = formatNum(newVal);
                        returnStr = newVal + ' ' + options.unit[index];
                        // if (newVal >= 10 ** 4 && newVal < 10 ** 8) {
                        //     newVal = `${newVal} （约 ${(
                        //         newVal /
                        //         10 ** 4
                        //     ).toFixed(2)}万)`;
                        //     returnStr = `${newVal}&nbsp;${options.unit[index]}`;
                        // } else if (newVal >= 10 ** 8) {
                        //     newVal = `${newVal} （约 ${(
                        //         newVal /
                        //         10 ** 8
                        //     ).toFixed(2)}亿)`;
                        //     returnStr = `${newVal}&nbsp;${options.unit[index]}`;
                        // }
                    }

                    res += `${params[index].marker}${params[index].seriesName} &nbsp;${returnStr}&nbsp;<br/>`;
                });
                return res;
            }
        },
        toolbox: {
            top: 5,
            right: 20,
            feature: {
                dataZoom: {
                    yAxisIndex: 'none'
                }
            }
        },
        dataZoom: [
            {
                type: 'inside',
                start: 0,
                end: 100
            },
            {
                start: 0,
                end: 100,
                height: 20,
                showDetail: false,
                showDataShadow: false,
                backgroundColor: '#04346AFF',
                fillerColor: 'rgba(0, 149, 255, 0.3)',
                borderColor: '#00000000',
                borderRadius: 0,
                moveHandleSize: 0,
                handleIcon: 'image://' + require('../../../img/icon/handle-icon.png'),
                handleSize: 20,
                handleStyle: {
                    color: 'rgba(0, 149, 255, 1)',
                    borderWidth: 0
                }
            }
        ],
        legend: {
            show: true,
            bottom: 50,
            left: 'center',
            itemWidth: 10,
            itemHeight: 10,
            itemGap: 30,
            textStyle: {
                fontSize: 12
            },
            formatter: function (name) {
                const index = options.lineData.findIndex((item) => item.name === name);
                if (index === options.lineData.length - 1) {
                    if (options.legend[name]) {
                        return '{a|' + name + '}{b|' + options.legend[name] + '}';
                    }
                    return '{a|' + name + '}';
                }
                return '{a|' + name + '}{b|' + options.legend[name] + '      |' + '}';
            },
            textStyle: {
                color: '#B3DAF4FF',
                rich: {
                    a: {
                        width: 60
                    },
                    b: {},
                    c: {
                        width: 20,
                        left: 'center'
                    }
                }
            }
        },
        grid: {
            top: 65,
            left: 100,
            right: 70,
            bottom: 100
        },
        xAxis: {
            type: 'category',
            data: options.xAxisData,
            axisTick: { show: false },
            axisLabel: {
                color: '#B3DAF4FF',
                fontSize: 12,
                margin: 10
            },
            axisLine: {
                lineStyle: {
                    color: '#B3DAF4FF'
                    // color: 'rgba(153, 153, 153, 1)'
                }
            },
            textStyle: {
                color: '#B3DAF4FF'
            },

            splitLine: {
                show: false,
                lineStyle: {
                    type: 'dashed',
                    color: '#B3DAF4FF'
                    // color: 'rgba(242, 242, 242, 1)'
                }
            }
        },
        yAxis: [],
        series: []
    };
    options.yAxis.forEach((item, index) => {
        const Yoptions = {
            type: 'value',
            name: item,
            max: function (value) {
                let max = value.max;
                let n = 10 ** (String(Math.ceil(max)).length - 2) * 1;
                // n 最大位数少1
                let middle = max % (n * 10);
                const newA = n * 5 > middle ? n * 5 : 10 * n;
                max = max - middle + newA;
                if (item === '高时延占比（%）' && max >= 100) {
                    max = 100;
                }
                if (max <= 5) max = 5;
                return max;
            },
            min: function (value) {
                let min = Math.floor(value.min - Math.floor(value.min * 0.3));
                let n = 10 ** (String(Math.ceil(min)).length - 1) * 1;
                min = intNum(min, n);
                if (String(min).length <= String(Math.ceil(value.max)).length - 1) {
                    min = 0;
                }
                min = min >= 10 ? intNum(min, 10) : 0;
                if (min <= 0) {
                    min = 0;
                }
                return min;
            },
            nameTextStyle: {
                color: '#B3DAF4FF',
                fontSize: 12,
                padding: [0, 20, 0, 0]
            },
            axisLine: { show: false },
            axisTick: { show: false },
            axisLabel: {
                margin: 4,
                fontSize: 12,
                color: '#B3DAF4FF',
                // color: 'rgba(100, 100, 100, 1)',
                formatter: function (val) {
                    let newVal = val;
                    if (newVal >= 10 ** 8) {
                        newVal = newVal / 10 ** 8 + '亿';
                    } else if (newVal >= 10 ** 4) {
                        newVal = newVal / 10 ** 4 + '万';
                    }

                    return newVal;
                }
            },
            splitLine: {
                lineStyle: {
                    type: 'dashed',
                    color: '#B3DAF4FF'
                    // color: 'rgba(242, 242, 242, 1)'
                }
            }
        };
        if (index !== 0) {
            Yoptions.splitLine.show = false;
        }
        option.yAxis.push(Yoptions);
    });
    options.lineData.forEach((item, index) => {
        option.series.push({
            name: item.name,
            data: item.data,
            type: 'line',
            smooth: true,
            unit: item.unit,
            yAxisIndex: index,
            itemStyle: {
                color: item.color || 'rgba(118, 131, 143, 1)'
            }
        });
    });
    myChart.clear();
    myChart.setOption(option);
};
export default {
    name: 'scaleLineEchart',
    props: {
        /**
		 * 趋势图数据
         * {
            title:'每5分钟记录总量  记录时间：2023-03-20',
            xAxisData:['12:00','13:00','14:00','15:00','16:00','17:00','18:00','19:00','20:00','21:00','22:00','23:00'],
            yAxis:['数量（十亿）','一分钟平均时延（分）'],
            unit:['十亿','分'],
            legend:{
                '记录数':'平均：21.58 十亿 总计：1.11 十亿',
                '时延':'平均：30'
            },
            lineData:[
                {
                    name:'记录数',
                    data:[512,434,666,123,113,443,124,123,375,223,344,566,356],
                    color:'#5586EA',
                },
                {
                    name:'时延',
                    data:[523,445,666,223,213,543,224,223,305,323,334,466,336],
                    color:'#5AD8A6',
                }
            ]
           },
		 */
        targetLineData: {
            type: Object,
            default: () => ({})
        },
        name: {
            type: String,
            default: 'line'
        }
    },
    data() {
        return {
            myChart: ''
        };
    },
    watch: {
        targetLineData: {
            handler(newV) {
                if (newV.lineData) {
                    this.initEchart();
                }
            },
            deep: true
        }
    },
    mounted() {
        if (this.targetLineData && this.targetLineData.lineData) {
            this.initEchart();
        }
    },
    methods: {
        initEchart() {
            this.$nextTick(() => {
                this.myChart = echarts.init(document.getElementById(this.name));
                initEcharts(this.myChart, this.targetLineData);
            });
        }
    }
};
</script>

<style lang="less" scoped>
.out-box,
.scale-line-echart {
    width: 100%;
    height: 100%;
}
.el-empty {
    height: 100%;
}
</style>
