<template>
    <el-cascader
        ref="selectPaneRef"
        v-model="selectList"
        class="select-district"
        :options="options"
        :props="props"
        :disabled="disabled"
        :size="size"
        :filterable="filterable"
        collapse-tags
        :popper-class="
            isLastMultiSelect
                ? 'custom-hide-checkbox cascader-menu-dark popover-dark'
                : 'cascader-menu-dark popover-dark'
        "
        :clearable="clearable"
        @change="handleChange"
        @expand-change="handleExpandChange"
    />
</template>

<script>
export default {
    name: 'select-district',
    props: {
        value: {
            type: Array,
            default: () => []
        },
        options: {
            type: Array,
            default: () => []
        },
        props: {
            type: Object,
            default: () => ({
                multiple: true,
                label: 'label',
                value: 'value',
                children: 'children'
            })
        },
        isLastMultiSelect: {
            type: Boolean,
            default: true
        },
        disabled: {
            type: Boolean,
            default: false
        },
        size: {
            type: String,
            default: 'mini'
        },
        clearable: {
            type: Boolean,
            default: true
        },
        filterable: {
            type: Boolean,
            default: false
        },
        emitPath: {
            type: Boolean,
            default: true
        }
    },
    model: {
        prop: 'value',
        event: 'change'
    },
    data() {
        return {
            curRootPath: '',
            preCheckedRootPath: '',
            preSelectList: []
        };
    },
    computed: {
        selectList: {
            get() {
                return this.value.map((item) => item);
            },
            set(newList) {
                this.$emit('change', newList);
            }
        },
        // 多选且只要最后一层
        isMultiAndNoPath() {
            const { multiple, emitPath } = this.props;
            return !emitPath && multiple;
        }
    },
    methods: {
        handleChange() {
            if (!this.isLastMultiSelect) return;
            if (!this.preCheckedRootPath || this.preCheckedRootPath !== this.curRootPath) {
                this.preCheckedRootPath = this.curRootPath;
                const { multiple, emitPath } = this.props;
                if (multiple) {
                    this.$nextTick(() => {
                        if (emitPath) {
                            this.selectList = this.selectList.filter((items) =>
                                items.join('/').startsWith(this.curRootPath)
                            );
                        } else {
                            this.selectList = this.selectList.filter(
                                (code) => !this.preSelectList.includes(code)
                            );
                        }
                    });
                }
            }
        },
        handleExpandChange(paths) {
            if (!this.isLastMultiSelect) return;
            this.curRootPath = paths.join('/');
            if (this.isMultiAndNoPath && this.curRootPath) {
                this.preSelectList = [...this.selectList];
            }
        }
    }
};
</script>
<style lang="less" scoped>
.select-district {
    width: 100%;
    .el-cascader-panel {
        width: 100%;

        /deep/ .el-cascader-menu {
            min-width: 100px;

            .el-cascader-node {
                padding-left: 8px;
            }

            &__wrap {
                .el-checkbox {
                    display: none;
                }
            }

            &:nth-of-type(3) {
                .el-cascader-menu__wrap {
                    .el-checkbox {
                        display: block;
                    }
                }
            }
        }
    }
    .el-input .el-input__inner:focus,
    .el-input.is-focus .el-input__inner {
        height: 32px; //这里高度根据需求自己设定
    }
    /deep/ .el-cascader__tags {
        display: inline-flex;
        margin-right: 60px;
        flex-wrap: nowrap;
        .el-cascader__search-input {
            min-width: 20px;
        }
    }
}
</style>
<style lang="less">
.custom-hide-checkbox {
    .el-cascader-panel li[aria-haspopup] .el-checkbox {
        display: none;
    }
}
</style>
