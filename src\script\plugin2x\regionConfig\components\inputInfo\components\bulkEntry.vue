<template>
    <div class="bulk-entry">
        <div class="bulk-entry__upload">
            <el-upload
                class="uploads"
                action=""
                accept=".xlsx, .xls"
                :before-upload="handleBefore"
                :on-change="handleChange"
                :on-remove="handleRemove"
                :file-list="fileList"
                auto-upload
            >
                <img :src="uploadImg" alt="上传" />
                <div class="text"><span class="blue">上传</span>批量录入文件</div>
                <div class="divider"></div>
            </el-upload>
        </div>
        <div class="bulk-entry__download">
            <i class="el-icon-download blue"></i>
            <span class="blue" @click="downloadTemplate">下载批量录入模板</span>
        </div>
    </div>
</template>

<script>
import uploadImg from '@/img/createResource/upload.png';
import { getFileRequst } from '@/script/utils/request.js';
import { handleDownloadFile } from '@/script/utils/index';
export default {
    name: 'bulk-entry',
    data() {
        return {
            uploadImg,
            uploadUrl: '',
            file: {},
            fileList: []
        };
    },
    methods: {
        handleBefore(file) {
            // 校验
            if (!/\.(xls|xlsx)$/.test(file.name.toLowerCase())) {
                this.$message.warning('上传格式不正确，请上传xls或者xlsx格式');
                return false;
            }
        },
        handleChange(file, fileList) {
            this.file = file;
            if (fileList.length === 2) {
                fileList.shift();
            }
            this.fileList = fileList;
        },
        handleRemove() {},
        downloadTemplate() {
            getFileRequst(
                'post',
                'downloadExcelTemplate',
                {},
                {
                    responseType: 'blob'
                }
            )
                .then((res) => {
                    handleDownloadFile(res, (err) => {
                        this.$message(err);
                    });
                })
                .catch((e) => {});
        },
        create() {},
        cancel() {}
    }
};
</script>

<style lang="less" scoped>
.bulk-entry {
    margin-top: 16px;
    &__upload {
        padding: 16px 12px;
        border-radius: 4px;
        text-align: center;
        background-color: #00326bcc;
        color: #c9dfff;
        border: 1px dashed rgba(0, 0, 0, 0.15);
        /deep/ .el-upload {
            width: 100%;
            .el-upload__input {
                display: none;
            }
        }
        /deep/ .el-upload-list {
            .el-upload-list__item {
                margin-top: 10px;
                border: 1px solid #ccc;
            }
            .el-upload-list__item-name {
                text-align: left;
            }
        }
        .text {
            font-size: 14px;
            font-weight: bold;
            letter-spacing: 1px;
        }
        .divider {
            margin: 16px 0 10px 0;
            height: 1px;
            border: 1px dashed rgba(0, 0, 0, 0.15);
        }
    }
    &__download {
        margin-top: 8px;
        font-size: 12px;
        color: #666666;
    }
}
.blue {
    color: #0091ff;
    cursor: pointer;
    text-decoration: underline;
    font-weight: 500;
}
</style>
