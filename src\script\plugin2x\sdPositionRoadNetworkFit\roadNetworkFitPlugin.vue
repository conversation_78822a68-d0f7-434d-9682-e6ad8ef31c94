<template>
    <div class="road-network-fit mtex-shandong-devops-dark-theme">
        <comNavTitle class="road-network-fit-search" :navList="navList">
            <searchBar
                class="search-bar"
                ref="formList"
                :formCols="formCols"
                :form="form"
                :isTransparentBg="true"
            >
                <template #search>
                    <el-button type="primary" size="small" @click="search">查询</el-button>
                </template>
                <template #reset>
                    <el-button class="reset-btn" size="small" @click="reset">重置</el-button>
                </template>
            </searchBar>
        </comNavTitle>
        <mtv-gis
            class="gis"
            ref="roadNetworkFitGisMap"
            :totaloptions="gistotalOptions"
            @onLoad="gisOnLoad"
        ></mtv-gis>
        <!-- 右侧播放详情 -->
        <div class="play-detail sd-devops-overflow-y--grey">
            <roadStep ref="roadStep" :data="treeData" @roadClick="roadClick"></roadStep>
        </div>
        <div class="tools-wrapper">
            <!-- 进度条 -->
            <el-slider
                class="slider-wrapper custom-slider-disabled"
                disabled
                :show-tooltip="false"
                v-model="sliderValue"
            >
            </el-slider>
            <div class="btn-wrapper">
                <el-popover
                    placement="top-start"
                    popper-class="speed-popover"
                    width="92"
                    :visible-arrow="false"
                    trigger="manual"
                    v-model="visible"
                >
                    <div class="speed-popover-content">
                        <div
                            class="speed-popover-content-item"
                            v-for="(item, index) in speedList"
                            :key="index"
                            @click="changeSpeed(item)"
                            :class="{ active: currentSpeed === item.value }"
                        >
                            {{ item.label }}
                        </div>
                    </div>
                    <div class="speed-btn" slot="reference" @click="visible = !visible">倍速</div>
                </el-popover>
                <el-button
                    v-if="!isPlay"
                    class="sd-devops-btn--small btn-content-center sd-devops-btn--success"
                    type="primary"
                    size="small"
                    icon="el-icon-video-play"
                    @click="play"
                    >播放</el-button
                >
                <el-button
                    v-else
                    class="sd-devops-btn--small btn-content-center"
                    type="warning"
                    size="small"
                    icon="el-icon-video-pause"
                    @click="pause"
                    >暂停</el-button
                >
                <el-button
                    class="sd-devops-btn--small btn-content-center stop-btn"
                    type="danger"
                    size="small"
                    @click="stop"
                >
                    <img :src="stopBtnImg" class="btn-stop-icon" />
                    停止</el-button
                >
            </div>
        </div>
    </div>
</template>
<!-- eslint-disable max-depth -->
<!-- eslint-disable complexity -->
<script>
import comNavTitle from '_com/comNavTitle.vue';
import searchBar from '_com/searchBar/searchBar.vue';
import roadStep from './components/roadStep.vue';
// import dataTest from './dataTest/data.json';
import beginImg from '../../../img/sdPosition/begin.png';
import endImg from '../../../img/sdPosition/end.png';
import positionImg from '../../../img/sdPosition/position.png';
import walkImg from '../../../img/gis/walk_big.png';
import bikeImg from '../../../img/gis/bicycle_big.png';
import carImg from '../../../img/gis/car_big.png';
import baseStationDir from '../../../img/gis/baseStationDir.png';
import stopBtnImg from '../../../img/icon/btn-stop.png';
import commonMixins from '@/script/mixins/commonMixins.js';
import { gistotalOptions, getMayType, changeGisColor } from '@/script/constant/gis.js';
export default {
    name: 'roadNetworkFit',
    components: {
        comNavTitle,
        searchBar,
        roadStep
    },
    props: {
        outsideParam: {
            type: Array,
            default: () => []
        }
    },
    mixins: [commonMixins],
    data() {
        return {
            navList: this.outsideParam,
            visible: false,
            speedList: [
                { label: '0.75x', value: 0.75 },
                { label: '1x', value: 1 },
                { label: '1.5x', value: 1.5 },
                { label: '2.0x', value: 2 },
                { label: '3.0x', value: 3 },
                { label: '5.0x', value: 5 }
            ],
            currentSpeed: 1,
            beginImg,
            endImg,
            positionImg,
            walkImg,
            bikeImg,
            carImg,
            baseStationDir,
            stopBtnImg,
            formCols: [
                [
                    {
                        type: 'el-input',
                        prop: 'msisdn',
                        label: '用户号码：',
                        labelWidth: '85px',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true,
                            class: 'displayPass'
                        },
                        rules: [
                            // { required: true, message: '必填', trigger: 'submit' }
                        ],
                        span: 9,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    // {
                    //     type: 'el-date-picker',
                    //     prop: 'time',
                    //     label: '日期选择：',
                    //     labelWidth: '100px',
                    //     attrs: {
                    //         'start-placeholder': '请选择开始时间',
                    //         'end-placeholder': '请选择结束时间',
                    //         type: 'datetimerange',
                    //         clearable: true,
                    //         'value-format': 'yyyy-MM-dd HH:mm:ss',
                    //         'default-time': ['00:00:00', '23:59:59']
                    //     },
                    //     rules: [
                    //         // { required: true, message: '必填', trigger: 'submit' }
                    //     ],
                    //     span: 13,
                    //     isDisabled: false,
                    //     isShow: true,
                    //     opts: []
                    // },
                    {
                        type: 'datePicker',
                        prop: 'time',
                        label: '日期选择：',
                        labelWidth: '85px',
                        attrs: {
                            pickerType: 'oneday-time',
                            'is-init-time': false,
                            'init-time-type': 'today',
                            showOptionsShortcuts: false,
                            clearable: true
                        },
                        span: 11,
                        isShow: true
                    },
                    {
                        type: 'tool',
                        prop: 'tool',
                        span: 4,
                        isShow: true,
                        labelWidth: '15px'
                    }
                ]
            ],
            form: {
                msisdn: '',
                time: []
            },
            gistotalOptions: gistotalOptions,
            gisLoaded: false,
            sliderValue: 0,
            marks: {
                0.75: '225m',
                1: '300m',
                1.5: '450m',
                2: '600m',
                3: '900m',
                5: '1500m'
            },
            isPlay: false,
            treeData: [],
            queryData: [],
            keyIndex: {},
            roadLayer: null,
            allLayer: null,
            trailLayer: null,
            trailPoints: [],
            reTrailData: [],
            sliderShow: false,
            nowRoad: null,
            trailAnimotion: null,
            walkMaterial: null, //步行图标
            bikeMaterial: null, //自行车图标
            carMaterial: null, //汽车图标
            highLightRoadLayer: null, //高亮道路图层
            highLightRoadList: [], //高亮道路数组
            baseStationMaterial: null, //基站图层图标
            positionMaterial: null, //当前位置图标
            toolTipsDom: '',
            baseStationLayer: null, //基站图层
            pointEciConnectLayer: null, //驻点和eci小区连接高亮线
            eciIdList: [], //小区名称id的数组
            totalTrailLength: 0, // 添加总轨迹长度
            currentTrailProgress: 0, // 添加当前轨迹进度
            isPlayFromMiddle: false, // 标记当前是从中间位置开始播放
            usePoitionImg: true, // 是否使用位置图标(不区分汽车、自行车、步行)
            beginMaterial: null, //起点图标
            endMaterial: null //终点图标
        };
    },
    computed: {
        animationSpeed() {
            return this.marks[this.currentSpeed].split('m')[0];
        }
    },
    watch: {
        nowRoad(newVal, oldVal) {
            let nodeKey = this.keyIndex[newVal];
            let oldNodeKey = this.keyIndex[oldVal];
            if (nodeKey || oldNodeKey) {
                this.$refs['roadStep'].$refs['key'].forEach((item) => {
                    let node = item.getNode(nodeKey);
                    let oldNode = item.getNode(oldNodeKey);
                    if (oldNode) {
                        item.setCurrentKey(null);
                    }
                    if (node) {
                        node.parent.expanded = true;
                        item.setCurrentNode(node);
                        item.$children.forEach((v) => {
                            v.$children.forEach((i) => {
                                if (i.node.data.key === node.data.key) {
                                    $(i.$el).addClass('bgGreen');
                                    i.$el.scrollIntoView({ block: 'center' });
                                } else {
                                    $('.bgGreen').removeClass('bgGreen');
                                }
                            });
                        });
                    }
                });
            }
        }
    },
    methods: {
        changeSpeed(item) {
            this.currentSpeed = item.value;
            this.visible = false;
        },
        search() {
            if (!this.gisLoaded) {
                this.$message('地图尚未初始化完成，请稍候重试');
                return;
            }
            if (!this.form.msisdn) {
                this.$message.error('请先输入用户号码！');
                return;
            }
            if (!this.form.time.length) {
                this.$message.error('请先选择时间！');
                return;
            }
            this.$exloading1x();
            this.getPost(
                'post',
                'getRoadLingerLocation',
                {
                    msisdn: this.form.msisdn,
                    startTime: this.form.time[0],
                    endTime: this.form.time[1]
                },
                '路网拟合轨迹查询',
                (data) => {
                    this.$exloaded1x();
                    this.initGis(data);
                }
            );
        },
        reset() {
            this.form.msisdn = '';
            this.form.time = [];
            if (this.gisLoaded) {
                this.stop();
            }
        },
        stop() {
            if (!this.isPlay) {
                return;
            }
            this.isPlay = false;
            this.trailPoints = this.deepCopy(this.reTrailData);
            if (this.trailAnimotion) {
                this.trailAnimotion.stop();
            }
            this.trailAnimotion = null;
            this.trailLayer ? this.trailLayer.removeAll() : '';

            // 重置进度条
            this.sliderValue = 0;
            this.currentTrailProgress = 0;
            this.isPlayFromMiddle = false;
        },
        gisOnLoad() {
            const gis = this.$refs['roadNetworkFitGisMap'].getEntity();
            window.gis = gis;
            // 设置底图
            if (getMayType() === 'default') {
                gis.tileLayerList['底图图层'].visible = false;
                //重写getUrl方法
                gis.tileLayerList['高德底图'].getUrl = (x, y, z) => {
                    return `${location.origin}/MapUrl/api/appmaptile?x=${x}&y=${y}&z=${z}`;
                };
            } else {
                gis.tileLayerList['高德底图'] && (gis.tileLayerList['高德底图'].visible = false);
            }
            changeGisColor(gis);

            gis.downLoad.onDownloadFinish.addOneEvent(() => {
                this.gisLoaded = true;
                //轨迹layer
                this.trailLayer = new gis.layer();
                this.trailLayer.visible = true;
                this.trailLayer.renderOrder = true;
                this.trailLayer.renderOrderIndex = 1020;
                gis.gis.scene.add(this.trailLayer.Group);
                //高亮道路layer
                this.highLightRoadLayer = new gis.layer();
                this.highLightRoadLayer.visible = true;
                gis.gis.scene.add(this.highLightRoadLayer.Group);
                //基站layer
                this.baseStationLayer = new gis.layer();
                gis.gis.scene.add(this.baseStationLayer.Group);
                this.baseStationLayer.visible = true;
                //驻点、eci点连接layer
                this.pointEciConnectLayer = new gis.layer();
                this.pointEciConnectLayer.visible = true;
                gis.gis.scene.add(this.pointEciConnectLayer.Group);
                this.walkMaterial = gis.meshList.sprite.getMaterial({
                    url: walkImg,
                    opacity: 1
                });
                this.bikeMaterial = gis.meshList.sprite.getMaterial({
                    url: bikeImg,
                    opacity: 1
                });
                this.carMaterial = gis.meshList.sprite.getMaterial({
                    url: carImg,
                    opacity: 1
                });
                this.positionMaterial = gis.meshList.img.getMaterial({
                    url: positionImg,
                    opacity: 1
                });
                this.baseStationMaterial = gis.meshList.img.getMaterial({
                    url: baseStationDir,
                    opacity: 1
                });
                this.beginMaterial = gis.meshList.img.getMaterial({
                    url: beginImg,
                    opacity: 1
                });
                this.endMaterial = gis.meshList.img.getMaterial({
                    url: endImg,
                    opacity: 1
                });
            });
        },
        roadClick(i) {
            let distance = 0; //用于计算速度，显示对应图标
            let time = 0;
            let data = this.deepCopy(this.reTrailData);
            let index = data.findIndex((v) => {
                return v.index === i;
            });
            this.pause();
            //绘制高亮道路
            if (this.highLightRoadLayer) {
                this.highLightRoadLayer.removeAll();
            }
            if (this.toolTipsDom) {
                this.toolTipsDom.remove();
            }
            if (this.pointEciConnectLayer) {
                this.pointEciConnectLayer.removeAll();
            }
            this.highLightRoadList = [];
            let points = [];
            let distancePointList = [];
            let GIS = window.gis;
            for (let item = index; item < data.length; item++) {
                if (data[item].index == i) {
                    let point = this.deepCopy(data[item]);
                    distancePointList.push(point);
                    time = point.time;
                    point = GIS.math.world2three({
                        lat: point.y,
                        lng: point.x,
                        ht: point.ht
                    });
                    points.push(point);
                } else {
                    break;
                }
            }
            this.highLightRoadList.push({
                points,
                color: 0x6680a6
            });
            distancePointList.sort((end, start) => {
                distance += GIS.math.worldDistance(start, end);
            });
            this.trailPoints = data.slice(index);

            // 标记当前是从中间位置开始播放
            this.isPlayFromMiddle = true;

            // 更新进度条位置 - 计算所点击路段在整个轨迹中的位置
            if (this.totalTrailLength > 0) {
                // 计算从起点到当前点击位置的距离
                let totalDistanceToCurrent = 0;
                for (let i = 0; i < index; i++) {
                    if (data[i + 1]) {
                        totalDistanceToCurrent += GIS.math.worldDistance(data[i], data[i + 1]);
                    }
                }
                // 更新进度条位置
                this.currentTrailProgress = (totalDistanceToCurrent / this.totalTrailLength) * 100;
                this.sliderValue = this.currentTrailProgress;
            }

            this.trailLayer ? this.trailLayer.removeAll() : '';
            this.drawRoadLine(this.highLightRoadList, this.highLightRoadLayer, 'highLightMesh');
            let runTime = time / (60 * 60 * 1000); //秒数转小时
            let peoSpeed = distance / runTime; //km/h
            let material = null;

            // 计算方向 - 获取这段路径的起点和终点来计算方向
            let startPoint = this.trailPoints[0];
            let endPoint = this.trailPoints[1] || startPoint;

            // 计算方向角度
            let deltaLng = endPoint.lng - startPoint.lng;
            let deltaLat = endPoint.lat - startPoint.lat;
            let angleRad = Math.atan2(deltaLng, deltaLat);
            let dir = (angleRad * 180) / Math.PI;
            if (dir < 0) {
                dir += 360;
            }

            let peoData = [
                {
                    lat: this.trailPoints[0].lat,
                    lng: this.trailPoints[0].lng,
                    dir: dir
                }
            ];
            window.gis.cameraControl.move({
                lat: this.trailPoints[0].lat,
                lng: this.trailPoints[0].lng
            });
            peoData.autoScale = true;
            peoData.size = 36;

            let mesh;
            // 根据usePoitionImg决定使用哪种图标
            if (this.usePoitionImg) {
                // 使用统一的位置图标
                material = this.positionMaterial;
                mesh = GIS.meshList.img.create(peoData, material);
            } else {
                // 使用原有的图标逻辑，根据速度区分
                if (peoSpeed < 10) {
                    material = this.walkMaterial;
                } else if (peoSpeed < 20) {
                    material = this.bikeMaterial;
                } else {
                    material = this.carMaterial;
                }
                mesh = GIS.meshList.sprite.create(peoData, material);
            }

            this.trailLayer.add(mesh);
            GIS.gis.needUpdate = true;
            this.trailAnimotion = null;
        },
        moveToPoint(e, item) {
            this.trailLayer ? this.trailLayer.removeAll() : '';
            this.highLightRoadLayer ? this.highLightRoadLayer.removeAll() : '';
            this.pointEciConnectLayer ? this.pointEciConnectLayer.removeAll() : '';
            window.gis.cameraControl.move(item.loc);
            let pointStart = window.gis.math.world2three({
                lat: item.loc.lat,
                lng: item.loc.lng,
                ht: item.ht
            });
            let pointEci = window.gis.math.world2three({
                lat: item.eci.latitude,
                lng: item.eci.longitude,
                ht: item.eci.height
            });
            let connectList = [
                {
                    color: '#65c1f7',
                    points: [pointStart, pointEci]
                }
            ];
            this.drawEciDiv(item.eci);
            this.drawConnectLine(connectList, this.pointEciConnectLayer);
            window.gis.cameraControl.zoomByPoints([pointStart, pointEci]);
        },
        play() {
            // 如果没有轨迹数据，则不执行播放操作
            if (!this.reTrailData || this.reTrailData.length === 0) {
                return;
            }

            this.isPlay = true;
            if (this.trailAnimotion && !this.trailAnimotion.state) {
                this.trailAnimotion.play();
                return;
            }
            this.replay();
        },
        replay() {
            // 只有当不是从中间位置播放时，才重置进度条
            if (!this.isPlayFromMiddle) {
                this.sliderValue = 0;
                this.currentTrailProgress = 0;
            } else {
                // 不重置进度条，但清除标记，下次再点击播放时应该从头开始
                this.isPlayFromMiddle = false;
            }
            this.drawSprite(this.deepCopy(this.trailPoints));
        },
        pause() {
            this.isPlay = false;
            if (this.trailAnimotion) {
                this.trailAnimotion.stop();
            }
        },
        deepCopy(obj) {
            return JSON.parse(JSON.stringify(obj));
        },
        initGis(data) {
            let GIS = window.gis;
            const afterDealData = this.dealTrailData(data);
            this.trailPoints = afterDealData;
            this.reTrailData = this.deepCopy(afterDealData);

            // 计算总轨迹长度
            this.calculateTotalTrailLength(this.reTrailData);

            this.initRoad(afterDealData);
            this.drawCircle(data);
            // this.drawBaseStation();
            GIS.gis.needUpdate = true;
            GIS.cameraControl.zoomByPoints(afterDealData, 1.2);

            // 初始化时重置标记
            this.isPlayFromMiddle = false;
        },
        // 计算总轨迹长度
        calculateTotalTrailLength(trailData) {
            this.totalTrailLength = 0;
            if (trailData.length < 2) return;

            let GIS = window.gis;
            for (let i = 0; i < trailData.length - 1; i++) {
                this.totalTrailLength += GIS.math.worldDistance(trailData[i], trailData[i + 1]);
            }
        },
        // 精灵打点
        drawSprite(data = [], speed = this.animationSpeed, m) {
            let GIS = window.gis;
            //speed 单位为米/秒
            let _self = this;
            _self.sliderShow = true;
            let layer = this.trailLayer;
            let starPosition = data.shift();
            let endPosition = data.shift();
            while (endPosition.lat === starPosition.lat && endPosition.lng === starPosition.lng) {
                endPosition = data.shift();
            }
            let peoData = [];
            let mesh;
            let animation = new GIS.animation();
            this.nowRoad = starPosition.index;
            animation.duration = (GIS.math.worldDistance(starPosition, endPosition) * 1000) / speed;
            if (animation.duration === 0) {
                data.unshift(endPosition);
                _self.drawSprite(data);
            }
            let stTime = performance.now();

            // 计算当前段轨迹在总轨迹中的比例
            const segmentDistance = GIS.math.worldDistance(starPosition, endPosition);
            const segmentRatio = _self.totalTrailLength
                ? (segmentDistance / _self.totalTrailLength) * 100
                : 0;
            let initialProgress = _self.currentTrailProgress;

            animation.onUpdate.addEvent(function (data) {
                try {
                    let nTime = performance.now();
                    if (nTime - stTime < 33) {
                        return;
                    }
                    stTime = nTime;
                    let percent = data.time / animation.duration; //动画当前进度
                    percent > 1 ? (percent = 1) : '';

                    // 更新进度条 - 仅显示进度
                    if (_self.totalTrailLength > 0) {
                        const segmentProgress = percent * segmentRatio;
                        _self.currentTrailProgress = initialProgress + segmentProgress;
                        _self.sliderValue = _self.currentTrailProgress;
                    }

                    //计算对应坐标移动距离
                    let lat = (endPosition.lat - starPosition.lat) * percent + starPosition.lat;
                    let ht = (endPosition.ht - starPosition.ht) * percent + starPosition.ht;
                    let lng = (endPosition.lng - starPosition.lng) * percent + starPosition.lng;
                    if (Number.isNaN(lat) || Number.isNaN(ht) || Number.isNaN(lng)) {
                        return;
                    }
                    window.gis.cameraControl.move({
                        lat,
                        lng,
                        ht
                    });
                    mesh ? layer.remove(mesh) : '';
                    let distance = GIS.math.worldDistance(starPosition, endPosition);
                    let runTime = starPosition.period / (60 * 60 * 1000); //秒数转小时
                    let peoSpeed = distance / runTime; //km/h
                    let material = null;

                    // 计算移动方向角度
                    // 这里lat对应y轴(纬度)，lng对应x轴(经度)
                    let deltaLng = endPosition.lng - starPosition.lng;
                    let deltaLat = endPosition.lat - starPosition.lat;

                    // 使用Math.atan2计算角度，返回值是弧度
                    let angleRad = Math.atan2(deltaLng, deltaLat);

                    // 将弧度转换为度数，并调整为地理坐标系下的角度
                    // 地理坐标中，0度表示正北方向，顺时针旋转
                    let dir = (angleRad * 180) / Math.PI;

                    // 调整角度范围为0-360度
                    if (dir < 0) {
                        dir += 360;
                    }

                    peoData = [
                        {
                            lat,
                            lng,
                            dir
                        }
                    ];
                    peoData.autoScale = true;
                    peoData.size = 36;

                    // 根据usePoitionImg变量决定使用哪种图标
                    if (_self.usePoitionImg) {
                        // 使用统一的位置图标
                        material = _self.positionMaterial;
                        mesh = GIS.meshList.img.create(peoData, material);
                    } else {
                        // 使用原有的图标逻辑，根据速度区分
                        if (peoSpeed < 10) {
                            material = _self.walkMaterial;
                        } else if (peoSpeed < 20) {
                            material = _self.bikeMaterial;
                        } else {
                            material = _self.carMaterial;
                        }
                        mesh = GIS.meshList.sprite.create(peoData, material);
                    }
                    mesh.datas = {
                        lat,
                        lng
                    };
                    if (m) {
                        layer.removeAll(m);
                    }
                    layer.add(mesh);
                } catch (e) {
                    console.log(e);
                    return;
                }
            }, 'animation');

            animation.onFinish.addEvent(function () {
                if (data.length > 0) {
                    // 更新当前累积的进度
                    initialProgress = _self.currentTrailProgress;
                    data.unshift(endPosition);
                    _self.drawSprite(data, this.animationSpeed, mesh);
                } else {
                    layer.removeAll(mesh);
                    let datas = mesh.datas;
                    let distance = GIS.math.worldDistance(starPosition, endPosition);
                    let runTime = starPosition.period / (60 * 60 * 1000); //秒数转小时
                    let peoSpeed = distance / runTime; //km/h
                    let material = null;
                    let dir = null;

                    peoData = [
                        {
                            position: GIS.math.world2three({
                                lat: endPosition.lat,
                                lng: endPosition.lng,
                                ht: endPosition.ht + 6,
                                dir
                            })
                        }
                    ];
                    peoData.autoScale = true;
                    peoData.size = 36;
                    // 根据usePoitionImg变量决定使用哪种图标
                    if (_self.usePoitionImg) {
                        // 使用统一的位置图标
                        material = _self.positionMaterial;
                        mesh = GIS.meshList.img.create(peoData, material);
                    } else {
                        // 使用原有的图标逻辑，根据速度区分
                        if (peoSpeed < 10) {
                            material = _self.walkMaterial;
                        } else if (peoSpeed < 20) {
                            material = _self.bikeMaterial;
                        } else {
                            material = _self.carMaterial;
                        }
                        mesh = GIS.meshList.sprite.create(peoData, material);
                    }

                    mesh.datas = datas;
                    layer.add(mesh);
                    _self.sliderShow = false;
                    _self.nowRoad = endPosition.index;
                    _self.stop();

                    // 完成时进度设为100%
                    _self.sliderValue = 100;
                    _self.currentTrailProgress = 100;
                }
            });

            animation.play();
            this.trailAnimotion = animation;
            _self.trailAnimotion.pointData = {
                starPosition,
                endPosition
            };
        },
        // 绘制起止点
        initStartAndEndPoint(data) {
            let GIS = window.gis;
            let points = this.deepCopy(data);
            if (!data.length) {
                return;
            }
            let list = [
                {
                    ...points[0],
                    url: require('../../../img/gis/start.png')
                },
                {
                    ...points[points.length - 1],
                    url: require('../../../img/gis/end.png')
                }
            ];
            list.forEach((item, index) => {
                const data = {
                    dom: `<div class="start-and-end-wrapper">
                            <img src="${item.url}" alt="" />
                        </div>`,
                    point: {
                        lat: item.lat,
                        lng: item.lng
                    }
                };
                data.autoScale = true;
                GIS.layerList.divLayer.addDiv(data);
            });
        },
        drawCircle(data) {
            data = data.filter((item) => item.type === 'LINGER');
            let GIS = window.gis;
            // GIS.layerList.divLayer.removeAll();
            if (!data.length) {
                return;
            }
            let iconLayer = new GIS.layer();
            iconLayer.visible = true;
            GIS.gis.scene.add(iconLayer.Group);

            data.forEach((item, index, arr) => {
                // 确定是起点还是终点或普通点
                let circleHtml = '';
                if (index === 0) {
                    // 起点
                    const begin = [];
                    begin.push({
                        lat: item.latitude,
                        lng: item.longitude,
                        ht: 2,
                        width: 65
                    });
                    begin.autoScale = true;
                    let beginMesh = GIS.meshList.img.create(begin, this.beginMaterial);
                    beginMesh.name = 'begin';
                    iconLayer.add(beginMesh);
                    GIS.gis.needUpdate = true;
                } else if (index === arr.length - 1) {
                    // 终点
                    const end = [];
                    end.push({
                        lat: item.latitude,
                        lng: item.longitude,
                        ht: 2,
                        width: 65
                    });
                    end.autoScale = true;
                    let endMesh = GIS.meshList.img.create(end, this.endMaterial);
                    endMesh.name = 'end';
                    iconLayer.add(endMesh);
                    GIS.gis.needUpdate = true;
                } else {
                    // 普通点
                    circleHtml = `<div class="cicle-wrapper">
                                    ${index + 1}
                                  </div>`;
                }

                const data = {
                    dom: circleHtml,
                    point: {
                        lat: item.latitude,
                        lng: item.longitude
                    }
                };
                data.autoScale = true;
                GIS.layerList.divLayer.addDiv(data);
            });
        },
        // 绘制道路
        initRoad() {
            let GIS = window.gis;
            if (this.roadLayer) {
                this.roadLayer.removeAll();
                this.roadLayer = null;
            }
            // 道路渲染
            this.roadLayer = new GIS.layer();
            this.roadLayer.visible = true;
            GIS.gis.scene.add(this.roadLayer.Group);
            const data = this.queryData;
            data.autoScale = true;
            data.width = 10;
            data.arrow = true;
            data.mapGap = 2;
            data.mapColor = 0xffffff;
            let roadMesh = GIS.meshList.road.create(data);
            roadMesh.name = 'roadMesh';
            this.roadLayer.add(roadMesh);
        },
        drawRoadLine(data = [], layers, name) {
            let GIS = window.gis;
            let layer = layers || new GIS.layer();
            layer.visible = true;
            if (layers) layer.visible = layers.visible;
            data.autoScale = true;
            data.width = 10;
            data.arrow = true;
            data.mapGap = 2;
            data.mapColor = 0xffffff;

            let roadMesh = GIS.meshList.road.create(data);
            roadMesh.name = name;
            layer.add(roadMesh);
            layers = layer;
        },
        //绘制基站数据
        drawBaseStation() {
            let GIS = window.gis;
            let eciDataList = [];
            let data = this.eciData;
            let _this = this;
            data.forEach((item = {}) => {
                let eciData = {};
                eciData.lat = item.latitude;
                eciData.lng = item.longitude;
                eciData.width = 40;
                eciData.dir = +item.directionAngle;
                eciData.color = '#7d7dff';
                eciData.name = `${item.latitude}${item.longitude}`;
                eciData.data = item;
                eciDataList.push(eciData);
            });
            this.eciImgDatas = eciDataList;
            eciDataList.autoScale = true;
            let stationMesh = GIS.meshList.img.create(eciDataList, this.baseStationMaterial);
            stationMesh.name = 'station';
            this.baseStationLayer.add(stationMesh);
            GIS.gis.needUpdate = true;
            GIS.event.addHover(
                this.baseStationLayer,
                (thData, event) => {
                    //清除原本存在的变色
                    for (let i = 0; i < this.eciImgDatas.length; i++) {
                        gis.meshList.img.changeColor(
                            this.baseStationLayer.Group.getObjectByName('station'),
                            i,
                            '#7d7dff'
                        );
                    }
                    //当鼠标选中目标时触发,因为用的img模型,所以对应的执行img模型构造器的hover方法,并获取目标数据序号.
                    let index = GIS.meshList.img.hover(thData, event);
                    //通过序号获取数据
                    let targetData = data[index];
                    _this.drawEciDiv(targetData);
                },
                () => {
                    //当鼠标移出对象时触发 因为上面用了hover 在这里固定执行clearHover 将高亮改回来即可.
                    GIS.meshList.img.clearHover();
                    _this.toolTipsDom ? _this.toolTipsDom.remove() : '';
                }
            );
        },
        //绘制连接线
        drawConnectLine(data = [], layers) {
            let GIS = window.gis;
            let layer = layers || new GIS.layer();
            layer.visible = true;
            if (layers) layer.visible = layers.visible;
            data.autoScale = true;
            data.width = 3;
            let roadMesh = GIS.meshList.road.create(data);
            roadMesh.name = 'connectMesh';
            layer.add(roadMesh);
            layers = layer;
        },
        drawEciDiv(eci = {}) {
            $(this.toolTipsDom) && $(this.toolTipsDom).remove();
            this.toolTipsDom ? this.toolTipsDom.remove() : '';
            // let _this=this;
            let eciId = eci.ECIID || eci.eciid;
            if (!eciId) return;
            let angle = Number(eci.directionAngle) || 0;
            angle = angle > 180 ? 360 - angle : angle;
            let boxStyle = `transform: translateX(-40%) translateY(calc(-100% - 20px + ${
                angle / 15
            }px));`;
            if (!this.eciIdList.includes(eciId)) {
                //  AjaxUtilUnicom.post("/trail/getEciName", {
                // 	eciId
                //  }).then(data => {
                // 	let point={};
                // 	point.lat=eci.latitude;
                // 	point.lng=eci.longitude;
                // 	_this.eciIdList.push(eciId);
                // 	if(!data.lacName)return;
                // 	_this.eciIdObj[eciId]=data.lacName;
                // 	//显示小区名称
                // 	let eciIdHtml=`<div class="h_25 tool_tip_content" >${data.lacName||''}</div>`;
                // 	let toolTipDiv={
                // 		dom:`<div class="tool_tip_div" style="${boxStyle}">
                // 				${eciIdHtml}
                // 			</div>`,
                // 		point,
                // 	};
                // 	this.toolTipsDom=window.gis.layerList.divLayer.addDiv(toolTipDiv);
                // 	}).catch(err => {
                // 		this.$message({
                // 			showClose: true,
                // 			message: '小区名称获取失败'
                // 		});
                // });
            } else {
                let point = {};
                point.lat = eci.latitude;
                point.lng = eci.longitude;
                if (!this.eciIdObj[eciId]) return;
                //显示小区名称
                let eciIdHtml = `<div class="h_25 tool_tip_content" >${
                    this.eciIdObj[eciId] || ''
                }</div>`;
                let toolTipDiv = {
                    dom: `<div class="tool_tip_div" style="${boxStyle}">
								${eciIdHtml}
							</div>`,
                    point
                };
                this.toolTipsDom = window.gis.layerList.divLayer.addDiv(toolTipDiv);
            }
        },
        getListFromJson(obj, k = 'points') {
            let re = [];
            let searchObj = (obj, rootIndex) => {
                let index = isNaN(rootIndex) ? undefined : rootIndex;
                if (Array.isArray(obj)) {
                    obj.forEach((item, rootIndex) => {
                        searchObj(item, index === undefined ? rootIndex : index);
                    });
                } else if (typeof obj === 'object') {
                    for (let key in obj) {
                        if (key === k) {
                            const { startTime, endTime } = obj;
                            let time = endTime - startTime; //总时长
                            let period = Number((time / obj[key].length).toFixed(3)); //每段路的平均时长
                            obj[key].forEach((v) => {
                                v.rootIndex = rootIndex;
                                v.index = obj.index;
                                v.period = period;
                                v.time = time;
                            });
                            re.push(...obj[key]);
                        } else {
                            searchObj(obj[key], rootIndex);
                        }
                    }
                } else {
                    return true;
                }
            };
            searchObj(obj);
            return re;
        },
        dobuleFn(val) {
            if (val >= 10) {
                return val;
            }
            return `0${val}`;
        },
        dealTrailData(data = []) {
            let trailData = this.getListFromJson(data).map((item) => {
                item.lng = item.x;
                item.lat = item.y;
                item.etime = 0;
                item.ht = 0;
                return item;
            });
            this.eciData = [];
            let pointCnt = 1; //驻点信息索引
            let keyId = 1;
            let GIS = window.gis;
            for (let rootIndex in data) {
                let item = data[rootIndex];
                if (item.segments) {
                    if (item.segments.length > 0) {
                        let obj = {};
                        obj.key = keyId;
                        obj.rootIndex = rootIndex;
                        keyId++;
                        let st = new Date(item.serialSTime);
                        let et = new Date(item.serialETime);
                        obj.time = item.serialSTime.toString();
                        obj.isRoad = item.type == 'ROAD' ? true : false;
                        item.type === 'ROAD'
                            ? ''
                            : (obj.loc = { lat: item.latitude, lng: item.longitude });
                        obj.label = `${item.type == 'ROAD' ? '道路序列' : '驻点'} ${this.dobuleFn(
                            st.getHours()
                        )}:${this.dobuleFn(st.getMinutes())}:${this.dobuleFn(st.getSeconds())} -
        					${this.dobuleFn(et.getHours())}:${this.dobuleFn(et.getMinutes())}:${this.dobuleFn(
                            et.getSeconds()
                        )}`;
                        for (let road of item.segments) {
                            let points = [];
                            let eci = road.eci || {};
                            let roadPoints = road.points || [];
                            this.eciData.push(eci);
                            if (roadPoints.length > 0) {
                                for (let point of roadPoints) {
                                    let p = GIS.math.world2three(point || {});
                                    points.push(p);
                                }
                                this.queryData.push({
                                    points,
                                    color: '#0095FF'
                                });
                            }
                        }
                        let groupRoad = this.groupByFiled(item.segments, 'roadName');
                        obj.children = [];
                        obj.index = item.index;
                        for (let i = 0; i < groupRoad.length; i++) {
                            let o = {};
                            let distance = 0; //用于计算速度，显示对应图标
                            o.label = `${groupRoad[i][0].roadName} ${new Date(
                                groupRoad[i][0].startTime
                            ).format('hh:mm:ss')} - ${new Date(
                                groupRoad[i][groupRoad[i].length - 1].endTime
                            ).format('hh:mm:ss')}`;
                            o.children = groupRoad[i].map((v) => {
                                let p = {};
                                p.index = v.index;
                                let st = new Date(v.startTime);
                                let et = new Date(v.endTime);
                                if (!v.points) return;
                                v.points.sort((end, start) => {
                                    distance += GIS.math.worldDistance(start, end);
                                });
                                let runTime = (v.endTime - v.startTime) / (60 * 60 * 1000); //秒数转小时
                                let peoSpeed = distance / runTime; //km/h
                                if (peoSpeed < 10) {
                                    p.url = walkImg;
                                } else if (peoSpeed < 20) {
                                    p.url = bikeImg;
                                } else {
                                    p.url = carImg;
                                }
                                p.label = `${v.roadName}-${
                                    v.roadSegmentId == null ? '无道路id' : v.roadSegmentId
                                } ${this.dobuleFn(st.getHours())}:${this.dobuleFn(
                                    st.getMinutes()
                                )}:${this.dobuleFn(st.getSeconds())} -
           					${this.dobuleFn(et.getHours())}:${this.dobuleFn(et.getMinutes())}:${this.dobuleFn(
                                    et.getSeconds()
                                )}`;
                                p.key = keyId;
                                this.keyIndex[p.index] = p.key;
                                keyId++;
                                return p;
                            });
                            o.key = keyId;
                            keyId++;
                            obj.children.push(o);
                        }
                        this.treeData.push(obj);
                    }
                } else {
                    let obj = {};
                    obj.key = keyId;
                    obj.showPointInfo = false;
                    obj.eci = item.eci || {};
                    this.eciData.push(obj.eci);
                    keyId++;
                    let st = new Date(item.serialSTime);
                    let et = new Date(item.serialETime);
                    obj.time = item.serialSTime.toString();
                    obj.isRoad = item.type == 'ROAD' ? true : false;
                    obj.pointCnt = pointCnt++;
                    item.type === 'ROAD'
                        ? ''
                        : (obj.loc = { lat: item.latitude, lng: item.longitude });
                    obj.label = `${item.type == 'ROAD' ? '道路序列' : '驻点'} ${this.dobuleFn(
                        st.getHours()
                    )}:${this.dobuleFn(st.getMinutes())}:${this.dobuleFn(st.getSeconds())} -
        				${this.dobuleFn(et.getHours())}:${this.dobuleFn(et.getMinutes())}:${this.dobuleFn(
                        et.getSeconds()
                    )}`;
                    this.treeData.push(obj);
                    obj.index = item.index;
                }
            }
            return trailData;
        },
        //合并道路
        groupByFiled(arry, field) {
            let obj = [];
            let last = arry[0];
            let temp = [];
            for (let i = 0; i < arry.length; i++) {
                if (arry[i][field] === last[field]) {
                    //如果当前路和上一条一样就合并
                    if (i == arry.length - 1) {
                        //合并到最后一个还是一样,直接返回给obj
                        temp.push(arry[i]);
                        obj.push(JSON.parse(JSON.stringify(temp)));
                        temp = [];
                    } else {
                        temp.push(arry[i]);
                    }
                } else {
                    if (temp.length > 0) obj.push(JSON.parse(JSON.stringify(temp)));
                    temp = [arry[i]];
                }
                last = arry[i];
            }
            return obj;
        }
    }
};
</script>

<style lang="less" scoped>
@import url('../../../style/custom.less');
.road-network-fit {
    width: 100%;
    height: 100%;
    position: relative;
    &-search {
        width: 100%;
        display: flex;
        align-items: center;
        .search-bar {
            width: 1000px;
            margin-left: auto;
            /deep/.displayPass {
                .el-input__inner {
                    -webkit-text-security: disc !important;
                    background-color: transparent;
                    border: 1px solid rgba(18, 139, 207, 0.6);
                    color: #fff;
                }
            }
        }
    }
    .gis {
        width: 100%;
        height: calc(100% - 80px);
    }
    .play-detail {
        position: absolute;
        right: 13px;
        top: 96px;
        width: 22.22rem;
        height: calc(100% - 109px);
        // box-shadow: 0px 0px 5px 1px rgba(13, 59, 128, 0.4);
        // border-radius: 4px;
        background: rgba(0, 42, 92, 0.9);
        border: 1px solid rgba(0, 149, 255, 0.5);
        z-index: 101;
        padding: 16px 16px 16px 25px;
    }
    .tools-wrapper {
        position: absolute;
        bottom: 13px;
        left: 13px;
        width: 82.22rem;
        height: 64px;
        padding: 0 24px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: rgba(0, 42, 92, 0.9);
        border-radius: 2px;
        border: 1px solid rgba(0, 149, 255, 0.5);
        z-index: 101;
        .btn-wrapper {
            padding-left: 2.22rem;
            display: flex;
            align-items: center;
            .btn-content-center {
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }
        .slider-wrapper {
            width: 0;
            flex: 1;
            /deep/&.el-slider {
                .el-slider__button {
                    // background-color: #409eff;
                    // box-shadow: 0px 4px 11px 4px rgba(0, 0, 0, 0.08),
                    //     0px 2px 4px 0px rgba(0, 0, 0, 0.12), 0px 1px 2px -2px rgba(0, 0, 0, 0.16);
                    background: transparent;
                }
                // .el-slider__stop {
                //     background-color: #409eff;
                //     height: 12px;
                //     width: 12px;
                //     border: 1px solid #fff;
                //     transform: translate(-50%, -30%);
                // }
                // .el-slider__marks-text {
                //     font-size: 12px;
                //     color: #262626;
                // }
                .el-slider__button-wrapper {
                    z-index: 101;
                }
            }
            /deep/.el-slider__runway {
                background: rgba(0, 149, 255, 0.2);
                border-radius: 3px;

                .el-slider__bar {
                    background: linear-gradient(270deg, #00f1ff 0%, #0095ff 100%);
                    border-radius: 3px;
                }
                .el-slider__button {
                    display: none;
                    width: 12px;
                    height: 12px;
                    background-color: #409eff !important;
                }
            }
        }

        /deep/.custom-slider-disabled {
            &.el-slider.is-disabled {
                cursor: default;

                .el-slider__runway {
                    cursor: default;
                }

                .el-slider__button-wrapper {
                    cursor: default;
                }
            }
        }
    }
}
/deep/.cicle-wrapper {
    transform: translate(-50%, -50%);
    border: 2px solid #d4e9ff;
    border-radius: 100px;
    width: 22px;
    height: 22px;
    font-size: 12px;
    display: inline-block;
    text-align: center;
    vertical-align: bottom;
    color: white;
    background: linear-gradient(90deg, #2a93ff 0%, #409eff 100%);
    box-shadow: 0px 2px 5px 1px rgba(11, 94, 255, 0.3);

    &.start-point,
    &.end-point {
        background: none;
        border: none;
        width: 44px;
        height: 44px;

        img {
            width: 100%;
            height: 100%;
        }
    }
}
/deep/.start-and-end-wrapper {
    transform: translate(-50%, -50%);
    img {
        width: 44px;
        height: 44px;
    }
}
.btn-stop-icon {
    width: 14px;
    height: 14px;
    vertical-align: middle;
    margin-right: 5px;
    margin-top: -2px;
}
.speed-btn {
    background: rgba(0, 149, 255, 0.3);
    width: 64px;
    height: 32px;
    margin-right: 10px;
    border-radius: 0.11rem;
    cursor: pointer;
    position: relative;
    &:hover {
        opacity: 0.7;
    }
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    border: none;
    padding: 9px 15px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}
.speed-popover-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;
    color: #fff;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 14px;
    &-item {
        width: 100%;
        padding: 5px;
        cursor: pointer;
        &:hover {
            background: rgba(0, 149, 255, 0.4);
            border-radius: 4px;
        }
    }
    .active {
        background: rgba(0, 149, 255, 0.4);
        border-radius: 4px;
    }
}
</style>
<style lang="less">
.speed-popover {
    min-width: 64px;
    background: rgba(0, 74, 140, 0.9);
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    border: 1px solid rgba(0, 149, 255, 0.5);
    padding: 6px;
    &[x-placement^='top'] .popper__arrow {
        display: none;
    }
}
</style>
