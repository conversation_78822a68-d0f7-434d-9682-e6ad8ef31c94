<template>
    <div class="bottom-content">
        <div class="bottom-content-item" v-for="(item, index) in bottomList" :key="index">
            <div class="bottom-content-item-icon">
                <img :src="item.icon" alt="" />
            </div>
            <div class="bottom-content-item-label">
                <span class="bottom-content-item-label-value">{{ item.value }}</span>
                <span class="bottom-content-item-label-label">{{ item.label }}</span>
            </div>
        </div>
    </div>
</template>

<script>
import commonMixins from '@/script/mixins/commonMixins.js';

export default {
    name: 'BottomData',
    mixins: [commonMixins],
    components: {},
    data() {
        return {
            bottomList: [
                {
                    icon: require('../../../../img/effectivenessScreen/bottom-icon-1.png'),
                    label: '常驻用户数(万人)',
                    value: '-',
                    field: 'residentNum'
                },
                {
                    icon: require('../../../../img/effectivenessScreen/bottom-icon-2.png'),
                    label: '定位输出流量(万条)',
                    value: '-',
                    field: 'locCount'
                },
                {
                    icon: require('../../../../img/effectivenessScreen/bottom-icon-3.png'),
                    label: '4G小区数(万个)',
                    value: '-',
                    field: 'count4g'
                },
                {
                    icon: require('../../../../img/effectivenessScreen/bottom-icon-4.png'),
                    label: '5G小区数(万个)',
                    value: '-',
                    field: 'count5g'
                }
            ]
        };
    },
    created() {
        this.getBottomData();
    },
    methods: {
        getBottomData() {
            this.getPost(
                'post',
                'residentPositionLac',
                {},
                '能效展板-常驻用户数、定位输出流量、4/5G小区数查询',
                (res) => {
                    this.bottomList.forEach((item) => {
                        const value = res[item.field] / 10000;
                        if (value === 0) {
                            item.value = 0;
                        } else if (Math.floor(value) === 0) {
                            item.value = value.toString();
                        } else {
                            item.value = value.toFixed(2);
                        }
                    });
                }
            );
        }
    }
};
</script>

<style lang="less" scoped>
.bottom-content {
    width: 72%;
    height: fit-content;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .bottom-content-item {
        height: 4.6667rem;
        display: flex;
        align-items: center;
        gap: 0.5556rem;
        .bottom-content-item-icon {
            width: 4.6667rem;
            height: 4.6667rem;
            img {
                width: 100%;
                height: 100%;
            }
        }
        .bottom-content-item-label {
            height: 100%;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 0.2222rem;
            &-value {
                font-family: DIN, DIN;
                font-weight: bold;
                font-size: 1.6rem;
                color: #19ebff;
            }
            &-label {
                font-family: PingFangSC, PingFang SC;
                font-weight: 600;
                font-size: 0.7778rem;
                color: #b0d7ff;
            }
        }
    }
}
</style>
