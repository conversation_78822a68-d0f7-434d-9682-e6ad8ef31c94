<template>
    <div class="road-network-fit">
        <div class="road-network-fit-search">
            <searchBar class="search-bar" ref="formList" :formCols="formCols" :form="form">
                <template #search>
                    <el-button type="primary" size="small" @click="search">查询</el-button>
                </template>
            </searchBar>
            <div class="tools-wrapper">
                <el-slider
                    class="slider-wrapper"
                    v-model="sliderValue"
                    :step="20"
                    :marks="marks"
                    show-stops
                    :format-tooltip="formatTooltip"
                    >
                </el-slider>
                <div class="btn-wrapper">
                    <el-button v-if="!isPlay" class="sd-devops-btn--small sd-devops-btn--success" type="success" size="small" icon="el-icon-video-play" @click="play">播放</el-button>
                    <el-button v-else class="sd-devops-btn--small" type="warning" size="small" icon="el-icon-video-pause" @click="pause">暂停</el-button>
                    <el-button class="sd-devops-btn--small " type="danger" size="small" @click="stop">停止</el-button>
                </div>
            </div>
        </div>
        <mtv-gis
            class="gis"
            ref="roadNetworkFitGisMap"
            :totaloptions="gistotalOptions"
            @onLoad="gisOnLoad"
        ></mtv-gis>
        <!-- 右侧播放详情 -->
        <div class="play-detail sd-devops-overflow-y--grey">
            <roadStep ref="roadStep" :data="treeData" @roadClick="roadClick"></roadStep>
        </div>
    </div>
</template>
<!-- eslint-disable max-depth -->
<!-- eslint-disable complexity -->
<script>
import searchBar from '_com/searchBar/searchBar.vue';
import roadStep from './components/roadStep.vue';
// import dataTest from './dataTest/data.json';
import walkImg from '../../../img/gis/walk_big.png';
import bikeImg from '../../../img/gis/bicycle_big.png';
import carImg from '../../../img/gis/car_big.png';
import baseStationDir from '../../../img/gis/baseStationDir.png';
import commonMixins from '@/script/mixins/commonMixins.js';
import {gistotalOptions,getMayType} from '@/script/constant/gis.js';
export default {
    name:'roadNetworkFit',
    components:{
        searchBar,
        roadStep
    },
	mixins:[commonMixins],
    data(){
        return{
            walkImg,
            bikeImg,
            carImg,
            baseStationDir,
            formCols:[
                [
                    {
                        type: 'el-input',
                        prop: 'msisdn',
                        label: '用户号码：',
                        labelWidth: '85px',
                        attrs:{
                            placeholder:'请输入',
							clearable:true,
							class:'displayPass'
                        },
                        rules: [
                            // { required: true, message: '必填', trigger: 'submit' }
                        ],
                        span: 7,
                        isDisabled: false,
                        isShow: true,
                        opts: [],
                    },
                    {
                        type: 'el-date-picker',
                        prop: 'time',
                        label: '日期选择：',
                        labelWidth: '100px',
                        attrs:{
                            'start-placeholder':'请选择开始时间',
                            'end-placeholder':'请选择结束时间',
                            type:'datetimerange',
                            clearable:true,
                            'value-format':'yyyy-MM-dd HH:mm:ss',
							'default-time':['00:00:00','23:59:59']
                        },
                        rules: [
                            // { required: true, message: '必填', trigger: 'submit' }
                        ],
                        span: 13,
                        isDisabled: false,
                        isShow: true,
                        opts: [],
                    },
                    {
                        type:'tool',
                        prop: 'tool',
                        span: 4,
                        isShow: true,
                        labelWidth: '30px',
                    }
                ],
            ],
            form:{
                msisdn:'',
                time:[]
            },
            gistotalOptions:gistotalOptions,
            gisLoaded:false,
            sliderValue:0,
            marks:{
                0:'300m',
                20:'600m',
                40:'900m',
                60:'1200m',
                80:'1500m',
                100:'1800m',
            },
            isPlay:false,
            treeData:[],
            queryData:[],
            keyIndex:{},
            roadLayer:null,
            allLayer:null,
            trailLayer:null,
            trailPoints:[],
            reTrailData:[],
            sliderShow:false,
            nowRoad:null,
            trailAnimotion:null,
            walkMaterial:null, //步行图标
			bikeMaterial:null, //自行车图标
			carMaterial: null,  //汽车图标
            highLightRoadLayer:null,  //高亮道路图层
			highLightRoadList:[],  //高亮道路数组
            baseStationMaterial:null, //基站图层图标
            toolTipsDom:'',
            baseStationLayer:null,//基站图层
            pointEciConnectLayer:null,//驻点和eci小区连接高亮线
            eciIdList:[],  //小区名称id的数组
        };
    },
    computed:{
        animationSpeed(){
            return this.marks[this.sliderValue].split('m')[0];
        }
    },
    watch:{
       nowRoad(newVal,oldVal){
			let nodeKey = this.keyIndex[newVal];
			let oldNodeKey = this.keyIndex[oldVal];
			if(nodeKey||oldNodeKey){
				this.$refs['roadStep'].$refs['key'].forEach(item=>{
					let node= item.getNode(nodeKey);
					let oldNode = item.getNode(oldNodeKey);
					if(oldNode){
						item.setCurrentKey(null);
					}
					if(node){
						node.parent.expanded=true;
						item.setCurrentNode(node);
						item.$children.forEach(v =>{
							v.$children.forEach(i =>{
								if(i.node.data.key === node.data.key){
									$(i.$el).addClass('bgGreen');
									i.$el.scrollIntoView({block:'center'});
								}else {
									$('.bgGreen').removeClass('bgGreen');
								}
							});
						});
					}
				});
			}
		}
    },
    methods:{
        search(){
			if(!this.gisLoaded){
                this.$message('地图尚未初始化完成，请稍候重试');
                return;
            }
            if(!this.form.msisdn){
                this.$message.error('请先输入用户号码！');
                return;
            }
            if(!this.form.time.length){
                this.$message.error('请先选择时间！');
                return;
            }
            this.$exloading1x();
            this.getPost('post','getRoadLingerLocation',{
                msisdn:this.form.msisdn,
                startTime:this.form.time[0],
                endTime:this.form.time[1],
            },'路网拟合轨迹查询',(data) => {
                this.$exloaded1x();
				this.initGis(data);
            });
        },
        stop(){
            this.isPlay = false;
            this.trailPoints = this.deepCopy(this.reTrailData);
            this.trailAnimotion.stop();
            this.trailAnimotion = null;
            this.trailLayer ? this.trailLayer.removeAll() : '';
        },
        gisOnLoad(){
            const gis = this.$refs['roadNetworkFitGisMap'].getEntity();
            window.gis = gis;
			// 设置底图
            if (getMayType() === 'default') {
                gis.tileLayerList['底图图层'].visible = false;
                //重写getUrl方法
                gis.tileLayerList['高德底图'].getUrl = (x, y, z) => {
                    return `${location.origin}/MapUrl/api/appmaptile?x=${x}&y=${y}&z=${z}`;
                };
            } else {
                gis.tileLayerList['高德底图'] &&
                    (gis.tileLayerList['高德底图'].visible = false);
            }
            gis.downLoad.onDownloadFinish.addOneEvent(() => {
                this.gisLoaded = true;
                //轨迹layer
				this.trailLayer = new gis.layer();
				this.trailLayer.visible = true;
				this.trailLayer.renderOrder = true;
				this.trailLayer.renderOrderIndex = 1020;
                gis.gis.scene.add(this.trailLayer.Group);
                //高亮道路layer
				this.highLightRoadLayer=new gis.layer();
				this.highLightRoadLayer.visible = true;
				gis.gis.scene.add(this.highLightRoadLayer.Group);
                //基站layer
				this.baseStationLayer = new gis.layer();
				gis.gis.scene.add(this.baseStationLayer.Group);
				this.baseStationLayer.visible = true;
                //驻点、eci点连接layer
				this.pointEciConnectLayer=new gis.layer();
				this.pointEciConnectLayer.visible = true;
				gis.gis.scene.add(this.pointEciConnectLayer.Group);
                this.walkMaterial = gis.meshList.sprite.getMaterial({
					url: walkImg , opacity: 1
				});
				this.bikeMaterial = gis.meshList.sprite.getMaterial({
					url: bikeImg , opacity: 1
				});
				this.carMaterial = gis.meshList.sprite.getMaterial({
					url: carImg , opacity: 1
				});
                this.baseStationMaterial = 	gis.meshList.img.getMaterial({
					url:baseStationDir, opacity: 1});
            });
        },
        roadClick(i){
            let distance = 0 ;//用于计算速度，显示对应图标
			let time = 0;
            let data = this.deepCopy(this.reTrailData);
			let index = data.findIndex(v => {
				return v.index===i;
			});
            this.pause();
            //绘制高亮道路
			if(this.highLightRoadLayer){
				this.highLightRoadLayer.removeAll();
			}
            if(this.toolTipsDom){
				this.toolTipsDom.remove();
			}
			if(this.pointEciConnectLayer){
				this.pointEciConnectLayer.removeAll();
			}
            this.highLightRoadList=[];  
			let points=[];
            let distancePointList=[];
            let GIS = window.gis;
			for(let item=index;item<data.length;item++){
				if(data[item].index==i){
					let point=this.deepCopy(data[item]);
					distancePointList.push(point);
					time = point.time;
					point=GIS.math.world2three({
						lat:point.y,
						lng:point.x,
						ht:point.ht
					});
					points.push(point);
				}else{
					break;
				}
			}
			this.highLightRoadList.push(
				{
					points,
					color: 0x409EFF
				}
			);
            distancePointList.sort((end,start)=>{
					distance+= GIS.math.worldDistance(start, end);
				});
            this.trailPoints = data.slice(index);
            this.trailLayer ? this.trailLayer.removeAll() : '';
            this.drawRoadLine(this.highLightRoadList, this.highLightRoadLayer ,'highLightMesh');
            let runTime = time / (60*60*1000); //秒数转小时
			let peoSpeed = distance / runTime; //km/h
			let material=null;
			if(peoSpeed<10){
				material=this.walkMaterial;
			}else if ( peoSpeed<20 ){
				material = this.bikeMaterial;
			}else{
				material = this.carMaterial;
			}
			let peoData = [{
					lat:this.trailPoints[0].lat,
					lng:this.trailPoints[0].lng,
				}];
			window.gis.cameraControl.move({
				lat:this.trailPoints[0].lat,
				lng:this.trailPoints[0].lng,
			});
			peoData.autoScale = true;
            peoData.size = 36;
			let mesh=GIS.meshList.sprite.create(peoData, material);
            this.trailLayer.add(mesh);
			GIS.gis.needUpdate = true;
            this.trailAnimotion = null;
        },
        moveToPoint(e,item){
			this.trailLayer ? this.trailLayer.removeAll() : '';
			this.highLightRoadLayer? this.highLightRoadLayer.removeAll() : '';
			this.pointEciConnectLayer? this.pointEciConnectLayer.removeAll() : '';
			window.gis.cameraControl.move(item.loc);
			let pointStart=window.gis.math.world2three({
						lat:item.loc.lat,
						lng:item.loc.lng,
						ht:item.ht
			});
			let pointEci=window.gis.math.world2three({
						lat:item.eci.latitude,
						lng:item.eci.longitude,
						ht:item.eci.height,
			});
			let connectList=[{
				color:'#65c1f7',
				points:[pointStart,pointEci]
			}];
			this.drawEciDiv(item.eci);
			this.drawConnectLine(connectList,this.pointEciConnectLayer);
			window.gis.cameraControl.zoomByPoints([pointStart,pointEci]);
        },
        play(){
            this.isPlay = true;
            if(this.trailAnimotion && !this.trailAnimotion.state){
                this.trailAnimotion.play();
                return;
            }
            this.replay();
        },
        replay(){
            this.drawSprite(this.deepCopy(this.trailPoints));
        },
        pause(){
            this.isPlay = false;
            if(this.trailAnimotion){
                this.trailAnimotion.stop();
            }
        },
        deepCopy(obj){
			return JSON.parse(JSON.stringify(obj));
		},
        initGis(data){
            let GIS = window.gis;
            const afterDealData = this.dealTrailData(data);
            this.trailPoints = afterDealData;
            this.reTrailData = this.deepCopy(afterDealData);
            this.initRoad(afterDealData);
            this.drawCircle(data);
			// this.drawBaseStation();
            GIS.gis.needUpdate = true;
            GIS.cameraControl.zoomByPoints(afterDealData,1.2);
        },
        formatTooltip(val){
            return this.marks[val] + '/每秒';
        },
        // 精灵打点
        drawSprite(data = [], speed = this.animationSpeed, m){
            let GIS = window.gis;
            //speed 单位为米/秒
			let _self = this;
			_self.sliderShow = true;
			let layer = this.trailLayer;
			let starPosition = data.shift();
			let endPosition = data.shift();
			while(endPosition.lat===starPosition.lat&&endPosition.lng===starPosition.lng){
				endPosition=data.shift();
			}
			let peoData = [];
			let mesh;
			let animation = new GIS.animation();
			this.nowRoad = starPosition.index;
			animation.duration = GIS.math.worldDistance(starPosition, endPosition) * 1000 / speed ;
			if(animation.duration === 0) {
				data.unshift(endPosition);
				_self.drawSprite(data);
			}
			let stTime = performance.now();
			animation.onUpdate.addEvent(function(data) {
				try{
				let nTime = performance.now();
				if(nTime - stTime < 33) {
					return;
				}
                stTime = nTime;
				let percent = data.time / animation.duration; //动画当前进度
				percent > 1 ? percent = 1 : '';
				//计算对应坐标移动距离
				let lat = (endPosition.lat - starPosition.lat) * percent + starPosition.lat;
				let ht = (endPosition.ht - starPosition.ht) * percent + starPosition.ht;
				let lng = (endPosition.lng - starPosition.lng) * percent + starPosition.lng;
				if((Number.isNaN(lat)||Number.isNaN(ht)||Number.isNaN(lng))){
					return;
				}
				window.gis.cameraControl.move({
					lat,
					lng,
					ht
				});
				mesh ? layer.remove(mesh) : '';
				let distance = GIS.math.worldDistance(starPosition, endPosition);
				let runTime = starPosition.period / (60*60*1000); //秒数转小时
				let peoSpeed = distance / runTime; //km/h
				let material=null;
				let dir=null;
				if(peoSpeed<10){
					material =  _self.walkMaterial;
				}else if ( peoSpeed<20 ){
					material = _self.bikeMaterial;
				}else{
					material =_self.carMaterial;
				}
				peoData = [{
					lat,
					lng,
					dir
				}];
				peoData.autoScale = true;
				peoData.size = 36;
				mesh = GIS.meshList.sprite.create(peoData, material);
				mesh.datas = {
					lat,
					lng
				};
				if(m){
					layer.removeAll(m);
					}
				layer.add(mesh);
				}catch (e) {
					console.log(e);
					return;
				}
			}, 'animation');
			animation.onFinish.addEvent(function() {
				if(data.length > 0) {
					data.unshift(endPosition);
					_self.drawSprite(data,this.animationSpeed,mesh);
				} else {
					layer.removeAll(mesh);
					let datas = mesh.datas;
					let distance = GIS.math.worldDistance(starPosition, endPosition);
					let runTime = starPosition.period / (60*60*1000); //秒数转小时
					let peoSpeed = distance / runTime; //km/h
					let material=null;
					let dir=null;
					if(peoSpeed<10){
						material =  _self.walkMaterial;
					}else if ( peoSpeed<20 ){
						material = _self.bikeMaterial;
					}else{
						material = _self.carMaterial;
					}
					peoData = [{
						position: GIS.math.world2three({
							lat: endPosition.lat,
							lng: endPosition.lng,
							ht: endPosition.ht+6,
							dir
						}),
					}];
					peoData.autoScale = true;
					peoData.size = 36;
					mesh = GIS.meshList.sprite.create(peoData, material);
					mesh.datas = datas;
					layer.add(mesh);
					_self.sliderShow = false;
					_self.nowRoad = endPosition.index;
					_self.stop();
				}
			});
			animation.play();
			this.trailAnimotion = animation;
			_self.trailAnimotion.pointData = {
				starPosition,
				endPosition
			};

        },
        // 绘制起止点
        initStartAndEndPoint(data){
            let GIS = window.gis;
            let points = this.deepCopy(data);
            if(!data.length){
                return;
            }
            let list =  [{
                ...points[0],
                url:require('../../../img/gis/start.png'),
            },{
                ...points[points.length -1],
                url:require('../../../img/gis/end.png'),
            }];
            list.forEach((item,index) => {
                const data = {
                    dom: `<div class="start-and-end-wrapper">
                            <img src="${item.url}" alt="" />
                        </div>`,
                    point: {
                        lat: item.lat,
                        lng: item.lng,
                    }
                };
                data.autoScale = true;
                GIS.layerList.divLayer.addDiv(data);
            });
        },
        drawCircle(data){
			data=data.filter(item => item.type==='LINGER');
			let GIS=window.gis;
            // GIS.layerList.divLayer.removeAll();
			if(!data.length){
                return;
            }
            data.forEach((item,index) => {
                const data = {
                    dom: `<div class="cicle-wrapper">
                            ${index+1}
                        </div>`,
                    point: {
                        lat: item.latitude,
                        lng: item.longitude,
                    }
                };
                data.autoScale = true;
                GIS.layerList.divLayer.addDiv(data);
            });
		},
        // 绘制道路
        initRoad(){
            let GIS = window.gis;
            if(this.roadLayer){
                this.roadLayer.removeAll();
                this.roadLayer = null;
            }
            // 道路渲染
            this.roadLayer = new GIS.layer();
            this.roadLayer.visible = true;
            GIS.gis.scene.add(this.roadLayer.Group);
            const data = this.queryData;
            data.autoScale = true;
			data.width = 10;
			data.arrow = true;
			let roadMesh = GIS.meshList.road.create(data);
			roadMesh.name='roadMesh';
			this.roadLayer.add(roadMesh);
        },
        drawRoadLine(data = [
			], layers ,name) {
			let GIS = window.gis;
			let layer = layers || new GIS.layer();
			layer.visible = true;
			if (layers) layer.visible = layers.visible;
			data.autoScale = true;
			data.width = 10;
			data.arrow = true;

			let roadMesh = GIS.meshList.road.create(data);
			roadMesh.name=name;
			layer.add(roadMesh);
			layers = layer;
		},
        //绘制基站数据
		drawBaseStation(){
			let GIS=window.gis;
			let eciDataList=[];
			let data=this.eciData;
			let _this=this;
			data.forEach((item={})=>{
				let eciData={};
				eciData.lat=item.latitude;
				eciData.lng=item.longitude;
				eciData.width=40;
				eciData.dir=+(item.directionAngle);
				eciData.color='#7d7dff';
				eciData.name=`${item.latitude}${item.longitude}`;
				eciData.data=item;
				eciDataList.push(eciData);
			});
			this.eciImgDatas=eciDataList;
			eciDataList.autoScale=true;
			let stationMesh = GIS.meshList.img.create(eciDataList, this.baseStationMaterial);
			stationMesh.name='station';
			this.baseStationLayer.add(stationMesh);
			GIS.gis.needUpdate = true;
			GIS.event.addHover(this.baseStationLayer,(thData,event)=>{
			//清除原本存在的变色
			for(let i=0;i<this.eciImgDatas.length;i++){
				gis.meshList.img.changeColor(this.baseStationLayer.Group.getObjectByName('station'),i,'#7d7dff');
			}
			//当鼠标选中目标时触发,因为用的img模型,所以对应的执行img模型构造器的hover方法,并获取目标数据序号.
				let index = GIS.meshList.img.hover(thData,event);
			//通过序号获取数据
				let targetData = data[index];
				_this.drawEciDiv(targetData);
			},()=>{
			//当鼠标移出对象时触发 因为上面用了hover 在这里固定执行clearHover 将高亮改回来即可.
				GIS.meshList.img.clearHover();
				_this.toolTipsDom?_this.toolTipsDom.remove():'';
			});
		},
        //绘制连接线
		drawConnectLine(data = [
		], layers) {
			let GIS = window.gis;
			let layer = layers || new GIS.layer();
			layer.visible = true;
			if (layers) layer.visible = layers.visible;
			data.autoScale = true;
			data.width = 3;
			let roadMesh = GIS.meshList.road.create(data);
			roadMesh.name='connectMesh';
			layer.add(roadMesh);
			layers = layer;
		},
        drawEciDiv(eci={}){
			$(this.toolTipsDom)&&$(this.toolTipsDom).remove();
			this.toolTipsDom ? this.toolTipsDom.remove() : '';
			// let _this=this;
			let eciId=eci.ECIID||eci.eciid;
			if(!eciId)return; 
			let angle=Number(eci.directionAngle)||0;
			angle=angle>180?360-angle:angle;
			let boxStyle=`transform: translateX(-40%) translateY(calc(-100% - 20px + ${angle/15}px));`;
			if(!this.eciIdList.includes(eciId)){
				//  AjaxUtilUnicom.post("/trail/getEciName", {
				// 	eciId
				//  }).then(data => {
				// 	let point={};
				// 	point.lat=eci.latitude;
				// 	point.lng=eci.longitude;
				// 	_this.eciIdList.push(eciId);
				// 	if(!data.lacName)return;
				// 	_this.eciIdObj[eciId]=data.lacName;
				// 	//显示小区名称
				// 	let eciIdHtml=`<div class="h_25 tool_tip_content" >${data.lacName||''}</div>`;
				// 	let toolTipDiv={
				// 		dom:`<div class="tool_tip_div" style="${boxStyle}">
				// 				${eciIdHtml}
				// 			</div>`,
				// 		point,
				// 	};
				// 	this.toolTipsDom=window.gis.layerList.divLayer.addDiv(toolTipDiv);	
				// 	}).catch(err => {
				// 		this.$message({
				// 			showClose: true,
				// 			message: '小区名称获取失败'
				// 		});
				// });
			}else{
				let point={};
					point.lat=eci.latitude;
					point.lng=eci.longitude;
					if(!this.eciIdObj[eciId])return;
					//显示小区名称
					let eciIdHtml=`<div class="h_25 tool_tip_content" >${this.eciIdObj[eciId]||''}</div>`;
					let toolTipDiv={
						dom:`<div class="tool_tip_div" style="${boxStyle}">
								${eciIdHtml}
							</div>`,
						point,
					};
					this.toolTipsDom=window.gis.layerList.divLayer.addDiv(toolTipDiv);	
			}
		},
        getListFromJson(obj, k = 'points') {
			let re = [];
			let searchObj = (obj,rootIndex) => {
					let index = isNaN(rootIndex)?undefined:rootIndex;
					if(Array.isArray(obj)) {
						obj.forEach((item,rootIndex) => {
							searchObj(item,index===undefined?rootIndex:index);
						});
					} else if(typeof obj === 'object') {
						for(let key in obj) {
							if(key === k) {
								const { startTime, endTime } = obj;
								let time =   endTime - startTime; //总时长
								let period = Number( ( time / obj[key].length ).toFixed(3) ); //每段路的平均时长
								obj[key].forEach(v=>{
									v.rootIndex=rootIndex;
									v.index = obj.index;
									v.period = period;
									v.time=time;
								});
								re.push(...obj[key]);
							} else {
								searchObj(obj[key],rootIndex);
							}
						}
					} else {
						return true;
					}
				};
			searchObj(obj);
			return re;
		},
        dobuleFn(val) {
			if(val >= 10) {
				return val;
			}
			return `0${val}`;
		},
        dealTrailData(data = []) {
			let trailData = this.getListFromJson(data).map(item => {
				item.lng = item.x;
				item.lat = item.y;
				item.etime = 0;
				item.ht = 0;
				return item;
			});
			this.eciData=[];
			let pointCnt=1; //驻点信息索引
			let keyId=1;
			let GIS=window.gis;
			for(let rootIndex in data){
                let item = data[rootIndex];
				if(item.segments){
					if(item.segments.length>0){
						let obj = {};
						obj.key = keyId;
                        obj.rootIndex = rootIndex;
						keyId++;
						let st = new Date(item.serialSTime);
						let et = new Date(item.serialETime);
						obj.time = item.serialSTime.toString();
						obj.isRoad = item.type == 'ROAD' ? true : false;
						item.type === 'ROAD' ?'':obj.loc={lat:item.latitude,lng:item.longitude};
						obj.label = `${item.type == 'ROAD' ? '道路序列' : '驻点'} ${this.dobuleFn(st.getHours())}:${this.dobuleFn(st.getMinutes())}:${this.dobuleFn(st.getSeconds())} -
        					${this.dobuleFn(et.getHours())}:${this.dobuleFn(et.getMinutes())}:${this.dobuleFn(et.getSeconds())}`;
						for(let road of item.segments){
							let points=[];
							let eci=road.eci||{};
							let roadPoints=road.points||[];
							this.eciData.push(eci);
							if(roadPoints.length>0){
								for(let point of roadPoints){
                                    let p=GIS.math.world2three(point||{});
                                    points.push(p);
                                }
                                this.queryData.push({
                                    points,
                                    color: '#FF9B00'
                                });
                            }
						}
						let groupRoad = this.groupByFiled(item.segments,'roadName');
						obj.children = [];
						obj.index=item.index;
						for(let i=0;i<groupRoad.length;i++){
							let o = {};
                            let distance = 0 ;//用于计算速度，显示对应图标
							o.label = `${groupRoad[i][0].roadName} ${new Date(groupRoad[i][0].startTime).format('hh:mm:ss')} - ${new Date(groupRoad[i][groupRoad[i].length-1].endTime).format('hh:mm:ss')}`;
							o.children = groupRoad[i].map((v => {
							let p = {};
							p.index = v.index;
							let st = new Date(v.startTime);
							let et = new Date(v.endTime);
							if(!v.points)return;
							v.points.sort((end,start)=>{
								distance+= GIS.math.worldDistance(start, end);
							});
							let runTime = ( v.endTime - v.startTime ) / (60*60*1000); //秒数转小时
							let peoSpeed = distance / runTime; //km/h
							if(peoSpeed<10){
								p.url=walkImg;
							}else if ( peoSpeed<20 ){
								p.url=bikeImg;
							}else{
								p.url=carImg;
							}
							p.label = `${v.roadName}-${v.roadSegmentId==null?'无道路id':v.roadSegmentId} ${this.dobuleFn(st.getHours())}:${this.dobuleFn(st.getMinutes())}:${this.dobuleFn(st.getSeconds())} -
           					${this.dobuleFn(et.getHours())}:${this.dobuleFn(et.getMinutes())}:${this.dobuleFn(et.getSeconds())}`;
							p.key = keyId;
							this.keyIndex[p.index]=p.key;
							keyId++;
							return p;
						}));
						o.key = keyId;
						keyId++;
						obj.children.push(o);
					}
						this.treeData.push(obj);
					}
				}else
				{
					let obj = {};
					obj.key = keyId;
					obj.showPointInfo=false;
					obj.eci=item.eci||{};
					this.eciData.push(obj.eci);
					keyId++;
					let st = new Date(item.serialSTime);
					let et = new Date(item.serialETime);
					obj.time = item.serialSTime.toString();
					obj.isRoad = item.type == 'ROAD' ? true : false;
					obj.pointCnt=pointCnt++;
					item.type === 'ROAD' ?'':obj.loc={lat:item.latitude,lng:item.longitude};
					obj.label = `${item.type == 'ROAD' ? '道路序列' : '驻点'} ${this.dobuleFn(st.getHours())}:${this.dobuleFn(st.getMinutes())}:${this.dobuleFn(st.getSeconds())} -
        				${this.dobuleFn(et.getHours())}:${this.dobuleFn(et.getMinutes())}:${this.dobuleFn(et.getSeconds())}`;
					this.treeData.push(obj);
					obj.index= item.index;
				}
			}
			return trailData;
		},
        //合并道路
		groupByFiled (arry,field) {
			let obj=[];
			let last = arry[0];
			let temp = [];
			for(let i=0;i<arry.length;i++){
				if(arry[i][field]===last[field]){  //如果当前路和上一条一样就合并
					if(i==arry.length-1){  //合并到最后一个还是一样,直接返回给obj
						temp.push(arry[i]);	
						obj.push(JSON.parse(JSON.stringify(temp)));
						temp = [];
					}else{
						temp.push(arry[i]);	
					}
				}else {
					if(temp.length>0)
						obj.push(JSON.parse(JSON.stringify(temp)));
						temp = [arry[i]];
				}
				last=arry[i];
			}
			return obj;
		},
    }
};
</script>

<style lang="less" scoped>
.road-network-fit{
    width:100%;
    height:100%;
    position:relative;
    &-search{
        width:100%;
        height:80px;
        display:flex;
        align-items:center;
        padding-top:10px;
        .search-bar{
            width:55%;
			/deep/.displayPass{
				.el-input__inner{
					-webkit-text-security:disc !important;
				}
			}
        }
        .tools-wrapper{
            width:45%;
            height:100%;
            padding:0 0 0 20px;
            display:flex;
            justify-content: flex-end;
            .btn-wrapper{
                padding-left: 2.22rem;
                padding-top: .67rem;
            }
            .slider-wrapper{
                width:60%;
                /deep/&.el-slider{
                    .el-slider__button{
                        background-color: #409EFF;
                        box-shadow: 0px 4px 11px 4px rgba(0,0,0,0.08), 0px 2px 4px 0px rgba(0,0,0,0.12), 0px 1px 2px -2px rgba(0,0,0,0.16);
                    }
                    .el-slider__stop{
                        background-color: #409EFF;
                        height: 12px;
                        width: 12px;
                        border: 1px solid #fff;
                        transform: translate(-50%,-30%);
                    }
                    .el-slider__marks-text{
                        font-size: 12px;
                        color: #262626;
                    }
                    .el-slider__button-wrapper{
                        z-index:101;
                    }
                }
            }
        }
    }
    .gis{
        width:100%;
        height:calc(100% - 80px);
    }
    .play-detail{
        position:absolute;
        right:13px;
        top:96px;
        width:22.22rem;
        height:calc(100% - 109px);
        box-shadow: 0px 0px 5px 1px rgba(13, 59, 128, 0.4);
        border-radius: 4px;
        background:#fff;
        z-index:101;
        padding:16px 16px 16px 25px;
    }
}
/deep/.cicle-wrapper{
    transform: translate(-50%, -50%);
    border: 2px solid #D4E9FF;
    border-radius: 100px;
    width: 22px;
    height: 22px;
    font-size: 12px;
    display: inline-block;
    text-align: center;
    vertical-align: bottom;
    color: white;
    background: linear-gradient(90deg, #2A93FF 0%, #409EFF 100%);
    box-shadow: 0px 2px 5px 1px rgba(11,94,255,0.3);
}
/deep/.start-and-end-wrapper{
    transform: translate(-50%, -50%);
    img{
        width: 44px;
        height: 44px;
    }
}
</style>