<template>
    <div class="area-event mtex-shandong-devops-dark-theme">
        <comNavTitle :navList="navList">
            <el-radio-group class="area-event-radio-group" v-model="searchParams.type" size="small">
                <el-radio-button v-for="(item, index) in btnGroup" :key="index" :label="item.value">
                    {{ item.name }}
                </el-radio-button>
            </el-radio-group>
        </comNavTitle>

        <div class="area-event-content">
            <full-content
                v-if="searchParams.type === '全部'"
                :searchParams.sync="searchParams"
            ></full-content>
            <task-content v-else :searchParams.sync="searchParams"></task-content>
        </div>
    </div>
</template>

<script>
import comNavTitle from '_com/comNavTitle.vue';
import fullContent from './areaEvent/fullContent.vue';
import taskContent from './areaEvent/taskContent.vue';

export default {
    name: 'areaEvent',
    components: {
        comNavTitle,
        fullContent,
        taskContent
    },
    props: {
        outsideParam: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            navList: this.outsideParam,
            searchParams: {
                type: '全部',
                time: []
            },
            btnGroup: [
                {
                    name: '全部',
                    value: '全部'
                },
                {
                    name: '按任务',
                    value: '按任务'
                }
            ]
        };
    },
    mounted() {},
    methods: {}
};
</script>

<style lang="less" scoped>
.area-event {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    &-radio-group {
        margin: 0 24px 0 auto;
    }
    &-content {
        flex: 1;
        border: 16px solid var(--normal-bg-border);
    }
}
</style>
