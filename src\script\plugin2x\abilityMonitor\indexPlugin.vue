<template>
    <div class="ability-monitor mtex-shandong-devops-dark-theme">
        <comNavTitle :navList="navList"> </comNavTitle>
        <div id="scrollContainer" class="ability-monitor-content">
            <div id="platformApp" class="section-wrapper">
                <PlatformApp :form="firstForm" :granularityOpt="granularityOpt" />
            </div>
            <div id="dataMonitor" class="section-wrapper">
                <DataMonitor :form="secondForm" :typeOpt="typeOpt" />
            </div>
            <div id="serviceStat" class="section-wrapper">
                <ServiceStat :form="thirdForm" :granularityOpt="granularityOpt" />
            </div>
        </div>
        <FloatingNav :navItems="navItems" domId="scrollContainer" />
    </div>
</template>
<script>
import comNavTitle from '_com/comNavTitle.vue';
import PlatformApp from './components/PlatformApp.vue';
import DataMonitor from './components/DataMonitor.vue';
import ServiceStat from './components/ServiceStat.vue';
import FloatingNav from '_com/FloatingNav.vue';
import commonMixins from '@/script/mixins/commonMixins.js';
import { getTimeRange } from './components/utils';

export default {
    name: 'abilityMonitor',
    mixins: [commonMixins],
    props: {
        outsideParam: {
            type: Array,
            default: () => []
        }
    },
    components: {
        comNavTitle,
        PlatformApp,
        DataMonitor,
        ServiceStat,
        FloatingNav
    },
    data() {
        return {
            navList: this.outsideParam,
            firstForm: {
                granularity: 3,
                time: getTimeRange({ amount: 7, useCurrentTime: false })
            },
            secondForm: {
                type: '4G',
                time: getTimeRange({
                    amount: 0,
                    useCurrentTime: false,
                    endDate: new Date(Date.now() - 1000 * 60 * 60 * 24)
                })
            },
            thirdForm: {
                granularity: 3,
                time: getTimeRange({ amount: 7, useCurrentTime: false })
            },
            granularityOpt: [
                { label: '天', value: 3 },
                { label: '时', value: 4 }
            ],
            typeOpt: [
                { label: '4G', value: '4G' },
                { label: '5G', value: '5G' }
            ],
            navItems: {
                平台应用: {
                    id: 'platformApp'
                    // icon: ''
                },
                数据监控: {
                    id: 'dataMonitor'
                    // icon: ''
                },
                服务统计: {
                    id: 'serviceStat'
                    // icon: ''
                }
            }
        };
    },
    methods: {}
};
</script>
<style lang="less" scoped>
@import url('../../../style/custom.less');
@import './PanelCommon.less';
.ability-monitor {
    height: 100%;
    background: var(--normal-bg-border);
    position: relative;

    &-content {
        height: calc(100% - 64px);
        border: 16px solid var(--normal-bg-border);
        display: flex;
        flex-direction: column;
        gap: 16px;
        overflow-y: auto;
        scroll-behavior: smooth;

        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        &::-webkit-scrollbar-thumb {
            border-radius: 10px;
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
            background: #5c6f92;
        }
        &::-webkit-scrollbar-track {
            /* 滚动条里面轨道 */
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            background: transparent;
        }
        &::-webkit-scrollbar-corner {
            background: rgba(0, 0, 0, 0);
        }
    }

    .section-wrapper {
        display: block;
        width: 100%;
    }
}
</style>
