<template>
    <div class="real-location mtex-shandong-devops-dark-theme">
        <comNavTitle :navList="navList">
            <searchBar
                class="searchBar"
                ref="formList"
                :formCols="formCols"
                :form="form"
                :isTransparentBg="true"
            >
                <template #search>
                    <el-button type="primary" size="small" @click="search">查询</el-button>
                </template>
                <template #reset>
                    <el-button class="reset-btn" size="small" @click="reset">重置</el-button>
                </template>
            </searchBar>
        </comNavTitle>
        <mtv-gis
            class="gis"
            :ref="gisId"
            :totaloptions="gistotalOptions"
            @onLoad="gisOnLoad"
        ></mtv-gis>
    </div>
</template>

<script>
import searchBar from '_com/searchBar/searchBar.vue';
import comNavTitle from '_com/comNavTitle.vue';
import commonMixins from '@/script/mixins/commonMixins.js';
import { gistotalOptions, getMayType, changeGisColor } from '@/script/constant/gis.js';
export default {
    name: 'realLocation',
    components: {
        searchBar,
        comNavTitle
    },
    props: {
        outsideParam: {
            type: Array,
            default: () => []
        }
    },
    mixins: [commonMixins],
    data() {
        return {
            navList: this.outsideParam,
            gisId: 'realLocationGisMap',
            formCols: [
                [
                    {
                        type: 'el-input',
                        prop: 'phoneNum',
                        label: '用户号码：',
                        labelWidth: '85px',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true,
                            class: 'displayPass'
                        },
                        rules: [
                            // { required: true, message: '必填', trigger: 'submit' }
                        ],
                        span: 16,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'tool',
                        prop: 'tool',
                        span: 8,
                        isShow: true,
                        labelWidth: '20px'
                    }
                ]
            ],
            form: {
                phoneNum: ''
            },
            gistotalOptions: gistotalOptions,
            gisLoaded: false,
            imgLayer: null,
            textLayer: null,
            divLayer: null
        };
    },
    methods: {
        // 获取置信度描述
        getConfidenceDesc(confidence) {
            const confidenceMap = {
                1: '高',
                2: '中',
                3: '低'
            };
            return confidenceMap[confidence] || `未知(${confidence})`;
        },
        // 获取定位类型描述
        getLocationTypeDesc(locationType) {
            const typeMap = {
                1: '基站定位',
                2: '指纹定位',
                3: '三角定位',
                4: '室分定位',
                5: 'MDT定位'
            };
            return typeMap[locationType] || `未知(${locationType})`;
        },
        search() {
            if (!this.gisLoaded) {
                this.$message('地图尚未初始化完成，请稍候重试');
                return;
            }
            if (!this.form.phoneNum) {
                this.$message.error('请先输入用户号码！');
                return;
            }
            this.clearGisData();
            this.$exloading1x();
            this.getPostGoldApproval(
                'post',
                'userPointUnencrypted',
                {
                    msisdn: this.form.phoneNum
                },
                '用户实时位置查询（明文）',
                (data) => {
                    this.$exloaded1x();
                    if (!data || !data.longitude) {
                        this.$message('该查询条件暂无数据，请重试！');
                        return;
                    }
                    const list = [
                        {
                            lng: data.longitude,
                            lat: data.latitude,
                            updateTime: data.updateTime,
                            width: 56,
                            ht: 0.2,
                            confidence: data.confidence,
                            locationType: data.locationType
                        }
                    ];
                    this.drawLocationImg(list);
                }
            );
        },
        reset() {
            if (!this.gisLoaded) {
                return;
            }
            this.clearGisData();
        },
        clearGisData() {
            if (this.divLayer) {
                this.divLayer.remove();
            }
            if (this.imgLayer) {
                this.imgLayer.removeAll();
            }
            if (this.textLayer) {
                this.textLayer.removeAll();
            }
        },
        gisOnLoad() {
            const gis = this.$refs[this.gisId].getEntity();
            // 设置底图
            if (getMayType() === 'default') {
                gis.tileLayerList['底图图层'].visible = false;
                //重写getUrl方法
                gis.tileLayerList['高德底图'].getUrl = (x, y, z) => {
                    return `${location.origin}/MapUrl/api/appmaptile?x=${x}&y=${y}&z=${z}`;
                };
            } else {
                gis.tileLayerList['高德底图'] && (gis.tileLayerList['高德底图'].visible = false);
            }
            changeGisColor(gis);

            gis.downLoad.onDownloadFinish.addOneEvent(() => {
                this.gisLoaded = true;
            });
        },
        drawLocationImg(list) {
            let data = list;
            const GIS = this.$refs[this.gisId].getEntity();
            this.imgLayer = new GIS.layer();
            this.imgLayer.name = 'imgTest';
            GIS.gis.scene.add(this.imgLayer.Group);
            this.imgLayer.visible = true;
            //传入图片url获取材质
            let material = GIS.meshList.img.getMaterial({
                url: require('../../../img/gis/location-new.png'),
                opacity: 1
            });
            //图片1
            data.autoScale = true;
            //生成模型
            let imgMesh = GIS.meshList.img.create(data, material);
            //模型添加进图层
            this.imgLayer.add(imgMesh);
            this.drawingText(data);
            //更新GIS
            GIS.gis.needUpdate = true;
            GIS.cameraControl.move(data[0]);
            GIS.cameraControl.zoom = 17;
        },
        drawingText(data) {
            const GIS = this.$refs[this.gisId].getEntity();
            if (!data.length) {
                return;
            }
            // this.textLayer = new GIS.layer();
            // this.textLayer.name = 'text';
            // this.textLayer.visible = true;
            // GIS.gis.scene.add(this.textLayer);
            // // //准备数据
            // let datas = data.map((item) => {
            //     return {
            //         value: `置信度：${this.getConfidenceDesc(
            //             item.confidence
            //         )}\\r定位类型：${this.getLocationTypeDesc(item.locationType)}\\r时间：${
            //             item.updateTime
            //         }`, //文字内容，通过\\r换行
            //         color: 0x000000, //文字颜色
            //         backgroundColor: 0xffffff, //背景颜色
            //         backgroundOpacity: 1, //背景不透明度
            //         lat: item.lat + 0.0001, //文字位置
            //         lng: item.lng, //文字位置
            //         ht: 2 //高度
            //     };
            // });
            // datas.size = 16; //设置文字大小为16px 包括了空白背景
            // datas.autoScale = true; //设置文字自动随等级缩放
            // datas.offsetX = 0; //X方向偏移
            // datas.offsetY = 0.5; //Y方向偏移  值代表 设置的size的倍数
            // GIS.meshList.text.backgroundOpacity = 0.5; //文字整体不透明度 如果数据中没有设置则以此为准
            // let o = GIS.meshList.text.create(datas);
            // this.textLayer.add(o);

            //准备数据
            let datas = data.map((item) => {
                return {
                    dom: `<div
                            style="
                                padding: 4px;
                                background: #375e8f;
                                border-radius: 2px;
                                border: 1px solid rgba(104, 146, 191, 0.5);
                                color: rgba(255, 255, 255, 0.85);
                                width: max-content;
                                font-size: 12px;
                                transform: translate(-50%, -160%);
                            "
                        >
                            置信度：${this.getConfidenceDesc(item.confidence)}<br />
                            定位类型：${this.getLocationTypeDesc(item.locationType)}<br />
                            时间：${item.updateTime}
                        </div>`,
                    point: { lng: item.lng, lat: item.lat }
                };
            });
            //调用相关接口
            datas.forEach((item) => {
                this.divLayer = GIS.layerList.divLayer.addDiv(item);
            });

            GIS.gis.needUpdate = true;
        }
    }
};
</script>

<style lang="less" scoped>
@import url('../../../style/custom.less');
.real-location {
    width: 100%;
    height: 100%;
    .gis {
        width: 100%;
        height: calc(100% - 80px);
    }
}
.searchBar {
    width: 510px;
    margin-left: auto;
    /deep/.displayPass {
        .el-input__inner {
            // -webkit-text-security: disc !important;
            background-color: transparent;
            border: 1px solid rgba(18, 139, 207, 0.6);
            color: #fff;
        }
    }
}
</style>
