<template>
    <cornerCard class="task-wrapper">
        <section-title-one title="实施区域事件">
            <el-form class="task-wrapper-form">
                <el-form-item label="创建时间：" label-width="120px">
                    <date-picker
                        class="task-date-picker"
                        v-model="searchParams.time"
                        :is-init-time="true"
                        init-time-type="today"
                    ></date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" size="small" @click="getRealTaskList(true)"
                        >查询</el-button
                    >
                    <el-button type="primary" size="small" @click="reset" class="reset-btn"
                        >重置</el-button
                    >
                </el-form-item>
            </el-form>
        </section-title-one>
        <div class="task-wrapper-content">
            <div class="task-wrapper-content-left">
                <div class="search-bar">
                    <el-input
                        placeholder="请输入关键字"
                        v-model="searchKey"
                        class="input-with-select"
                        size="small"
                        clearable
                        @keyup.enter.native="getRealTaskList(true)"
                    >
                        <el-select
                            v-model="keyName"
                            size="small"
                            slot="prepend"
                            placeholder="请选择"
                        >
                            <el-option label="任务名称" value="taskName"></el-option>
                            <el-option label="任务ID" value="taskId"></el-option>
                        </el-select>
                    </el-input>
                    <el-button type="primary" size="small" @click="getRealTaskList(true)"
                        >查询</el-button
                    >
                    <el-button class="reset-btn" type="primary" size="small" @click="resetTable"
                        >重置</el-button
                    >
                </div>
                <common-table
                    class="table-wrapper custom-table-dark"
                    :columns="tableConfig.columns"
                    :tableData="tableConfig.tableData"
                    :currentPage="tableConfig.currentPage"
                    :pageSize="tableConfig.pageSize"
                    :total="tableConfig.total"
                    :border="tableConfig.border"
                    :stripe="tableConfig.stripe"
                    @cell-click="cellClick"
                    @size-change="sizeChange"
                    @current-change="currentChange"
                ></common-table>
            </div>
            <div class="task-wrapper-content-right">
                <div class="right-top">
                    <div class="title">
                        <span style="margin-right: 20px"
                            >任务ID：{{ currentRowData.taskId || '' }}</span
                        >
                        <span>任务名称：{{ currentRowData.taskName || '' }}</span>
                    </div>
                    <div class="card-wrapper">
                        <div class="card">
                            <div class="card-left">
                                <img
                                    src="../../../../img/realtimeRegionEvent/region-count.png"
                                    alt=""
                                />
                            </div>
                            <div class="card-right">
                                <div class="card-title">当前订购区域数量</div>
                                <div class="quantity-time">
                                    <div class="quantity">
                                        {{ countList.regionCount || 0 }}
                                        <span class="unit">个</span>
                                    </div>
                                    <div class="time">
                                        <span class="mark-text">今日</span>
                                        {{ ' 截止至 ' + nowTime }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-left">
                                <img
                                    src="../../../../img/realtimeRegionEvent/total-count.png"
                                    alt=""
                                />
                            </div>
                            <div class="card-right">
                                <div class="card-title">总记录数</div>
                                <div class="quantity-time">
                                    <div class="quantity">
                                        {{ totalCount.totalRecords || 0 }}
                                        <span class="unit">万条</span>
                                    </div>
                                    <div class="time">
                                        {{ startTime + ' 至 ' + endTime }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="right-bottom">
                    <el-radio-group class="radio-group" v-model="activeName" size="medium">
                        <el-radio-button
                            label="记录数走势、高时延占比趋势图"
                            name="scaleLineEchart"
                        ></el-radio-button>
                        <el-radio-button label="数据延迟饼图" name="pieRowEchart"></el-radio-button>
                    </el-radio-group>
                    <el-select
                        v-if="
                            activeName === '记录数走势、高时延占比趋势图' &&
                            !(JSON.stringify(targetLineData) === '{}')
                        "
                        class="chart-select"
                        v-model="trendValue"
                        placeholder="-"
                        size="mini"
                        :style="{ width: '100px', left: left }"
                        @change="changeTrend"
                    >
                        <el-option
                            v-for="(item, index) in trendOps"
                            :key="index"
                            :label="item.label"
                            :value="item.value"
                        ></el-option>
                    </el-select>
                    <keep-alive>
                        <component
                            class="comp"
                            :ref="radioGroupList[activeName]"
                            :is="radioGroupList[activeName]"
                            :targetLineData="targetLineData"
                            :pieData="pieData"
                        >
                        </component>
                    </keep-alive>
                </div>
            </div>
        </div>
    </cornerCard>
</template>

<script>
import cornerCard from '_com/cornerCard.vue';
import sectionTitleOne from '../../../components/sectionTitle/sectionTitleOne.vue';
import commonTable from '@/script/components/commonTable.vue';
import scaleLineEchart from '@/script/components/charts/scaleLineEchart.vue';
import normalPieEchart from '@/script/components/charts/normalPieEchart.vue';
import pieRowEchart from './pieRowEchart.vue';
import datePicker from '_com/datePicker.vue';
import comonMixins from '../mixins/comonMixins.js';
import commonMixins from '@/script/mixins/commonMixins.js';

export default {
    name: 'taskContent',
    components: {
        cornerCard,
        sectionTitleOne,
        commonTable,
        scaleLineEchart,
        normalPieEchart,
        pieRowEchart,
        datePicker
    },
    props: {
        searchParams: {
            type: Object,
            default: () => ({})
        }
    },
    mixins: [comonMixins, commonMixins],
    data() {
        return {
            searchKey: '',
            keyName: 'taskName',
            tableConfig: {
                columns: [
                    {
                        label: '任务ID',
                        prop: 'taskId',
                        width: '100'
                    },
                    {
                        label: '任务名称',
                        prop: 'taskName'
                    }
                ],
                tableData: [
                    {
                        taskId: '145',
                        taskName: '获取天津行政区内开机用户任务'
                    }
                ],
                currentPage: 1,
                pageSize: 10,
                total: 1,
                border: false,
                stripe: true
            },
            activeName: '记录数走势、高时延占比趋势图',
            initLineData: {
                title: '每5分钟记录总量',
                xAxisData: [],
                yAxis: ['数量（条）', '平均时延（秒）'],
                unit: ['条', '秒'],
                legend: {
                    记录数: '平均：21.58 十亿 总计：1.11 十亿',
                    时延: '平均：30'
                },
                lineData: [
                    {
                        name: '记录数',
                        data: [],
                        color: '#5586EA'
                    },
                    {
                        name: '时延',
                        data: [],
                        color: '#5AD8A6'
                    }
                ]
            },
            initPieData: {
                pieData: [
                    { value: 0, name: '<=60s' },
                    { value: 0, name: '61-120s' },
                    { value: 0, name: '121-300s' },
                    { value: 0, name: '301-600s' },
                    { value: 0, name: '601-1200s' },
                    { value: 0, name: '>1200s' }
                ],
                percent: {
                    '<=60s': '0.0%',
                    '61-120s': '0.0%',
                    '121-300s': '0.0%',
                    '301-600s': '0.0%',
                    '601-1200s': '0.0%',
                    '>1200s': '0.0%'
                },
                title: '数据延迟',
                bgWH: 365,
                bgLeft: '8%'
            },
            currentRowData: {},
            radioGroupList: {
                '记录数走势、高时延占比趋势图': 'scaleLineEchart',
                数据延迟饼图: 'pieRowEchart'
            },
            nowTime: new Date().format('yyyy-MM-dd HH:mm:ss')
        };
    },
    computed: {
        startTime() {
            const time = this.searchParams.time;
            if (time && time.length) {
                return time[0];
            }
            return '';
        },
        endTime() {
            const time = this.searchParams.time;
            if (time && time.length) {
                return time[1];
            }
            return '';
        },
        left() {
            return `calc(50% + ${this.lengendLength}px)`;
        }
    },
    watch: {
        countList: {
            handler(newV) {
                this.nowTime = new Date().format('yyyy-MM-dd HH:mm:ss');
            },
            deep: true
        },
        activeName: {
            handler(newV) {
                if (newV === '数据延迟饼图') {
                    this.$nextTick(() => {
                        this.initPieData.bgWH = this.$refs.pieRowEchart.$el.offsetWidth / 3;
                        this.initPieData.bgLeft = '8.3%';
                    });
                }
            },
            deep: true
        }
    },
    mounted() {
        this.pieData = this.initPieData;
        this.getRealTaskList(true);
    },
    methods: {
        sizeChange(val) {
            this.tableConfig.currentPage = 1;
            this.tableConfig.pageSize = val;
            this.getRealTaskList();
        },
        currentChange(val) {
            this.tableConfig.currentPage = val;
            this.getRealTaskList();
        },
        resetTable() {
            this.searchKey = this.$options.data().searchKey;
            this.keyName = this.$options.data().keyName;
            this.getRealTaskList(true);
        },
        getRealTaskList(resetPage) {
            if (resetPage) {
                if (this.tableConfig.currentPage !== 1) {
                    this.tableConfig.currentPage = 1;
                    this.tableConfig.pageSize = 10;
                }
            }

            const _params = {
                isValid: 1,
                pageIndex: this.tableConfig.currentPage,
                rowsPage: this.tableConfig.pageSize
            };

            if (this.searchParams.time && this.searchParams.time.length === 2) {
                _params.startTime = this.searchParams.time[0];
                _params.endTime = this.searchParams.time[1];
            }

            _params[this.keyName] = this.searchKey;
            this.getPost('post', 'searchEvent', _params, '事件任务查询', (data) => {
                this.tableConfig.tableData = data.taskList;
                this.tableConfig.total = data.totalNumber;
                this.currentRowData = {};
                this.countList.regionCount = 0;
                this.totalCount.totalRecords = 0;
                if (data.taskList.length) {
                    this.cellClick(data.taskList[0]);
                }
            });
        },
        cellClick(data) {
            this.currentRowData = data;
            const { taskId } = this.currentRowData;
            this.getRegionTaskCount(taskId);
            this.getTotalCount(taskId);
            this.getTrendChart(taskId);
            this.getPieData(taskId);
        },
        reset() {
            const today = new Date();
            this.$set(this.searchParams, 'time', [
                today.format('yyyy-MM-dd 00:00:00'),
                today.format('yyyy-MM-dd 23:59:59')
            ]);
            this.getRealTaskList(true);
        }
    }
};
</script>

<style lang="less" scoped>
@import url('../../../../style/custom.less');
.task-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: transparent;
    padding: 16px;
    &-form {
        margin: 0 0 0 auto;
        display: flex;
        flex-direction: row;
        .task-date-picker {
            margin-right: 10px;
        }
        /deep/.el-form-item__label {
            color: #ffffffa6;
            font-weight: 400;
        }
    }
    &-content {
        width: 100%;
        flex: 1;
        display: flex;
        gap: 16px;
        &-left {
            width: 45%;
            height: 100%;
            padding: 15px;
            display: flex;
            flex-direction: column;
            border-radius: 2px;
            background: var(--section-card-bg);
            border: 1px solid var(--section-card-border);
            .search-bar {
                display: flex;
                align-items: center;
                margin-bottom: 15px;
                /deep/.input-with-select {
                    margin-right: 20px;

                    .el-input-group {
                        &__prepend {
                            background-color: transparent;
                            border: none;
                            .el-input__inner {
                                border-radius: 0;
                                border-top-left-radius: 2px;
                                border-bottom-left-radius: 2px;
                            }
                        }
                    }
                    .el-input__inner {
                        border-radius: 0;
                        border-top-right-radius: 2px;
                        border-bottom-right-radius: 2px;
                    }
                    .el-select .el-input {
                        width: 120px;
                    }
                }
            }
            .table-wrapper {
                flex: 1;
                border-radius: 2px;
                // border: 1px solid #ebeef5;
            }
        }
        &-right {
            width: 55%;
            height: 100%;
            display: flex;
            gap: 16px;
            flex-direction: column;
            // padding: 15px 15px 15px 5px;
            .right-top {
                width: 100%;
                padding: 15px;
                display: flex;
                flex-direction: column;
                border-radius: 2px;
                height: 194px;
                background: var(--section-card-bg);
                border: 1px solid var(--section-card-border);
                .title {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 16px;
                    font-weight: 500;
                    color: #fff;
                    line-height: 22px;
                }
                .card-wrapper {
                    height: 120px;
                    display: flex;
                    align-items: center;
                    margin-top: 15px;
                    .card {
                        width: 50%;
                        height: 100%;
                        background-image: url('../../../../img/realtimeRegionEvent/card-bg-1.png');
                        background-repeat: no-repeat;
                        background-size: 100% 100%;
                        display: flex;
                        font-family: PingFangSC, PingFang SC;
                        &-left {
                            width: 100px;
                            height: 100%;
                            padding: 10px 0;
                            margin-left: 12px;
                            img {
                                width: 100%;
                                height: 100%;
                            }
                        }
                        &-right {
                            flex: 1;
                            height: 100%;
                            padding: 24px 16px;
                            display: flex;
                            flex-direction: column;
                            justify-content: space-around;
                            .card-title {
                                line-height: 22px;
                                font-weight: 400;
                                font-size: 14px;
                                color: #c9dfff;
                            }
                            .quantity-time {
                                display: flex;
                                height: 30px;
                                justify-content: space-between;
                                align-items: flex-end;
                                .quantity {
                                    font-family: DINAlternate, DINAlternate;
                                    font-weight: bold;
                                    font-size: 30px;
                                    color: #00ffff;
                                    .unit {
                                        font-size: 12px;
                                        color: #c9dfff;
                                    }
                                }
                                .time {
                                    font-size: 12px;
                                    color: #c9dfff;
                                    .mark-text {
                                        width: 38px;
                                        height: 20px;
                                        background: #67c23a;
                                        border-radius: 2px;
                                        border: 1px solid #67c23a;
                                        font-size: 12px;
                                        font-weight: 400;
                                        color: #ffffff;
                                        line-height: 20px;
                                        padding: 0 5px;
                                    }
                                }
                            }
                        }
                    }
                    .card:not(:last-child) {
                        margin-right: 15px;
                    }
                }
            }
            .right-bottom {
                width: 100%;
                flex: 1;
                border-radius: 2px;
                background: var(--section-card-bg);
                border: 1px solid var(--section-card-border);
                position: relative;
                .chart-select {
                    position: absolute;
                    bottom: 50px;
                    z-index: 1;
                    left: calc(50% + 220px);
                }
                .el-tabs {
                    padding: 0 15px;
                    height: 40px;
                }
                .comp {
                    width: 100%;
                    height: calc(100% - 71px);
                }
                .radio-group {
                    padding: 15px;
                    /deep/.el-radio-group:first-child .el-radio-button .el-radio-button__inner {
                        border-radius: 2px 0 0 2px;
                    }
                    /deep/.el-radio-group:last-child .el-radio-button .el-radio-button__inner {
                        border-radius: 0 2px 2px 0;
                    }
                    /deep/.el-radio-button__orig-radio:checked + .el-radio-button__inner {
                        color: #409eff;
                        background-color: #fff;
                    }
                }
            }
        }
    }
}
/deep/.el-table .el-table__body tr.current-row > td {
    border-left: 1px solid #ff0000;
    border-top: 1px solid #ff0000;
    border-bottom: 1px solid #ff0000;
}
/deep/.el-table .el-table__body tr.current-row td:last-child {
    border-right: 1px solid #ff0000;
    border-left: none;
}
</style>
