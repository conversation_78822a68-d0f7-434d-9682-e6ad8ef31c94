<template>
    <div class="dialog-container mtex-shandong-devops-dark-theme" v-if="visible">
        <div class="dialog-header">
            <div class="header-title">
                <span class="title">{{ title }}</span>
            </div>
            <i class="el-icon-close close-btn" @click="handleClose"></i>
        </div>
        <div class="dialog-body">
            <el-table
                class="table-dark detail-table"
                ref="detailTable"
                size="small"
                :data="tableData"
                stripe
                height="100%"
            >
                <el-table-column prop="time" label="时间" />
                <el-table-column prop="user" label="需求人" />
                <el-table-column prop="title" label="需求标题" show-overflow-tooltip />
                <el-table-column prop="city" label="归属地市" />
                <el-table-column prop="desc" label="需求说明" show-overflow-tooltip />
                <el-table-column prop="value" label="需求价值" show-overflow-tooltip />
            </el-table>
            <el-pagination
                class="pagination-dark detail-page"
                popper-class="pagination-size-dark popover-dark"
                size="mini"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="page.num"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="page.size"
                layout="prev, pager, next"
                :total="total"
                :pager-count="5"
            >
            </el-pagination>
        </div>
    </div>
</template>

<script>
import commonMixins from '@/script/mixins/commonMixins.js';

export default {
    name: 'DetailDialog',
    mixins: [commonMixins],
    props: {
        // 对话框标题
        title: {
            type: String,
            default: '详情信息'
        },
        // 控制显示/隐藏
        visible: {
            type: Boolean,
            default: false
        },
        // 对话框宽度
        width: {
            type: String,
            default: '50%'
        },
        allowDrag: {
            type: Boolean,
            default: false
        },
        type: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            tableData: [
                // {
                //     time: '2023-11-15',
                //     user: '李明',
                //     title: '优化广州天河区5G网络覆盖',
                //     city: '广州市',
                //     desc: '针对天河区CBD商圈5G网络覆盖不足的问题进行优化,提升用户体验',
                //     value: '预计可提升区域网络质量20%,增加月收入30万'
                // },
                // {
                //     time: '2023-11-14',
                //     user: '王芳',
                //     title: '深圳南山区信号干扰治理',
                //     city: '深圳市',
                //     desc: '解决南山区科技园周边信号干扰严重问题,确保网络稳定性',
                //     value: '可减少投诉量50%,提升用户满意度'
                // }
            ],
            page: {
                num: 1,
                size: 10
            },
            total: 100
        };
    },
    watch: {
        type: {
            handler(val) {
                if (this.visible && val) {
                    this.getTableData();
                }
            },
            immediate: true
        }
    },
    methods: {
        // 关闭对话框（保持不变）
        handleClose() {
            this.$emit('update:visible', false);
            this.$emit('close');
        },
        getTableData() {
            this.tableData = [];
            this.total = 0;
            const typeMap = {
                internalSupport: {
                    api: 'internalSupport',
                    params: {
                        cityId: this.cityId
                    },
                    title: '对内支撑需求'
                },
                totalValue: {
                    api: 'totalValue',
                    params: {
                        cityId: this.cityId
                    },
                    title: '累计价值实现'
                },
                externalValue: {
                    api: 'queryContract',
                    params: {
                        cityId: this.cityId
                    },
                    title: '对外价值体现',
                    // 添加数据处理函数
                    processData: (data) => {
                        return data.map((item) => ({
                            time: (item.createTime && item.createTime.split(' ')[0]) || '-', // 只取日期部分
                            user: item.contactPerson || '-',
                            title: item.contractName || '-',
                            city: item.unit || '-',
                            desc: item.supportContent || '-',
                            value: `合同金额：${item.contractAmount || 0}元，合同期限：${
                                (item.contractStartTime && item.contractStartTime.split(' ')[0]) ||
                                '-'
                            } 至 ${
                                (item.contractEndTime && item.contractEndTime.split(' ')[0]) || '-'
                            }`
                        }));
                    }
                }
            };
            if (['totalValue', 'internalSupport'].includes(this.type)) {
                return;
            }
            const currentType = typeMap[this.type];
            this.getPost('post', currentType.api, currentType.params, currentType.title, (data) => {
                if (data) {
                    this.tableData = currentType.processData(data.contractList);
                    this.total = data.total; // 更新总数
                }
            });
        },
        handleSizeChange(size) {
            this.page.size = size;
            console.log('size', size);
            // this.getTableData();
        },
        handleCurrentChange(num) {
            this.page.num = num;
            console.log('num', num);
            // this.getTableData();
        }
    }
};
</script>

<style lang="less" scoped>
@import url('~@/style/custom.less');
.dialog-container {
    width: 46rem;
    height: 27rem;
    display: flex;
    flex-direction: column;
    background: url('~@/img/effectivenessScreen/dialog-bg.png') no-repeat;
    background-size: 100% 100%;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 999999999;
    .dialog-header {
        user-select: none;
        width: 100%;
        height: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        top: 1rem;
        .header-title {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 14.4444rem;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: url('~@/img/effectivenessScreen/dialog-header.png') no-repeat;
            background-size: 100% 100%;
            .title {
                font-family: YouSheBiaoTiHei;
                font-size: 1rem;
                color: #ffffff;
                line-height: 1.2222rem;
                letter-spacing: 0.0556rem;
                text-shadow: 0px 0.1111rem 0.2222rem rgba(0, 0, 0, 0.15);
            }
        }
        .close-btn {
            position: absolute;
            right: 3rem;
            top: 0;
            transform: translate(-50%, 50%);
            font-size: 0.8889rem;
            color: #19e8fc;
            cursor: pointer;
        }
    }
    .dialog-body {
        flex: 1;
        padding: 3.5rem 4rem 5rem 4rem;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }
}
.detail {
    z-index: 10;
    width: 40%;
    position: absolute;
    top: 86px;
    bottom: 16px;
    left: 16px;
    background: rgba(0, 42, 92, 0.9);
    border: 1px solid rgba(0, 149, 255, 0.5);
    padding: 16px;
    display: flex;
    flex-direction: column;
    &-table {
        height: 0;
        flex: 1;
        width: 100%;
    }
    &-page {
        padding-top: 16px;
        justify-content: center;
        /deep/ .el-pagination__sizes,
        /deep/ .el-pagination__jump {
            margin-left: 0;
            margin-right: 0;
        }
    }
}
</style>
<style lang="less">
.mtex-shandong-devops-dark-theme .pagination-dark.detail-page {
    padding-top: 16px;
    justify-content: center;
}
</style>
