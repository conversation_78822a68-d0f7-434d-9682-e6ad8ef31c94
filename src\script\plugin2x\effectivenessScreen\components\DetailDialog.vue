<template>
    <div class="dialog-container mtex-shandong-devops-dark-theme" v-if="visible">
        <div class="dialog-header">
            <div class="header-title">
                <span class="title">{{ title }}</span>
            </div>
            <i class="el-icon-close close-btn" @click="handleClose"></i>
        </div>
        <div class="dialog-body">
            <el-table
                class="table-dark detail-table"
                ref="detailTable"
                size="small"
                :data="tableData"
                stripe
                height="100%"
            >
                <el-table-column prop="time" label="时间" width="140" />
                <el-table-column prop="user" label="需求人" />
                <el-table-column prop="title" label="需求标题" show-overflow-tooltip />
                <el-table-column prop="city" label="归属地市" />
                <el-table-column prop="desc" label="需求说明" show-overflow-tooltip />
                <el-table-column prop="value" label="需求价值" show-overflow-tooltip />
            </el-table>
            <el-pagination
                class="pagination-dark detail-page"
                popper-class="pagination-size-dark popover-dark"
                size="mini"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="page.num"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="page.size"
                layout="prev, pager, next"
                :total="total"
                :pager-count="5"
            >
            </el-pagination>
        </div>
    </div>
</template>

<script>
import commonMixins from '@/script/mixins/commonMixins.js';

export default {
    name: 'DetailDialog',
    mixins: [commonMixins],
    props: {
        // 对话框标题
        title: {
            type: String,
            default: '详情信息'
        },
        // 控制显示/隐藏
        visible: {
            type: Boolean,
            default: false
        },
        // 对话框宽度
        width: {
            type: String,
            default: '50%'
        },
        allowDrag: {
            type: Boolean,
            default: false
        },
        type: {
            type: String,
            default: ''
        },
        cityId: {
            type: Number,
            default: null
        }
    },
    data() {
        return {
            tableData: [],
            page: {
                num: 1,
                size: 10
            },
            total: 100
        };
    },
    watch: {
        type: {
            handler(val) {
                if (this.visible && val) {
                    this.getTableData();
                }
            },
            immediate: true
        }
    },
    methods: {
        // 关闭对话框（保持不变）
        handleClose() {
            this.$emit('update:visible', false);
            this.$emit('close');
        },
        getTableData() {
            this.tableData = [];
            this.total = 0;
            const typeMap = {
                internalSupport: {
                    api: 'approveInnerQuery',
                    params: {
                        cityId: this.cityId || undefined,
                        pageNum: this.page.num,
                        pageSize: this.page.size
                    },
                    title: '对内支撑需求',
                    processData: (data) => {
                        console.log(77777777777, data);

                        const list = data.list;
                        return list.map((item) => ({
                            ...item,
                            time: item.needsStime || '-',
                            user: item.needsUser || '-',
                            title: item.needsTitle || '-',
                            city: item.cityName || '-',
                            desc: item.description || '-',
                            value: item.needsAmount || '-'
                        }));
                    }
                },
                totalValue: {
                    api: 'totalValue',
                    params: {
                        cityId: this.cityId || undefined,
                        pageNum: this.page.num,
                        pageSize: this.page.size
                    },
                    title: '累计价值实现'
                },
                externalValue: {
                    api: 'queryContract',
                    params: {
                        cityId: this.cityId || undefined,
                        pageNum: this.page.num,
                        pageSize: this.page.size
                    },
                    title: '对外价值体现',
                    processData: (data) => {
                        const list = data.contractList;
                        return list.map((item) => ({
                            time: (item.createTime && item.createTime.split(' ')[0]) || '-', // 只取日期部分
                            user: item.contactPerson || '-',
                            title: item.contractName || '-',
                            city: item.unit || '-',
                            desc: item.supportContent || '-',
                            value: `合同金额：${item.contractAmount || 0}元，合同期限：${
                                (item.contractStartTime && item.contractStartTime.split(' ')[0]) ||
                                '-'
                            } 至 ${
                                (item.contractEndTime && item.contractEndTime.split(' ')[0]) || '-'
                            }`
                        }));
                    }
                }
            };
            if (['totalValue'].includes(this.type)) {
                return;
            }
            const currentType = typeMap[this.type];
            this.getPost('post', currentType.api, currentType.params, currentType.title, (data) => {
                if (data) {
                    this.tableData = currentType.processData(data);
                    this.total = data.total;
                }
            });
        },
        handleSizeChange(size) {
            this.page.size = size;
            this.getTableData();
        },
        handleCurrentChange(num) {
            this.page.num = num;
            this.getTableData();
        }
    }
};
</script>

<style lang="less" scoped>
@import url('~@/style/custom.less');
.dialog-container {
    width: 46rem;
    height: 27rem;
    display: flex;
    flex-direction: column;
    background: url('~@/img/effectivenessScreen/dialog-bg.png') no-repeat;
    background-size: 100% 100%;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 999999999;
    .dialog-header {
        user-select: none;
        width: 100%;
        height: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        top: 1rem;
        .header-title {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 14.4444rem;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: url('~@/img/effectivenessScreen/dialog-header.png') no-repeat;
            background-size: 100% 100%;
            .title {
                font-family: YouSheBiaoTiHei;
                font-size: 1rem;
                color: #ffffff;
                line-height: 1.2222rem;
                letter-spacing: 0.0556rem;
                text-shadow: 0px 0.1111rem 0.2222rem rgba(0, 0, 0, 0.15);
            }
        }
        .close-btn {
            position: absolute;
            right: 3rem;
            top: 0;
            transform: translate(-50%, 50%);
            font-size: 0.8889rem;
            color: #19e8fc;
            cursor: pointer;
        }
    }
    .dialog-body {
        flex: 1;
        padding: 3.5rem 4rem 5rem 4rem;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }
}
.detail {
    z-index: 10;
    width: 40%;
    position: absolute;
    top: 86px;
    bottom: 16px;
    left: 16px;
    background: rgba(0, 42, 92, 0.9);
    border: 1px solid rgba(0, 149, 255, 0.5);
    padding: 16px;
    display: flex;
    flex-direction: column;
    &-table {
        height: 0;
        flex: 1;
        width: 100%;
    }
    &-page {
        padding-top: 16px;
        justify-content: center;
        /deep/ .el-pagination__sizes,
        /deep/ .el-pagination__jump {
            margin-left: 0;
            margin-right: 0;
        }
    }
}
</style>
<style lang="less">
.mtex-shandong-devops-dark-theme .pagination-dark.detail-page {
    padding-top: 16px;
    justify-content: center;
}
</style>
