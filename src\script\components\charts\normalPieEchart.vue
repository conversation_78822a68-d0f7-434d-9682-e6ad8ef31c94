<template>
    <div class="out-box">
        <el-empty
            v-if="JSON.stringify(pieData) === '{}'"
            description="暂无数据"
            :image="require('../../../img/noDataMon.png')"
        ></el-empty>
        <div v-else :id="name" class="pie-row-echart"></div>
    </div>
</template>

<script>
import * as echarts from 'echarts';
const initEcharts = (myChart, options) => {
    const option = {
        tooltip: {
            trigger: 'item',
            confine: true,
            formatter: '{a}\n{b}: {c} ({d}%)',
        },
        grid: {
            top: 10,
            left: '5%',
            right: '5%',
            bottom: 10,
        },
        series: [
            {
                type: 'pie',
                name: options.title,
                radius: options.radius || '50%',
                avoidLabelOverlap: false,
                avoidLabelOverlap: true,
                stillShowZeroSum: true,
                startAngle: 90,
                label: {
                    show: true,
                    position: 'outside',
                    alignTo: 'none',
                    formatter: function (params) {
                        return (
                            '{b|' +
                            params.name +
                            '}\n{d|' +
                            params.percent +
                            '%' +
                            '}'
                        );
                    },
                    rich: {
                        b: {
                            fontSize: 15,
                            fontWeight: 500,
                            lineHeight: 30,
                        },
                        d: {
                            color: 'rgba(22, 42, 64, 1)',
                            lineHeight: 16,
                        },
                    },
                },
                itemStyle: {
                    borderWidth: 2,
                    borderColor: '#fff',
                },
                labelLine: {
                    length: 15,
                    length2: 30,
                    maxSurfaceAngle: 80,
                    lineStyle: {
                        width: 2,
                    },
                },
                data: options.pieData,
            },
        ],
    };
    myChart.clear();
    myChart.setOption(option);
};
export default {
    name: 'normalPieEchart',
    props: {
        pieData: {
            type: Object,
            default: () => ({}),
        },
        name: {
            type: String,
            default: 'pie',
        },
    },
    data() {
        return {
            myChart: '',
        };
    },
    watch: {
        pieData: {
            handler(newV) {
                if (newV.pieData) {
                    this.initEchart();
                }
            },
            deep: true,
        },
    },
    mounted() {
        if (this.pieData && this.pieData.pieData) {
            this.initEchart();
        }
    },
    methods: {
        initEchart() {
            this.$nextTick(() => {
                this.myChart = echarts.init(document.getElementById(this.name));
                initEcharts(this.myChart, this.pieData);
            });
        },
    },
};
</script>

<style lang="less" scoped>
.el-empty {
    height: 100%;
}
.out-box,
.pie-row-echart {
    width: 100%;
    height: 100%;
}
</style>
