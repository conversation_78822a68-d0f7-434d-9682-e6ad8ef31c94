<template>
    <div class="position-visual">
        <div class="position-visual-left">
            <visualMenu @jumpRouter="jumpRouter"></visualMenu>
        </div>
        <div class="position-visual-right">
            <components
                :is="activeItem+'Plugin'"
            >
            </components>
        </div>
    </div>
</template>

<script>
import visualMenu from './components/menu.vue';
import baseStationPlugin from './baseStationPlugin.vue';
import LocationPlugin from './LocationPlugin.vue';
import realLocationPlugin from './realLocationPlugin.vue';
import roadNetworkFitPlugin from './roadNetworkFitPlugin.vue';
export default {
    name:'sdPositionVisual',
    components:{
        visualMenu,
        baseStationPlugin,
        LocationPlugin,
        realLocationPlugin,
        roadNetworkFitPlugin,
    },
    data(){
        return{
            activeItem:'realLocation',
        };
    },
    mounted() {
    },
    methods:{
        jumpRouter(value) {
            this.activeItem = value;
            // this.openSubPath(value, '', {});
        },
    }
};
</script>

<style lang="less" scoped>
.position-visual{
    width:100%;
    height:100%;
    background: #F0F3F7;
    padding:.89rem;
    display:flex;
    &-left{
        width: 240px;
        height: 100%;
        background: #FFFFFF;
        box-shadow: 0px 0px 5px 1px rgba(13, 59, 128, 0.4);
        padding-top:.56rem;
    }
    &-right{
        width: calc(100% - 257px);
        margin-left:.89rem;
        height: 100%;
        background: #FFFFFF;
        box-shadow: 0px 0px 5px 1px rgba(13, 59, 128, 0.4);
        padding:0 0.89rem 0.89rem;
    }
}
</style>
<style lang="less">
html{
    font-size:calc(100vw * 18 / 1920);
}
.el-time-spinner__item{
    line-height: 18px;
}
</style>