<template>
    <div class="tree-list border-radius4">
        <div class="top">
            <div class="num">{{ title }} （{{ allNum }}）</div>
            <span class="checkedAll" @click="btnClick">{{ btn }}</span>
        </div>
        <div class="filter-input" v-if="searchable">
            <el-input
                placeholder="输入关键字进行搜索"
                v-model="filterText"
                clearable
                size="small"
            >
            </el-input>
        </div>
        <div class="tree-out">
            <el-tree
                class="filter-tree left-tree"
                :data="treeData"
                :props="defaultProps"
                :filter-node-method="filterNode"
                :node-key="nodeKey"
                ref="treeL"
                :show-checkbox="showCheckbox"
                :expand-on-click-node="false"
                check-on-click-node
                @check="nodeCheck"
                @check-change="handleCheckChange"
            >
                <span class="custom-tree-node" slot-scope="{ node, data }">
                    <span class="custom-tree-node-label" :title="node.label">{{
                        node.label
                    }}</span>
                    <div v-if="slotRight">
                        <i
                            size="mini"
                            class="el-icon-close"
                            @click.stop="removeNode(node, data)"
                        >
                        </i>
                    </div>
                </span>
            </el-tree>
        </div>
    </div>
</template>
<script>
function getNum(data) {
    let num = 0;
    (function _num(data) {
        data.forEach((item) => {
            if (!item.children || !item.children.length) {
                num++;
            }

            if (item.children) {
                _num(item.children);
            }
        });
    })(data);
    return num;
}
export default {
    name: 'treeList',
    props: {
        defaultProps: {
            type: Object,
            default: () => ({
                children: 'children',
                label: 'label',
            }),
        },
        treeData: {
            type: Array,
            default: () => [],
        },
        showCheckbox: {
            type: Boolean,
            default: true,
        },
        nodeKey: {
            type: String,
            default: 'id',
        },
        title: {
            type: String,
            default: '区域列表',
        },
        btn: {
            type: String,
            default: '全选',
        },
        slotRight: {
            type: Boolean,
            default: false,
        },
        searchable: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            // allNum: 100,
            filterText: '',
            timer: null,
        };
    },
    computed: {
        allNum() {
            return getNum(this.treeData);
        },
    },
    methods: {
        removeNode(node, data) {
            this.$emit('remove-node', node, data);
        },
        filterNode(value, data) {
            if (!value) return true;
            return data.label.indexOf(value) !== -1;
        },
        handleCheckChange(val) {
            // this.$nextTick(() => {
            //
            // });
            if (this.timer) {
                clearTimeout(this.timer);
                this.timer = null;
            }
            this.timer = setTimeout(() => {
                this.$emit('check-change', this.getCheckedNodes());
            }, 300);
        },
        getChecked() {
            return this.$refs.treeL.getCheckedKeys();
        },
        getCheckedNodes() {
            return this.$refs.treeL.getCheckedNodes(false, false);
        },
        setCheckeds(keys, checked) {
            this.$refs.treeL.setCheckedKeys(keys, checked);
        },
        setChecked(key, checked = true, setChild = false) {
            this.$refs.treeL.setChecked(key, checked, setChild);
        },

        filterData() {
            this.$refs.treeL.filter(this.filterText);
        },
        btnClick() {
            if (this.btn === '全选') this.checkedAll();
            if (this.btn === '清空') this.cleanAll();
        },
        cleanAll() {
            this.$emit('checkedTree', false);
        },
        checkedAll() {
            const keys = this.treeData.map((item) => item[this.nodeKey]);
            this.setCheckeds(keys);
            this.$nextTick(() => {
                this.$emit('checkedTree', this.treeData, true);
            });
        },
        nodeCheck(data, node) {
            this.$emit('click-checkbox', data, node);
        },
    },
    watch: {
        filterText(val) {
            this.$refs.treeL.filter(val);
        },
    },
    mounted() {},
};
</script>
<style lang="less" scoped>
.border-radius4 {
    border-radius: 4px;
}
.tree-list {
    width: 300px;
    border: 1px solid #ccc;
    height: 380px;
    .filter-input {
        padding: 16px;
    }
    .top {
        padding: 10px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        height: 40px;
        color: #333;
        background: #f5f7f9;
        .checkedAll {
            height: 100%;
            width: 30px;
            color: #66b1ff;
            cursor: pointer;
        }
    }
    .tree-out {
        padding: 0 10px 16px;
        height: calc(100% - 40px);
        overflow-x: hidden;
        overflow-y: auto;
        &::-webkit-scrollbar {
            width: 10px;
            height: 1px;
        }

        &::-webkit-scrollbar-thumb {
            border-radius: 4px;
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.6);
            background: #f6f7fa;
        }

        &::-webkit-scrollbar-track {
            /* 滚动条里面轨道 */
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            background: transparent;
        }
    }
    .left-tree {
        // /deep/ .el-tree-node__content > label.el-checkbox {
        //     position: absolute;
        //     right: 10px;
        // }
        .custom-tree-node {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 14px;
            padding-right: 8px;
            flex: 1;
            &:hover {
                color: #3686ff;
            }
            &-label {
                max-width: 300px;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
    }
}
</style>
