<template>
    <div class="search-panel" :class="{ 'flex-1': fitWidth, 'search-panel-bg': bg }">
        <span class="search-panel-title" v-if="showTitle">{{ title }}</span>
        <el-form class="search-panel-form" ref="form" :model="form">
            <el-form-item v-if="isShowFormItem('省份')" label="省份：" label-width="100px" inline>
                <el-select v-model="form.province" placeholder="请选择省份" size="small" filterable>
                    <el-option
                        v-for="(item, index) in provinceList"
                        :key="index"
                        :label="item.label"
                        :value="item.value"
                    ></el-option>
                </el-select>
            </el-form-item>
            <el-form-item
                v-if="isShowFormItem('统计时间段') || isShowFormItem('开始时间')"
                :label="timeLabel + '：'"
                :label-width="{ 0: '120px', 1: '85px' }[+isShowFormItem('开始时间')]"
            >
                <el-date-picker
                    popper-class="date-picker-dark popover-dark"
                    v-model="form.time"
                    type="datetimerange"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    :default-time="['00:00:00', '23:59:59']"
                    align="right"
                    size="small"
                    format="yyyy-MM-dd HH:mm:ss"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    :clearable="!isInitTime"
                    :picker-options="pickerOptions"
                >
                </el-date-picker>
            </el-form-item>
            <el-form-item v-if="isShowFormItem('btn')" label-width="20px">
                <el-radio-group v-model="form.type" size="small">
                    <el-radio-button
                        v-for="(item, index) in btnGroup"
                        :key="index"
                        :label="item.value"
                        :disabled="item.disabled"
                        :title="item.tooltip ? item.tooltip : ''"
                        >{{ item.name }}</el-radio-button
                    >
                </el-radio-group>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import { isNull } from '../utils/index';
export default {
    name: 'searchPanel',
    props: {
        isInitTime: {
            type: Boolean,
            default: () => true
        },
        title: {
            type: String,
            default: '实时区域事件'
        },
        showTitle: {
            type: Boolean,
            default: true
        },
        userIdLabel: {
            type: String,
            default: '租户视图'
        },
        timeLabel: {
            type: String,
            default: '统计时间段'
        },
        btnGroup: {
            type: Array,
            default: () => [
                {
                    name: '全部',
                    value: '全部'
                },
                {
                    name: '按任务',
                    value: '按任务'
                }
            ]
        },
        // 限制哪些显示
        searchParamsList: {
            type: Array,
            default: () => ['租户视图', '统计时间段', 'btn']
        },
        // 省份
        provinceList: {
            type: Array,
            default: () => []
        },
        pageName: {
            type: String,
            default: ''
        },
        // 父盒需要flex
        fitWidth: {
            type: Boolean,
            default: false
        },
        // 是否需要背景
        bg: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            form: {
                userId: '',
                time: '',
                type: '',
                province: '100000'
            },
            pickerOptions: {
                shortcuts: [
                    {
                        text: '最近5分钟',
                        onClick: (picker) => {
                            this.datePicker(picker, 5);
                        }
                    },
                    {
                        text: '最近15分钟',
                        onClick: (picker) => {
                            this.datePicker(picker, 15);
                        }
                    },
                    {
                        text: '最近30分钟',
                        onClick: (picker) => {
                            this.datePicker(picker, 30);
                        }
                    },
                    {
                        text: '最近1小时',
                        onClick: (picker) => {
                            this.datePicker(picker, 60);
                        }
                    },
                    {
                        text: '最近3小时',
                        onClick: (picker) => {
                            this.datePicker(picker, 180);
                        }
                    },
                    {
                        text: '最近6小时',
                        onClick: (picker) => {
                            this.datePicker(picker, 360);
                        }
                    },
                    {
                        text: '最近12小时',
                        onClick: (picker) => {
                            this.datePicker(picker, 720);
                        }
                    },
                    {
                        text: '最近24小时',
                        onClick: (picker) => {
                            this.datePicker(picker, 1440);
                        }
                    },
                    {
                        text: '最近7天',
                        onClick: (picker) => {
                            this.datePicker(picker, 10080);
                        }
                    }
                ]
            }
        };
    },
    computed: {
        hasLimits() {
            return this.$store.state.monitor.power;
        }
    },
    watch: {
        form: {
            handler(newV) {
                const searchParams = {};
                Object.keys(newV).forEach((item) => {
                    console.log(newV[item]);
                    if (!isNull(newV[item]) && newV[item].length) {
                        searchParams[item] = newV[item];
                    }
                });
                this.$emit('update:searchParams', searchParams);
            },
            deep: true
        },
        provinceList: {
            handler(newV) {
                if (newV.length) {
                    this.form.province = this.provinceList[0].value;
                }
            },
            deep: true
            // immediate: true,
        }
    },
    mounted() {
        this.form.type = this.btnGroup[0].value;
        if (this.isInitTime) {
            const startDate = new Date();
            this.form.time = [
                startDate.format('yyyy-MM-dd 00:00:00'),
                startDate.format('yyyy-MM-dd 23:59:59')
            ];
        }
        if (this.pageName === 'taskList') {
            const endDate = new Date();
            const startDate = new Date(endDate.getTime() - 30 * 24 * 3600 * 1000);
            this.form.time = [
                startDate.format('yyyy-MM-dd HH:mm:ss'),
                endDate.format('yyyy-MM-dd HH:mm:ss')
            ];
        }
    },
    methods: {
        datePicker(picker, num) {
            const startDate = new Date();
            const endDate = new Date();
            startDate.setTime(startDate.getTime() - 60 * 1000 * num);
            picker.$emit('pick', [startDate, endDate]);
        },
        isShowFormItem(name) {
            return this.searchParamsList.includes(name);
        }
    }
};
</script>

<style lang="less" scoped>
.search-panel {
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    &-bg {
        background: #ffffff;
        box-shadow: 0px 0px 5px 1px rgba(13, 59, 128, 0.4);
    }
    &-title {
        font-size: 20px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.85);
        line-height: 28px;
    }
    &-form {
        display: flex;
        align-items: center;
        .el-form-item {
            margin-bottom: 0px;
        }
        /deep/ .el-radio-button:first-child:last-child .el-radio-button__inner,
        /deep/.el-radio-button__inner {
            border-radius: 0px;
        }
        /deep/.el-radio-group:first-child .el-radio-button .el-radio-button__inner {
            border-radius: 2px 0 0 2px;
        }
        /deep/.el-radio-group:last-child .el-radio-button .el-radio-button__inner {
            border-radius: 0 2px 2px 0;
        }
        /deep/.el-radio-button__orig-radio:checked + .el-radio-button__inner {
            color: #409eff;
            background-color: #fff;
        }
        /deep/.el-form-item__label {
            color: #ffffffa6;
            font-weight: 400;
        }
    }
}
.flex-1 {
    flex: 1;
}
</style>
<style lang="less">
.my-picker .el-picker-panel__footer .el-button--text {
    display: none !important;
}
</style>
