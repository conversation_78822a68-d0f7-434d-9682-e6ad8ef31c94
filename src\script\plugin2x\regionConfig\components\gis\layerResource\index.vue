<template>
  <div class="layer-resource">
    <el-popover
      v-model="isCollapse"
      v-clickOutside="handleClkOut"
      ref="layerPopover"
      :visible-arrow="false"
      placement="right-start"
      popper-class="popover-box"
      trigger="manual"
    >
      <!-- label -->
      <div class="popover-btn" slot="reference">
        <img class="collapse" :src="curCollapseImg" @click="toggleShowTag" />
        <div>
          <template v-for="item in layerList">
            <div v-if="item.isShow" class="item" :key="item.label" @click="toggleState(item)">
              <img class="layer w-16" :src="getIcon(item.prop, item.icons)" alt="" srcset="" />
              <span class="name" :class="{ isActive: curLayer.prop === item.prop }">
                {{ item.label }}
              </span>
            </div>
          </template>
        </div>
      </div>
      <!-- content -->
      <div class="popover-com">
        <keep-alive :exclude="['operateInfo', 'baseStationShow', 'sharpInfo']">
          <component
            class="w-370"
            :is="curLayer.prop"
            :curLayer="curLayer"
            v-bind="{ ...$attrs, ...curLayer.attrs }"
            v-on="{ ...$listeners, ...curLayer.listeners }"
          />
        </keep-alive>
      </div>
    </el-popover>
  </div>
</template>

<script>
import noCollapseImg from '@/img/createResource/noCollapse.png';
import vectorImg from '@/img/createResource/vector.png';
import { clickOutside } from '@/script/utils/directives.js';
export default {
  name: 'layer-resource',
  provide() {
    return {
      layerRoot: this,
    };
  },
  directives: {
    clickOutside,
  },
  props: {
    layerList: {
      type: Array,
    },
  },
  data() {
    return {
      isCollapse: false,
      isShowTag: false,
      resources: [],
      curLayer: {},
      isManualOpen: false, // 是否手动（非点击事件）打开图层
      isAllowClkOut: true,
      layerInfo: {},
    };
  },
  computed: {
    curCollapseImg() {
      return this.isShowTag ? vectorImg : noCollapseImg;
    },
  },
  mounted() {
    // this.$eventBus.$on('openLayer', (prop) => {
    //   this.toggleState(prop);
    // });
  },
  methods: {
    toggleState(item) {
      if (!item) return;
      if (!this.isCollapse) {
        this.isCollapse = true;
      }
      this.isShowTag = true;
      this.curLayer = item;
      if (item.toggleLayer) {
        item.toggleLayer(this.resetAll.bind(this));
      }
      this.$nextTick(() => {
        if (this.$refs.layerPopover) {
          this.$refs.layerPopover.updatePopper();
        }
      });
    },
    toggleShowTag() {
      this.isShowTag = !this.isShowTag;
      this.resetAll(this.isCollapse);
    },
    getIcon(prop, icons) {
      const isActive = this.curLayer.prop === prop;
      return icons[Number(isActive)];
    },
    handleClkOut() {
      if (!this.isAllowClkOut) return;
      this.resetAll(this.isCollapse);
    },
    resetAll(isCollapse) {
      if (isCollapse) {
        this.isCollapse = false;
        this.curLayer = {};
      }
    },
    setClkOut(isAllowClkOut) {
      this.isAllowClkOut = isAllowClkOut;
    },
  },
};
</script>

<style lang="less" scoped>
.layer-resource {
  .isActive {
    color: #0091ff;
  }
}
.w-16 {
  width: 16px;
}
</style>

<style lang="less" scoped>
.popover-box {
  padding: 0px;
}
.popover-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px 10px;
  width: 40px;
  background: #ffffff;
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.16);
  border-radius: 4px;
  cursor: pointer;
  .collapse {
    width: 20px;
    height: 20px;
  }
  img {
    display: inline-block;
  }
  .item {
    margin-top: 8px;
    text-align: center;
    &:not(:nth-child(1))::before {
      content: '';
      display: block;
      margin: 12px 0;
      width: 20px;
      height: 1px;
      background: rgba(0, 0, 0, 0.15);
    }
  }
  .layer {
    margin-bottom: 4px;
  }
  .name {
    display: inline-block;
    text-align: center;
    font-size: 16px;
    line-height: 18px;
    font-weight: 500;
  }
}
.w-370 {
  width: 370px;
}
</style>
