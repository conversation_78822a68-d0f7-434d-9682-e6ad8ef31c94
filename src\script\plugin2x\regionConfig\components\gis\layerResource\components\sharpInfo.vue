<template>
  <div class="sharp-info">
    <div v-for="(item, inx) in sharpList" class="wrap" :key="inx">
      <span class="title">{{ item.label }}</span>
      <el-table
        v-if="item.isTable"
        :data="item.content"
        size="mini"
        :max-height="178"
        :show-header="false"
      >
        <el-table-column type="index" label="资源名称" />
        <el-table-column prop="lng" label="经度"></el-table-column>
        <el-table-column prop="lat" label="纬度"></el-table-column>
        <el-table-column v-if="shapeType === 1" property="radius" :width="72" />
      </el-table>
      <div v-else class="content">
        <template v-for="(text, i) in item.content">
          <div :key="i">{{ text }}</div>
          <br />
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { toWKTCoordinates, toPointSequence } from '../../common/method';
export default {
  name: 'sharpInfo',
  components: {},
  props: {
    planeObjs: {
      type: Array,
      default: () => [],
    },
    shapeType: {
      // 多边形或者圆形，默认多边形
      type: Number,
      default: 2,
    },
  },
  data() {
    return {
      sharpList: [],
    };
  },
  created() {
    this.sharpList =
      this.shapeType === 2
        ? this.getSharpList(this.planeObjs)
        : this.getCircleSharpList(this.planeObjs);
  },
  methods: {
    getSharpList(planeObjs) {
      const regionCoors = planeObjs.map((item) => item.region.points);
      const pointSequence = [];
      const hexadecimal = [];
      const wkt = [];
      const coordinates = [];
      for (const region of regionCoors) {
        pointSequence.push(toPointSequence(region));
        hexadecimal.push(this.getHEXCoordinates(region));
        wkt.push(this.getWKTCoordinates(region));
        coordinates.push({}, ...region);
      }
      return [
        { label: '点序列', content: pointSequence },
        { label: '16进制', content: hexadecimal },
        { label: 'WKT', content: wkt },
        {
          label: '轮廓',
          content: coordinates.slice(1),
          isTable: true,
        },
      ];
    },
    getCircleSharpList(planeObjs) {
      const regionCoors = planeObjs.map((item) => {
        const circle = item.region.circle;
        return {
          ...circle,
          radius: Number((circle.radius * 1000).toFixed(3)),
        };
      });
      const pointSequence = [];
      const hexadecimal = [];
      const wkt = [];
      const coordinates = [];
      for (const circle of regionCoors) {
        const { centerLongitude, centerLatitude, radius } = circle;
        pointSequence.push(`${centerLongitude},${centerLatitude};${radius}`);
        hexadecimal.push(this.getCircleHEXCoordinates(circle));
        wkt.push(this.getCircleWKTCoordinates(circle));
        coordinates.push({
          lat: circle.centerLatitude,
          lng: circle.centerLongitude,
          radius: circle.radius,
        });
      }
      return [
        { label: '点序列', content: pointSequence },
        { label: '16进制', content: hexadecimal },
        { label: 'WKT', content: wkt },
        {
          label: '轮廓',
          content: coordinates,
          isTable: true,
        },
      ];
    },
    getWKTCoordinates(coordinates) {
      const coors = coordinates.map(({ lng, lat }) => {
        return toWKTCoordinates(Number(lng), Number(lat));
      });
      return coors.join(';');
    },
    getCircleWKTCoordinates(coordinates) {
      const { centerLongitude, centerLatitude, radius } = coordinates;
      const wktPoint = toWKTCoordinates(Number(centerLongitude), Number(centerLatitude));
      return `${wktPoint};${radius}`;
    },
    getHEXCoordinates(coordinates) {
      const coors = coordinates.map(({ lng, lat }) => {
        return `${Number(lng).toString(16)},${Number(lat).toString(16)}`;
      });
      return coors.join(';');
    },
    getCircleHEXCoordinates(coordinates) {
      const { centerLongitude, centerLatitude, radius } = coordinates;
      return `${Number(centerLongitude).toString(16)},${Number(centerLatitude).toString(
        16
      )};${radius}`;
    },
  },
};
</script>

<style lang="less" scoped>
.sharp-info {
  width: 370px;
  overflow: auto;
  .wrap {
    margin-bottom: 8px;

    .title {
      display: inline-block;
      margin-bottom: 4px;
      height: 28px;
      line-height: 28px;
      font-size: 14px;
      font-weight: bold;
      color: #333333;
    }
    .content {
      padding: 8px;
      height: 94px;
      background: rgba(0, 0, 0, 0.03);
      border-radius: 4px;
      font-size: 12px;
      overflow: auto;
    }
  }
}
</style>
