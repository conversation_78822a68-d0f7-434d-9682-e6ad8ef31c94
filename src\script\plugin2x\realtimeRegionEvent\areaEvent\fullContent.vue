<template>
    <div class="full-wrapper">
        <cornerCard class="full-wrapper-top">
            <sectionTitleOne title="今日数据">
                <span class="time">截止至{{ nowTime }}</span>
            </sectionTitleOne>
            <div class="content-wrapper">
                <div class="card-warpper" v-for="(item, index) in cardList" :key="index">
                    <div class="card-left">
                        <img class="img" :src="item.imgSrc" alt="" />
                    </div>
                    <div class="card">
                        <div class="title">{{ item.title }}</div>
                        <div class="quantity">
                            <span class="num">{{ item.num }}</span>
                            <span class="unit">{{ item.unit }}</span>
                            <span class="contrast"
                                >较昨日{{ item.contrast === 'fall' ? '减少' : '新增' }}</span
                            >
                            <span :class="['cursor', item.contrast]">{{ item.yoy }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </cornerCard>
        <cornerCard class="full-wrapper-bottom">
            <sectionTitleOne title="统计视图">
                <el-form class="full-wrapper-form">
                    <el-form-item label="统计时间段：" label-width="120px">
                        <date-picker
                            v-model="searchParams.time"
                            :is-init-time="true"
                            init-time-type="today"
                        ></date-picker>
                    </el-form-item>
                </el-form>
            </sectionTitleOne>
            <div class="trend-chart-panel">
                <div class="trend-chart-panel_line">
                    <!-- <span>记录数走势、高时延占比趋势图</span> -->
                    <sectionTitleTwo
                        class="trend-chart-panel_line-header"
                        title="记录数走势、高时延占比趋势图"
                    />
                    <el-select
                        v-if="!(JSON.stringify(targetLineData) === '{}')"
                        class="chart-select"
                        v-model="trendValue"
                        placeholder="-"
                        size="mini"
                        :style="{ width: '100px', left: left }"
                        @change="changeTrend"
                    >
                        <el-option
                            v-for="(item, index) in trendOps"
                            :key="index"
                            :label="item.label"
                            :value="item.value"
                        ></el-option>
                    </el-select>
                    <scale-line-echart
                        class="line"
                        :targetLineData="targetLineData"
                    ></scale-line-echart>
                </div>
                <div class="trend-chart-panel_total">
                    <div class="card-wrapper">
                        <div class="title">总记录数</div>
                        <div class="quantity">
                            <span class="num">{{ totalCount.totalRecords || 0 }}</span>
                            <span class="unit">万条</span>
                            <!-- <span class="contrast">同比</span>
                            <el-tooltip :content="tips" placement="top" :open-delay="500">
                                <span :class="['cursor', contrast]"
                                    >{{ totalCompare }}
                                    <i
                                        :class="
                                            contrast === 'rise'
                                                ? 'el-icon-caret-top'
                                                : 'el-icon-caret-bottom'
                                        "
                                    ></i>
                                </span>
                            </el-tooltip> -->
                        </div>
                    </div>
                    <pie-row-echart class="pie" name="test" :pieData="pieData"></pie-row-echart>
                </div>
            </div>
        </cornerCard>
    </div>
</template>

<script>
import cornerCard from '_com/cornerCard.vue';
import sectionTitleOne from '../../../components/sectionTitle/sectionTitleOne.vue';
import sectionTitleTwo from '../../../components/sectionTitle/sectionTitleTwo.vue';
import datePicker from '_com/datePicker.vue';
import pieRowEchart from './pieRowEchart.vue';
import comonMixins from '../mixins/comonMixins.js';
import scaleLineEchart from '@/script/components/charts/scaleLineEchart.vue';
export default {
    name: 'fullContent',
    components: {
        cornerCard,
        sectionTitleOne,
        sectionTitleTwo,
        pieRowEchart,
        scaleLineEchart,
        datePicker
    },
    props: {
        searchParams: {
            type: Object,
            default: () => ({})
        }
    },
    mixins: [comonMixins],
    data() {
        return {
            cardList: [
                {
                    title: '当前订购有效区域数量',
                    num: '0',
                    numKey: 'regionCount',
                    imgSrc: require('../../../../img/realtimeRegionEvent/region-count.png'),
                    yoyKey: 'orderRegionCountCompare',
                    unit: '个',
                    tips: '',
                    yoy: '0',
                    contrast: 'rise'
                },
                {
                    title: '当前订购有效任务数量',
                    num: '0',
                    numKey: 'eventCount',
                    imgSrc: require('../../../../img/realtimeRegionEvent/total-count.png'),
                    yoyKey: 'orderEventCountCompare',
                    unit: '个',
                    tips: '',
                    yoy: '0%',
                    contrast: 'rise'
                }
            ],
            initLineData: {
                title: '每5分钟记录总量',
                xAxisData: [],
                yAxis: ['数量（条）', '平均时延（秒）'],
                unit: ['条', '秒'],
                legend: {
                    记录数: '平均：21.58 条 总计：1.11 条',
                    时延: '平均：30'
                },
                lineData: [
                    {
                        name: '记录数',
                        data: [],
                        color: '#5586EA'
                    },
                    {
                        name: '时延',
                        data: [],
                        color: '#5AD8A6'
                    }
                ]
            },
            initPieData: {
                pieData: [
                    { value: 0, name: '<=60s' },
                    { value: 0, name: '61-120s' },
                    { value: 0, name: '121-300s' },
                    { value: 0, name: '301-600s' },
                    { value: 0, name: '601-1200s' },
                    { value: 0, name: '>1200s' }
                ],
                percent: {
                    '<=60s': '0.0%',
                    '61-120s': '0.0%',
                    '121-300s': '0.0%',
                    '301-600s': '0.0%',
                    '601-1200s': '0.0%',
                    '>1200s': '0.0%'
                },
                title: '数据延迟',
                bgWH: 240,
                bgLeft: '5.6%'
            },
            nowTime: new Date().format('yyyy-MM-dd HH:mm:ss')
        };
    },
    computed: {
        // 总记录数上升/下降
        contrast() {
            if (this.totalCount.totalRecordsCompare) {
                const contrast = String(this.totalCount.totalRecordsCompare);
                if (contrast.includes('-')) {
                    return 'fall';
                }
            }
            return 'rise';
        },
        previousIssueTimeGap() {
            return this.totalCount.previousIssueTimeGap || '';
        },
        tips() {
            if (this.contrast === 'fall') {
                return (
                    '对比' +
                        this.previousIssueTimeGap +
                        '下降' +
                        this.totalCount.totalRecordsCompare || '0.00%'
                );
            }
            return (
                '对比' + this.previousIssueTimeGap + '上升' + this.totalCount.totalRecordsCompare ||
                '0.00%'
            );
        },
        // 总记录数同比
        totalCompare() {
            let contrast = String(this.totalCount.totalRecordsCompare) || '0.00%';
            if (this.contrast === 'fall') {
                return String(contrast).replace('-', '');
            }
            console.log(666666, contrast);
            return contrast;
        }
    },
    watch: {
        countList: {
            handler(newV) {
                this.nowTime = new Date().format('yyyy-MM-dd HH:mm:ss');
                this.cardList.forEach((item) => {
                    item.num = newV[item.numKey];
                    const contrast = String(newV[item.yoyKey]);
                    if (contrast.includes('-')) {
                        item.yoy = String(contrast).replace('-', '');
                        item.contrast = 'fall';
                    } else {
                        item.yoy = contrast;
                        item.contrast = 'rise';
                    }
                });
            },
            deep: true
        },
        searchParams: {
            handler(newV) {
                this.fullFetch();
            },
            deep: true
        }
    },
    mounted() {
        this.pieData = this.initPieData;
        if (this.searchParams.time) {
            this.fullFetch();
        }
    },
    methods: {
        async fullFetch() {
            const promises = [
                this.getRegionTaskCount(),
                this.getTotalCount(),
                this.getTrendChart(),
                this.getPieData()
            ];
            await Promise.all(promises);
        }
    }
};
</script>

<style lang="less" scoped>
.full-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: transparent;
    .title {
        font-size: 14px;
        font-weight: 400;
        color: #c9dfff;
        line-height: 22px;
    }
    .time {
        padding-left: 10px;
        font-size: 14px;
        font-weight: 400;
        color: #c9dfff;
        line-height: 20px;
        align-self: center;
    }
    .quantity {
        height: 30px;
        line-height: 30px;
        .num {
            font-size: 30px;
            font-weight: bold;
            color: #00ffff;
        }
        .unit {
            font-size: 12px;
            font-weight: 400;
            color: #c9dfff;
            line-height: 22px;
        }
    }
    .contrast {
        font-size: 14px;
        font-weight: 400;
        color: #c9dfff;
        line-height: 22px;
        padding-left: 20px;
    }
    .cursor {
        cursor: pointer;
        padding-left: 10px;
        font-size: 16px;
    }
    .rise {
        color: #52c41a;
    }
    .fall {
        color: #f5222d;
    }
    &-top {
        width: 100%;
        height: 202px;
        background: var(--normal-bg);
        border-radius: 2px;
        padding: 16px;
        .content-wrapper {
            width: 100%;
            height: calc(100% - 50px);
            display: flex;
            gap: 16px;
            align-items: center;
            .card-warpper {
                width: 50%;
                height: 100%;
                display: flex;
                background-image: url('../../../../img/realtimeRegionEvent/card-bg-2.png');
                background-repeat: no-repeat;
                background-size: 100% 100%;
                .card-left {
                    width: 100px;
                    height: 100%;
                    padding: 10px 0;
                    margin-left: 12px;
                    .img {
                        width: 100%;
                        height: 100%;
                    }
                }
                .card {
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    padding: 30px 0 30px 24px;
                }
            }
        }
    }
    &-bottom {
        width: 100%;
        height: 0;
        flex: 1;
        margin-top: 15px;
        background: var(--normal-bg);
        border-radius: 2px;
        padding: 16px;
        .full-wrapper-form {
            margin: 0 0 0 auto;
            display: flex;
            flex-direction: row;
            /deep/.el-form-item__label {
                color: #ffffffa6;
                font-weight: 400;
            }
        }
        .trend-chart-panel {
            width: 100%;
            height: calc(100% - 50px);
            display: flex;
            gap: 16px;
            align-items: center;
            &_line {
                flex: 1;
                height: 100%;
                position: relative;
                border-radius: 2px;
                background: var(--section-card-bg);
                border: 1px solid var(--section-card-border);
                &-header {
                    margin: 12px 0 0 12px;
                }
                .chart-select {
                    position: absolute;
                    bottom: 50px;
                    z-index: 1;
                    left: calc(50% + 220px);
                }
                .line {
                    width: 100%;
                    height: calc(100% - 42px);
                }
            }
            &_total {
                width: 650px;
                height: 100%;
                border-radius: 2px;
                background: var(--section-card-bg);
                border: 1px solid var(--section-card-border);
                padding: 15px;
                .card-wrapper {
                    border-radius: 4px;
                    padding: 25px 15px;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    height: 107px;
                    background-image: url('../../../../img/realtimeRegionEvent/card-bg-3.png');
                    background-repeat: no-repeat;
                    background-size: 100% 100%;
                    .contrast {
                        font-size: 12px;
                        font-weight: 400;
                        color: #c9dfff;
                        line-height: 22px;
                        padding-left: 20px;
                    }
                }
                .pie {
                    width: 100%;
                    height: calc(100% - 107px);
                }
            }
        }
    }
}
</style>
<style lang="less">
.my-picker .el-picker-panel__footer .el-button--text {
    display: none !important;
}
</style>
