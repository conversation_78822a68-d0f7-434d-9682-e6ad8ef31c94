<template>
    <el-tabs v-model="activeName" @tab-click="handleClick" class="output-com">
        <el-tab-pane
            v-for="(item, index) in listData"
            :key="index"
            :label="item.name"
            :name="item.id"
        >
            <div class="content">
                <div class="con-table">
                    <el-table
                        class="con-table table-dark"
                        :data="item.showData"
                        style="width: 100%"
                        height="100%"
                        stripe
                    >
                        <el-table-column
                            v-for="(child, cIndex) in item.headData"
                            :key="cIndex"
                            :prop="child.value"
                            :width="child.width || ''"
                            :label="child.label"
                        >
                        </el-table-column>
                    </el-table>
                </div>

                <div class="page">
                    <el-pagination
                        @size-change="handleSizeChange(item)"
                        @current-change="handleCurrentChange(item)"
                        :current-page.sync="item.pageOption.page"
                        :page-sizes="[10, 50, 200, 300, 400]"
                        :page-size.sync="item.pageOption.pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="item.pageOption.total"
                    >
                    </el-pagination>
                </div>
            </div>
        </el-tab-pane>
    </el-tabs>
</template>
<script>
export default {
    name: 'outputCom',
    props: {
        list: {
            type: Array,
            default: () => []
        }
    },
    data() {
        console.log(this);
        const list = [
            {
                id: '1',
                name: '进入',
                data: [
                    {
                        index: '1',
                        attribute: 'imsi',
                        name: '手机imsi加密串',
                        dataType: 'String',
                        comment: '内容',
                        case: 'oen1o3h41bfcdasofu190u312bjv'
                    },
                    {
                        index: '2',
                        attribute: 'imsi',
                        name: '手机imsi加密串',
                        dataType: 'String',
                        comment: '内容',
                        case: 'oen1o3h41bfcdasofu190u312bjv'
                    }
                ],
                headData: [
                    { label: '序号', value: 'index', width: 180 },
                    { label: '字段属性', value: 'attribute' },
                    { label: '字段名称', value: 'name' },
                    { label: '数据类型', value: 'dataType' },
                    { label: '备注', value: 'comment' },
                    { label: '数据样例', value: 'case' }
                ],
                showData: [
                    {
                        index: '1',
                        attribute: 'imsi',
                        name: '手机imsi加密串',
                        dataType: 'String',
                        comment: '内容',
                        case: 'oen1o3h41bfcdasofu190u312bjv'
                    },
                    {
                        index: '1',
                        attribute: 'imsi',
                        name: '手机imsi加密串',
                        dataType: 'String',
                        comment: '内容',
                        case: 'oen1o3h41bfcdasofu190u312bjv'
                    }
                ],
                pageOption: {
                    page: 1,
                    pageSize: 50,
                    total: 400
                }
            },
            {
                id: '2',
                name: '离开',
                data: [
                    {
                        index: '1',
                        attribute: 'imsi',
                        name: '手机imsi加密串',
                        dataType: 'String',
                        comment: '内容',
                        case: 'oen1o3h41bfcdasofu190u312bjv'
                    },
                    {
                        index: '1',
                        attribute: 'imsi',
                        name: '手机imsi加密串',
                        dataType: 'String',
                        comment: '内容',
                        case: 'oen1o3h41bfcdasofu190u312bjv'
                    }
                ],
                showData: [
                    {
                        index: '1',
                        attribute: 'imsi',
                        name: '手机imsi加密串',
                        dataType: 'String',
                        comment: '内容',
                        case: 'oen1o3h41bfcdasofu190u312bjv'
                    },
                    {
                        index: '1',
                        attribute: 'imsi',
                        name: '手机imsi加密串',
                        dataType: 'String',
                        comment: '内容',
                        case: 'oen1o3h41bfcdasofu190u312bjv'
                    }
                ],
                headData: [
                    { label: '序号', value: 'index' },
                    { label: '字段属性', value: 'attribute' },
                    { label: '字段名称', value: 'name' },
                    { label: '数据类型', value: 'dataType' },
                    { label: '备注', value: 'comment' },
                    { label: '数据样例', value: 'case' }
                ],
                pageOption: {
                    page: 1,
                    pageSize: 50,
                    total: 400
                }
            }
        ];
        for (let i = 3; i < 503; i++) {
            list[0].data.push({
                index: i + '',
                attribute: 'imsi',
                name: '手机imsi加密串',
                dataType: 'String',
                comment: '内容',
                case: 'oen1o3h41bfcdasofu190u312bjv'
            });
        }
        list[0].pageOption.total = list[0].data.length;
        list.forEach((item) => {
            item.pageOption.total = item.data.length;
            const { page, pageSize, total } = item.pageOption;
            let end = page * pageSize;
            if (end >= total) {
                end = total;
            }
            item.showData = item.data.slice(page - 1, end);
        });
        return {
            activeName: list[0].id,
            listData: JSON.parse(JSON.stringify(list))
        };
    },
    computed: {
        // listData:{}
    },
    methods: {
        handleClick() {},
        tableDataChage(item) {
            const { page, pageSize, total } = item.pageOption;
            const start = (page - 1) * pageSize;
            let end = page * pageSize;
            if (end >= total) {
                end = total;
            }
            item.showData = item.data.slice(start, end);
        },
        handleSizeChange(item) {
            console.log('item: ', item);
            item.pageOption.page = 1;
            this.tableDataChage(item);
            // this.pageOption.page = 1;
        },
        handleCurrentChange(item) {
            this.tableDataChage(item);
            console.log('item: ', item);
        }
    }
};
</script>
<style lang="less" scoped>
@import url('../../../../style/custom.less');
.output-com {
    width: 100%;
    height: 100%;
    /deep/ .el-tabs__header {
        margin: 0;
        .el-tabs__item {
            color: #c9dfff;
            &.is-active {
                color: #409eff;
            }
        }
    }
    /deep/ .el-tabs__nav {
        &-scroll {
            padding: 0 16px;
            height: 48px;
            line-height: 48px;
        }
        &-wrap {
            &::after {
                background-color: #999;
            }
        }
    }
    /deep/ .el-tabs__content {
        height: calc(100% - 49px);
        .el-tab-pane {
            width: 100%;
            height: 100%;
            padding: 16px;
            .content {
                width: inherit;
                height: inherit;
                .con-table {
                    height: calc(100% - 48px);
                    background: rgba(0, 42, 92, 0.9);
                }
                .page {
                    margin-top: 16px;
                    text-align: right;
                    .el-pagination {
                        height: 32px;
                        line-height: 32px;
                    }
                }
            }
        }
    }
}
</style>
