<template>
    <div class="floating-nav" :class="{ collapsed: isCollapsed }">
        <div class="nav-container" v-if="!isCollapsed">
            <div
                v-for="(item, name) in navItems"
                :key="name"
                class="nav-item nav-item-col"
                :class="{ active: activeSection === name }"
                @click="scrollToSection(name, item.id)"
            >
                <img v-if="item.icon" class="nav-icon" :src="item.icon" />
                <span>{{ name }}</span>
            </div>
            <div class="nav-actions">
                <div class="action-item" @click.stop.prevent="scrollToTop">
                    <el-tooltip popper-class="tooltip-dark" content="返回顶部" placement="left">
                        <i class="el-icon-caret-top action-icon"></i>
                    </el-tooltip>
                </div>
            </div>
        </div>
        <div class="nav-actions collapse-action">
            <div class="action-item" @click.stop.prevent="toggleCollapse">
                <el-tooltip
                    popper-class="tooltip-dark"
                    :content="isCollapsed ? '展开' : '收起'"
                    placement="left"
                >
                    <i
                        :class="[
                            'action-icon',
                            isCollapsed ? 'el-icon-caret-left' : 'el-icon-caret-right'
                        ]"
                    ></i>
                </el-tooltip>
            </div>
        </div>
    </div>
</template>

<script>
/**
 * 浮动导航组件
 * 使用方法:
 * <FloatingNav domId="scrollContainer" :navItems="navItems" />
 *
 * navItems格式:
 * {
 *   菜单名称1: {
 *     id: 'element1', // 要滚动到的元素ID
 *     icon: 'icon-url' // 可选，菜单图标
 *   },
 *   菜单名称2: {
 *     id: 'element2'
 *   }
 * }
 */
export default {
    name: 'FloatingNav',
    props: {
        // 需要操作的容器id
        domId: {
            type: String,
            required: true,
            default: ''
        },
        // 导航项
        navItems: {
            type: Object,
            required: true,
            default: () => ({})
        }
    },
    data() {
        return {
            element: null,
            activeSection: Object.keys(this.navItems)[0] || '',
            isCollapsed: false,
            scrollDebounceTimer: null
        };
    },
    mounted() {
        // 添加滚动监听，更新激活状态
        this.element = document.getElementById(this.domId);
        if (this.element) {
            this.element.addEventListener('scroll', this.debouncedHandleScroll);
        }
        // 初始化时检查一次位置
        this.$nextTick(() => {
            this.handleScroll();
        });
    },
    beforeDestroy() {
        if (this.element) {
            this.element.removeEventListener('scroll', this.debouncedHandleScroll);
        }
        // 清除可能存在的定时器
        if (this.scrollDebounceTimer) {
            clearTimeout(this.scrollDebounceTimer);
        }
    },
    methods: {
        debouncedHandleScroll() {
            if (this.scrollDebounceTimer) {
                clearTimeout(this.scrollDebounceTimer);
            }
            this.scrollDebounceTimer = setTimeout(() => {
                this.handleScroll();
            }, 100); // 防抖延迟
        },
        handleScroll() {
            if (this.isCollapsed) return; // 收起状态不更新激活项

            const sections = Object.entries(this.navItems);
            if (sections.length === 0) return;

            // 检查是否滚动到底部
            if (this.element) {
                // 当滚动位置 + 容器高度 >= 内容总高度时，表示已滚动到底部或接近底部
                const isAtBottom =
                    this.element.scrollTop + this.element.clientHeight >=
                    this.element.scrollHeight - 100; // 允许100px的误差

                if (isAtBottom) {
                    // 设置最后一个为激活项
                    const lastSection = sections[sections.length - 1];
                    if (lastSection && lastSection[0] !== this.activeSection) {
                        this.activeSection = lastSection[0];
                    }
                    return;
                }
            }

            let closestSection = null;
            let minDistance = Infinity;

            sections.forEach(([name, item]) => {
                const element = document.getElementById(item.id);
                if (element) {
                    const rect = element.getBoundingClientRect();
                    const distance = Math.abs(rect.top);
                    if (distance < minDistance) {
                        minDistance = distance;
                        closestSection = name;
                    }
                }
            });

            if (closestSection && closestSection !== this.activeSection) {
                this.activeSection = closestSection;
            }
        },
        scrollToSection(section, elementId) {
            const element = document.getElementById(elementId);
            if (!element) return;

            try {
                element.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });

                this.activeSection = section;
            } catch (error) {
                // 错误处理
            }
        },
        scrollToTop() {
            if (this.element) {
                this.element.scrollTop = 0;
            }

            this.activeSection = Object.keys(this.navItems)[0] || '';

            return false; // 防止事件冒泡
        },
        toggleCollapse() {
            this.isCollapsed = !this.isCollapsed;
            return false; // 防止事件冒泡
        }
    }
};
</script>

<style lang="less" scoped>
.floating-nav {
    position: fixed;
    right: 21px;
    top: 70%;
    transform: translateY(-50%);
    background-color: #00ffff55;
    border-radius: 8px;
    border: 2px solid #00ffff;
    padding: 10px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 999;
    width: 80px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;

    &.collapsed {
        width: 40px;

        .collapse-action {
            border-top: none;
            margin-top: 0;
        }
    }

    &:hover {
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    }
    .nav-container {
        width: 100%;
        padding: 0 5px;
    }

    .nav-item,
    .action-item {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px 0;
        cursor: pointer;
        width: 100%;
        color: white;
        transition: all 0.3s ease;

        &-col {
            flex-direction: column;
        }

        &-row {
            flex-direction: row;
        }

        &:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        &.active {
            color: #00ffff;
            position: relative;

            &::before {
                content: '';
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                width: 3px;
                height: 70%;
                background-color: #00ffff;
            }

            .nav-icon {
                filter: invert(80%) sepia(80%) saturate(500%) hue-rotate(140deg) brightness(120%)
                    contrast(100%);
            }
        }
    }

    .nav-icon,
    .action-icon {
        width: 24px;
        height: 24px;
        font-size: 24px;
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
        transition: transform 0.2s ease;
    }

    .nav-item:hover .nav-icon,
    .action-item:hover .action-icon {
        transform: scale(1.1);
    }

    .nav-actions {
        margin-top: 10px;
        border-top: 1px solid rgba(255, 255, 255, 0.2);
        width: 100%;

        &.collapse-action {
            margin-top: auto; // 将收起按钮推到底部
        }
    }
}
</style>
