<template>
    <div class="table-list border-radius4 mtex-shandong-devops-dark-theme">
        <div class="top">
            <div class="num">{{ title }} （{{ totalNumber }}）</div>
            <span class="checkedAll" @click="btnClick">{{ btn }}</span>
        </div>
        <div class="sel-table-out" v-if="isSelectList">
            <el-table
                class="table-dark"
                style="width: 100%"
                height="325px"
                ref="selTable"
                size="small"
                :data="regionList"
                stripe
                @select-all="selectAllTable"
                @row-click="rowClick"
                @select="selectTableItem"
                @selection-change="handleSelectionChange"
            >
                <!-- <el-table-column v-if="selectMore" type="selection" width="55"> </el-table-column> -->
                <el-table-column
                    prop="regionName"
                    sortable
                    :filters="initFilterList('regionName')"
                    :filter-method="handleFilterRegionName"
                    label="区域名称"
                >
                    <template slot-scope="{ row }">
                        <span>{{ row.regionName }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="regionId"
                    sortable
                    label="区域ID"
                    :filters="initFilterList('regionId')"
                    :filter-method="handleFilterRegionId"
                    width="180"
                >
                    <template slot-scope="{ row }">
                        <span @click="viewRegionId(row)">{{ row.regionId }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="layerNamesCn"
                    sortable
                    label="图层类型"
                    :filters="initFilterList('attributes.layerNames')"
                    :filter-method="handleFilterLayerNames"
                    width="180"
                >
                    <template slot-scope="{ row }">
                        <span>{{ row.layerNamesCn }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="50">
                    <template slot-scope="{ row }">
                        <el-button
                            type="text"
                            icon="el-icon-delete"
                            @click="removeRow(row)"
                        ></el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                class="sel-page pagination-dark"
                popper-class="pagination-size-dark popover-dark"
                size="mini"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="page.num"
                :page-sizes="[100, 200, 400, 500]"
                :page-size="page.size"
                layout="total, sizes, prev, pager, next, jumper"
                :total="totalNumber"
                :pager-count="5"
            >
            </el-pagination>
        </div>
        <div class="sel-table-out" v-else>
            <el-table
                class="table-dark"
                style="width: 100%"
                height="325px"
                ref="selTable"
                size="small"
                :data="regionList"
                stripe
                @select-all="selectAllTable"
                @row-click="rowClick"
                @select="selectTableItem"
                @selection-change="handleSelectionChange"
            >
                <el-table-column v-if="selectMore" type="selection" width="55"> </el-table-column>
                <el-table-column
                    prop="regionName"
                    sortable
                    :filters="initFilterList('regionName')"
                    :filter-method="handleFilterRegionName"
                    label="区域名称"
                >
                    <template slot-scope="{ row }">
                        <span>{{ row.regionName }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="regionId"
                    sortable
                    label="区域ID"
                    :filters="initFilterList('regionId')"
                    :filter-method="handleFilterRegionId"
                    width="180"
                >
                    <template slot-scope="{ row }">
                        <span class="region-id">{{ row.regionId }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="layerNamesCn"
                    sortable
                    label="图层类型"
                    :filters="initFilterList('attributes.layerNames')"
                    :filter-method="handleFilterLayerNames"
                    width="180"
                >
                    <template slot-scope="{ row }">
                        <span>{{ row.layerNamesCn }}</span>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                class="sel-page pagination-dark"
                popper-class="pagination-size-dark popover-dark"
                size="mini"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="page.num"
                :page-sizes="[100, 200, 400, 500]"
                :page-size="page.size"
                layout="total, sizes, prev, pager, next, jumper"
                :total="totalNumber"
                :pager-count="5"
            >
            </el-pagination>
        </div>
    </div>
</template>
<script>
import { getDeepValue, isArr } from '@/script/utils/index';
export default {
    name: 'tableList',
    props: {
        defaultProps: {
            type: Object,
            default: () => ({
                children: 'children',
                label: 'label'
            })
        },
        regionList: {
            type: Array,
            default: () => []
        },
        showCheckbox: {
            type: Boolean,
            default: true
        },
        nodeKey: {
            type: String,
            default: 'id'
        },
        title: {
            type: String,
            default: '区域列表'
        },
        btn: {
            type: String,
            default: '全选'
        },
        slotRight: {
            type: Boolean,
            default: false
        },
        // -----------
        // 总条数
        totalNumber: {
            type: Number,
            default: 0
        },
        selectMore: {
            type: Boolean,
            default: true
        },
        isSelectList: {
            type: Boolean,
            default: false
        },
        hasSelectAll: {
            type: Boolean,
            default: false
        },
        selectList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            page: {
                num: 1,
                size: 100
            },
            filterText: '',
            timer: null
        };
    },
    computed: {
        // allNum() {
        //     return getNum(this.treeData);
        // },
    },
    methods: {
        initFilterList(key) {
            let filterList = [];
            let list = this.regionList.map((item) => getDeepValue(key, item));
            list.forEach((item) => {
                if (isArr(item)) {
                    item.forEach((child) => {
                        filterList.push(child);
                    });
                } else {
                    filterList.push(item);
                }
            });
            filterList = [...new Set(filterList)].map((item) => {
                return {
                    text: item,
                    value: item
                };
            });
            return filterList;
        },
        clearFilter() {
            this.$refs.selTable.clearFilter();
        },
        handleFilterRegionName(value, row, column) {
            return row.regionName === value;
        },
        handleFilterRegionId(value, row, column) {
            return row.regionId === value;
        },
        handleFilterLayerNames(value, row, column) {
            if (!value) {
                return !row.layerNamesCn;
            }
            if (!row.attributes) {
                return row.layerNamesCn === value;
            }
            return (
                row.attributes &&
                row.attributes.layerNames &&
                row.attributes.layerNames.includes(value)
            );
        },
        removeRow(row) {
            this.$emit('removeRow', row);
        },
        selectAllTable(selection) {
            if (!selection.length) {
                this.$emit('removeSelectList', this.showList);
            } else {
                this.$emit('changeSelectList', selection);
            }
        },
        rowClick(row) {
            this.$emit('click-row', row);
        },
        btnClick() {
            if (this.btn === '全选') this.checkedAll();
            if (this.btn === '清空') this.cleanAll();
        },
        cleanAll() {
            this.$emit('cleanAll', false);
        },
        checkedAll() {
            this.$nextTick(() => {
                this.toggleListSelection(this.regionList);
                this.$emit('checkedAll');
            });
        },
        handleSizeChange(val) {
            this.page.size = val;
            this.page.num = 1;
            this.getNewList();
        },
        handleCurrentChange(val) {
            this.page.num = val;
            this.getNewList();
        },
        getNewList() {
            this.$emit('getNewList', this.page);
        },
        handleSelectionChange(list) {
            // if (this.timer) {
            //     clearTimeout(this.timer);
            //     this.timer = null;
            // }
            // this.timer = setTimeout(() => {
            //     this.$emit('changeSelectList', list);
            // }, 200);
        },
        selectTableItem(selection, row) {
            console.log('selection, row: ', selection, row);
            if (this.selectMore) {
                const index = selection.findIndex((item) => item.regionId === row.regionId);
                if (index === -1) {
                    this.removeRow(row);
                } else {
                    this.$emit('changeSelectItem', row);
                }
            }
        },
        toggleListSelection(list, type = true) {
            list.forEach((item) => {
                this.toggleRowSelection(item, type);
            });
        },
        // 改变选择
        toggleRowSelection(row, type = true) {
            this.$refs.selTable.toggleRowSelection(row, type);
        },
        viewRegionId() {},
        // 初始化显示
        initSelectList() {
            const list = this.regionList;
            this.selectList.forEach((item) => {
                const index = list.findIndex((i) => i.regionId === item.regionId);
                if (index !== -1) {
                    setTimeout(() => {
                        this.toggleRowSelection(list[index]);
                    }, 300);
                }
            });
        },
        initPage() {
            this.page = {
                num: 1,
                size: 100
            };
        },
        clearSelection() {
            this.$refs.selTable.clearSelection(false);
        }
    },
    watch: {
        filterText(val) {},
        regionList(val) {
            if (this.hasSelectAll) {
                this.$nextTick(() => {
                    this.toggleListSelection(this.regionList);
                });
            } else if (this.selectMore && val.length) {
                this.$nextTick(() => {
                    this.initSelectList();
                });
            }
            this.clearFilter();
        }
    },
    mounted() {}
};
</script>
<style lang="less" scoped>
@import url('../../style/custom.less');
.border-radius4 {
    border-radius: 4px;
}
.table-list {
    width: 600px;
    background: rgba(0, 50, 102, 0.5);
    height: 410px;
    .region-id {
        // color: #66b1ff;
        // text-decoration: #66b1ff;
        // cursor: pointer;
    }
    .filter-input {
        padding: 16px;
    }
    .top {
        padding: 10px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        height: 40px;
        color: #fff;
        background: var(--dark-bg);
        .checkedAll {
            height: 100%;
            width: 30px;
            color: #66b1ff;
            cursor: pointer;
        }
    }
}
.sel-page {
    padding: 10px 15px !important;
    /deep/ .el-pagination__sizes,
    /deep/ .el-pagination__jump {
        margin-left: 0;
        margin-right: 0;
    }
}
</style>
