import _ from 'lodash';
import proj4 from 'proj4';
// 将扁平数据转化成树形
const formatAreas = (srcData, isSort = false) => {
    if (isSort) {
        srcData = _.orderBy(srcData, ['provinceCode', 'cityCode'], ['asc', 'asc']);
    }
    const result = [];
    let provinceObj = {};
    let cityObj = {};
    for (const { provinceName, provinceCode, districtName, districtCode, cityName, cityCode } of srcData) {
        // set province
        if (provinceObj.label !== provinceName) {
            provinceObj = {
                label: provinceName,
                value: provinceCode,
                children: [],
            };
            result.push(provinceObj);
        }
        // set city
        if (cityObj.label !== cityName) {
            cityObj = {
                label: cityName,
                value: cityCode,
                children: []
            };
            provinceObj.children.push(cityObj);
        }
        // set district
        cityObj.children.push({
            label: districtName,
            value: districtCode
        });
    }
    return result;
}
// 格式化地域字段
const formatField = (srcData, mapField = {
    typeCode: 'value',
    subTypeCode: 'value',
    classifyCode: 'value',
    typeName: 'label',
    subTypeName: 'label',
    classifyName: 'label',
    subTypeDetailList: 'children',
    classifyDetailList: 'children',
}) => {
    const res = [];
    for (const item of srcData) {
        const obj = {};
        Object.keys(item).forEach(key => {
            const needKey = mapField[key];
            if (needKey === 'children') {
                obj[needKey] = formatField(item[key]);
            } else {
                obj[needKey] = item[key];
            }
        })
        res.push(obj);
    }
    return res;
}
// 将坐标转化为 wkt
const toWKTCoordinates = (longitude, latitude) => {
    // 定义源坐标系和目标坐标系的EPSG代码
    const sourceEPSG = 'EPSG:4326'; // WGS84经纬度坐标系
    const targetEPSG = 'EPSG:3857'; // Web墨卡托投影坐标系
    // 定义源坐标系和目标坐标系的投影转换函数
    const sourceProjection = proj4(sourceEPSG);
    const targetProjection = proj4(targetEPSG);
    // 将经纬度坐标转换为目标坐标系的坐标
    const [x, y] = proj4(sourceProjection, targetProjection, [longitude, latitude]);
    // 构造WKT类型的坐标
    return `POINT (${x} ${y})`;
}
const getLabel = (curVal, tarList = []) => {
    const curItem = tarList.find(item => item.value == curVal);
    return curItem ? curItem.label : '';
}
const removeDuplicateLocations = (locations) => {
    const seen = new Map();
    const result = [];
    locations.forEach((location, index) => {
        const key = `${location.lng}-${location.lat}`;
        const seenLocation = seen.get(key);
        if (seenLocation) {
            seenLocation.repeatIndex.push(index);
            if (seenLocation.status !== 'added' && location.status === 'added') {
                seen.set(key, location);
                location.repeatIndex = seenLocation.repeatIndex;
                seenLocation.repeatIndex = [];
            }
        } else {
            location['repeatIndex'] = [index];
            seen.set(key, location);
        }
    });
    seen.forEach((value) => {
        result.push(value);
    });
    return result;
}
const removeDuplicateByCell = (locations) => {
    const seen = new Map();
    const result = [];
    locations.forEach((location) => {
        const key = `${location.lac}-${location.cell}`;
        const seenLocation = seen.get(key);
        if (seenLocation) {
            if (seenLocation.status !== 'added' && location.status === 'added') {
                seen.set(key, location);
            }
        } else {
            seen.set(key, location);
        }
    });
    seen.forEach((value) => {
        result.push(value);
    });
    return result;
}
// 求差集
const getDiffSet = (bigSet, smallSet, callback) => bigSet.filter(item => {
    const LACCELL = `${item.lac}-${item.cell}`;
    const isExclude = !smallSet.some(it => LACCELL === `${it.lac}-${it.cell}`);
    if (isExclude && callback) callback(item);
    return isExclude;
})

// 求交集
const getIntersectionSet = (bigSet, smallSet) => bigSet.filter(item => {
    const LACCELL = `${item.lac}-${item.cell}`;
    return smallSet.some(it => LACCELL === `${it.lac}-${it.cell}`);
})

//排序
const compareSort = (prop, sort = 'asce') => {
    return function (a, b) {
        if (sort === 'asce') {
            return a[prop] - b[prop];
        }
        return b[prop] - a[prop];
    };
};
const addLastStatus = (srcBaseStations, newBaseStations, callback) => {
    const baseStations = [];
    for (const item of newBaseStations) {
        const LACCELL = `${item.lac}-${item.cell}`;
        const tarItem = srcBaseStations.find(it => `${it.lac}-${it.cell}` === LACCELL);
        if (tarItem) {
            item.status = tarItem.status;
            item.onceAdded = tarItem.onceAdded;
            item.isArea = tarItem.isArea;
            callback && callback(item, tarItem);
        }
        baseStations.push(item);
    }
    return baseStations;
}
const addIncStations = (srcBaseStations, newBaseStations) => {
    const result = [];
    for (const item of srcBaseStations) {
        const lngLat = `${item.lng},${item.lat}`;
        const tarItem = newBaseStations.find(it => `${it.lng},${it.lat}` === lngLat);
        if (tarItem) {
            if (tarItem.status === 'added') {
                item.status = tarItem.status;
            }
        } else {
            result.push(tarItem);
        }
    }
    return result;
}
const toCoordinate = (regionCharts) => {
    return regionCharts.split(';')
        .map((item) => {
            const [lng, lat] = item.split(',');
            return lng && lat ? { lng: Number(lng), lat: Number(lat) } : null;
        })
        .filter(Boolean);
}
const toPointSequence = (regionCoors = []) => {
    return regionCoors.map(({ lat, lng }) => `${lng},${lat}`)
        .join(';');
}
const formatBaseStations = (baseStations, status, callback) => {
    const result = [];
    const isArea = status === 'added' ? '是' : '否';
    const onceAdded = status === 'added';
    for (const item of baseStations) {
        const curItem = {
            ...item,
            lng: item.cellLongitude,
            lat: item.cellLatitude,
            dir: 0,
            ht: 20,
            size: 30,
            status,
            isArea,
            onceAdded,
        }
        callback && callback(curItem);
        result.push(curItem);
    }
    return result;
}
const arrayToString = (arr) => {
    return arr.map(subArr => subArr.join(','));
}

const getRegionLabels = (idData, treeData) => {
    const result = [];
    idData.forEach(id => {
        const findLabel = (data, path = []) => {
            for (let i = 0; i < data.length; i++) {
                const item = data[i];
                if (item.value === id) {
                    result.push([...path, item.label].join('/'));
                    break;
                } else if (item.children) {
                    findLabel(item.children, [...path, item.label]);
                }
            }
        };
        findLabel(treeData);
    });
    return result.join('、');
}
const isExistWithinCurArea = (curRegions, tarRegions, g) => {
    if (!curRegions || !tarRegions) return;
    const curPoints = toCoordinate(curRegions);
    const tarPoints = toCoordinate(tarRegions);
    const curPoints3 = curPoints.map(point => g.math.world2three(point));
    const tarPoints3 = tarPoints.map(point => g.math.world2three(point));
    const results = g.math.positionTest(curPoints3, tarPoints3);
    return results.length && results.every(Boolean);
}
const isExistWithinCurCircle = (curRegions, tarCircle, g) => {
    if (!curRegions || !tarCircle) return;
    let { centerLatitude, centerX, centerLongitude, centerY, radius } = tarCircle;
    let lat,lng;
    if (centerX && centerY) {
        lat = centerY;
        lng = centerX;
    } else {
        lat = centerLatitude;
        lng = centerLongitude;
    }
    const circlePoint = { lat, lng };
    const curPoints = toCoordinate(curRegions);
    const result = curPoints.every(point => radius >= g.math.worldDistance(point, circlePoint));
    return curPoints.length && result;
};
const addInxForHollowList = (regionList = [], holeList = [], g) => {
    const newHoleList = [];
    const copyHoleList = [...holeList];
    for (const [inx, region] of regionList.entries()) {
        let index = 0;
        for (const [holeInx, hole] of holeList.entries()) {
            const isExist = isExistWithinCurArea(hole.polygon, region.polygon, g);
            if (isExist) {
                newHoleList.push({
                    ...hole,
                    index: index++,
                    region: inx,
                });
                copyHoleList[holeInx] = null;
            }
        }
    }
    return {
        holeList: newHoleList,
        invalidHoleList: copyHoleList.filter(Boolean),
    }
}
const formatEnterAreas = (regionList, holeList, g, type = 1) => {
    const holeRegionCoors = [];
    const copyHoleList = [...holeList];
    const isExistWithinArea = type === 1 ? isExistWithinCurArea : isExistWithinCurCircle;
    for (const [inx, region] of regionList.entries()) {
        let index = 0;
        for (const [holeInx, hole] of copyHoleList.entries()) {
            const isExist = isExistWithinArea(hole, region, g);
            if (isExist) {
                holeRegionCoors.push({
                    polygon: hole,
                    areaType: 1,
                    index: index++,
                    region: inx,
                });
                copyHoleList[holeInx] = null;
            }
        }
    }
    return {
        holeRegionCoors,
        invalidHoleList: copyHoleList.filter(Boolean),
    };
}
const isImportCreated = (holeList = []) => {
    const firstItem = holeList[0] || {};
    return !('region' in firstItem) || !('index' in firstItem);
}
const positionCircleTest = (tarPoints, circle, g) => {
    const { centerLatitude, centerLongitude, radius } = circle;
    return tarPoints.map(tarPoint => {
        return radius >= g.math.worldDistance(tarPoint, { lat: centerLatitude, lng: centerLongitude });
    })
}
const getRegions = (baseInfo, shapeType) => {
    const isPolygon = shapeType === 2;
    const { multiPolygonList, regionCoors, circle = {} } = baseInfo;
    if (multiPolygonList && multiPolygonList.length) {
        return multiPolygonList.map((it) => {
            return isPolygon
                ? it.polygon
                : {
                    centerLatitude: it.centerY,
                    centerLongitude: it.centerX,
                    radius: it.radius,
                };
        });
    } else {
        return isPolygon
            ? (regionCoors || '').split('|')
            : [
                {
                    ...circle,
                    radius: circle.radius,
                },
            ];
    }
}
export {
    formatAreas,
    formatField,
    toWKTCoordinates,
    getLabel,
    removeDuplicateLocations,
    removeDuplicateByCell,
    addLastStatus,
    toCoordinate,
    toPointSequence,
    formatBaseStations,
    addIncStations,
    arrayToString,
    getRegionLabels,
    getDiffSet,
    getIntersectionSet,
    compareSort,
    isExistWithinCurArea,
    addInxForHollowList,
    isImportCreated,
    formatEnterAreas,
    positionCircleTest,
    isExistWithinCurCircle,
    getRegions,
}