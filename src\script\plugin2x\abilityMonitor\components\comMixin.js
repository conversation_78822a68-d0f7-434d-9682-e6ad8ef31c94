import cornerCard from '_com/cornerCard.vue';
import sectionTitleOne from '_com/sectionTitle/sectionTitleOne.vue';
import sectionTitleTwo from '_com/sectionTitle/sectionTitleTwo.vue';
import datePicker from '_com/datePicker.vue';
import commonEchart from '_com/charts/commonEchart.vue';
import commonMixins from '@/script/mixins/commonMixins.js';
import { formatDate, getFormattedTime, getTimeRange } from './utils';

export default {
    components: {
        cornerCard,
        sectionTitleOne,
        sectionTitleTwo,
        datePicker,
        commonEchart
    },
    mixins: [commonMixins],
    methods: {
        formatDate,
        getFormattedTime,
        getTimeRange
    }
};
