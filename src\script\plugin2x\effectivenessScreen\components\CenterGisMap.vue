<template>
    <div class="center-gis-map">
        <div class="top">
            <div
                class="top-item"
                :class="{ large: card.large }"
                v-for="(card, index) in topCards"
                :key="index"
                :style="{ backgroundImage: `url(${card.bg})` }"
                @click="handleTopCardClick(card.type)"
            >
                <div class="icon" :style="{ backgroundImage: `url(${card.icon})` }"></div>
                <div class="content">
                    <div class="title">
                        <span class="title-text">{{ card.title }}</span>
                        <div class="title-info" />
                    </div>
                    <div class="info">
                        <span class="info-value">{{ card.value }}</span>
                        <span class="info-label">{{ card.label }}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="center">
            <div class="center-header">
                <div
                    class="header-back-btn"
                    @click="handleBack"
                    v-show="currentLevel === 'city'"
                ></div>
                <div class="header-location">
                    <img src="../../../../img/effectivenessScreen/location.png" alt="" />
                    <span>{{ activeRegionName }}</span>
                </div>
            </div>
            <main class="center-main">
                <div ref="centerGisMap" class="center-gis-map"></div>
                <div class="map-legend">
                    <span class="map-legend-title">图例</span>
                    <div class="map-legend-content">
                        <div
                            class="map-legend-item"
                            v-for="(item, index) in mapLegend"
                            :key="index"
                        >
                            <div
                                class="map-legend-item-color"
                                :style="{ backgroundColor: item.color }"
                            ></div>
                            <span class="map-legend-item-label">{{ item.label }}</span>
                        </div>
                    </div>
                </div>
            </main>
        </div>
        <div class="bottom">
            <div class="bottom-radio">
                <div
                    class="bottom-radio-item"
                    :class="{ active: bottomRadioActive === index }"
                    v-for="(item, index) in bottomRadio"
                    :key="index"
                    @click="handleBottomRadioClick(item.value)"
                >
                    {{ item.label }}
                </div>
            </div>
            <div class="bottom-problem">
                <img src="../../../../img/effectivenessScreen/tip-info.png" alt="" />问题流量
            </div>
        </div>
        <DetailDialog
            :visible="dialogVisible"
            title="指标详情"
            :type="activeTopCardType"
            @update:visible="dialogVisible = $event"
        />
    </div>
</template>

<script>
import * as echarts from 'echarts';
import DetailDialog from './DetailDialog.vue';
// 引入山东省 GeoJSON 数据
import shandongGeoJson from '@/script/constant/map-json/province/370000.json';
import citysGeoJson from '@/script/constant/map-json/citys/370000_full_district.json';
import commonMixins from '@/script/mixins/commonMixins.js';

export default {
    name: 'CenterGisMap',
    mixins: [commonMixins],
    components: {
        DetailDialog
    },
    props: {
        data: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            dialogVisible: false,
            thisGis: null,
            activeRegionName: '山东',
            activeRegionId: null,
            centerChart: null,
            resizeObserver: null,
            // 地图缩放限制
            mapScaleLimit: {
                min: 1,
                max: 5
            },
            // 下钻相关状态
            currentLevel: 'province',
            geoJson: {
                province: shandongGeoJson, // 省级地图（山东）
                city: citysGeoJson // 市级
            },
            regionData: {
                province: shandongGeoJson.features.map((item) => {
                    return {
                        adcode: item.properties.adcode,
                        name: item.properties.name,
                        select: {
                            itemStyle: {
                                areaColor: '#adadad',
                                borderColor: '#fff',
                                borderWidth: 2
                            }
                        },
                        itemStyle: {
                            areaColor: '#adadad'
                        }
                    };
                }),
                city: citysGeoJson.features.map((item) => {
                    return {
                        adcode: item.properties.adcode,
                        name: item.properties.name,
                        select: {
                            disabled: true
                        },
                        itemStyle: {
                            areaColor: '#adadad'
                        }
                    };
                })
            },
            // 顶部卡片数据
            activeTopCardType: null,
            topCards: [
                {
                    title: '对内支撑需求',
                    type: 'internalSupport',
                    value: '-',
                    label: '累计需求(个)',
                    bg: require('@/img/effectivenessScreen/gis-top-card-bg.png'),
                    icon: require('@/img/effectivenessScreen/gis-top-1.png'),
                    large: false
                },
                {
                    title: '累计价值实现',
                    type: 'totalValue',
                    value: '-',
                    label: '累计创收(万元)',
                    bg: require('@/img/effectivenessScreen/gis-top-card-bg2.png'),
                    icon: require('@/img/effectivenessScreen/gis-top-2.png'),
                    large: true
                },
                {
                    title: '对外价值体现',
                    type: 'externalValue',
                    value: '-',
                    label: '累计创收(万元)',
                    bg: require('@/img/effectivenessScreen/gis-top-card-bg.png'),
                    icon: require('@/img/effectivenessScreen/gis-top-3.png'),
                    large: false
                }
            ],
            // 底部radio数据
            bottomRadioActive: 0,
            bottomRadio: [
                {
                    label: '实时软采流量',
                    value: 0
                },
                {
                    label: '指纹模型定位精度',
                    value: 1
                }
            ],
            // 地图图例数据
            mapLegend: [
                {
                    label: '1~5名',
                    color: 'rgba(0, 191, 24, 0.65)'
                },
                {
                    label: '6~10名',
                    color: 'rgba(255, 198, 2, 0.65)'
                },
                {
                    label: '11~16名',
                    color: 'rgba(255, 69, 69, 0.65)'
                },
                {
                    label: '无数据',
                    color: '#adadad'
                }
            ]
        };
    },
    watch: {
        data: {
            handler(newVal) {
                if (newVal) {
                    if (this.bottomRadioActive === 1) {
                        this.updateMapColors();
                    } else {
                        this.updateSoftCollectionColors();
                    }
                }
            },
            deep: true,
            immediate: true
        },
        'data.level100Type': {
            handler(newVal) {
                if (newVal && this.bottomRadioActive === 1) {
                    // 当指纹模型定位精度类型变化时更新图例
                    this.updateMapLegend();
                }
            },
            immediate: true
        }
    },
    created() {
        this.getTopData();
    },
    methods: {
        getTopData() {
            this.getPost(
                'post',
                'valueSummary',
                { cityId: this.activeRegionId },
                '能效展板-累计价值实现/对内支撑需求/对外价值体现查询接口',
                (res) => {
                    this.topCards[0].value = res.internalSupport;
                    this.topCards[1].value = (res.totalValue / 10000).toFixed(2);
                    this.topCards[2].value = (res.externalValue / 10000).toFixed(2);
                }
            );
        },
        // 获取城市的颜色
        getCityColor(value, type) {
            if (!value) return '#adadad'; // 无数据时显示浅灰色

            const numValue = parseFloat(value);

            if (type === '4G') {
                if (numValue >= 90) return 'rgba(0, 191, 24, 1)'; // 深绿色
                if (numValue >= 80) return 'rgba(0, 191, 24, 0.65)'; // 绿色
                if (numValue >= 70) return 'rgba(255, 198, 2, 0.65)'; // 黄色
                return 'rgba(255, 69, 69, 0.65)'; // 红色
            } else if (type === '5G') {
                if (numValue >= 80) return 'rgba(0, 191, 24, 1)'; // 深绿色
                if (numValue >= 70) return 'rgba(0, 191, 24, 0.65)'; // 绿色
                if (numValue >= 60) return 'rgba(255, 198, 2, 0.65)'; // 黄色
                return 'rgba(255, 69, 69, 0.65)'; // 红色
            }

            return '#adadad';
        },

        // 获取软采流量的颜色
        getSoftCollectionColor(cityInfo) {
            if (!cityInfo) return '#adadad';

            // 根据排名返回不同的颜色
            const index = this.data.softCollectionRankData.findIndex(
                (item) => item.cityId === cityInfo.cityId
            );
            if (index < 0) return '#adadad';

            if (index < 5) return 'rgba(0, 191, 24, 0.65)'; // 前5名绿色
            if (index < 10) return 'rgba(255, 198, 2, 0.65)'; // 6-10名黄色
            return 'rgba(255, 69, 69, 0.65)'; // 其余红色
        },

        // 更新图例数据
        updateMapLegend() {
            if (this.bottomRadioActive === 0) {
                // 实时软采流量图例
                this.mapLegend = [
                    {
                        label: '1~5名',
                        color: 'rgba(0, 191, 24, 0.65)'
                    },
                    {
                        label: '6~10名',
                        color: 'rgba(255, 198, 2, 0.65)'
                    },
                    {
                        label: '11~16名',
                        color: 'rgba(255, 69, 69, 0.65)'
                    },
                    {
                        label: '无数据',
                        color: '#adadad'
                    }
                ];
            } else if (this.data && this.data.level100Type === '4G') {
                // 4G指纹模型定位精度图例
                this.mapLegend = [
                    {
                        label: '≥90%',
                        color: 'rgba(0, 191, 24, 1)'
                    },
                    {
                        label: '≥80%',
                        color: 'rgba(0, 191, 24, 0.65)'
                    },
                    {
                        label: '≥70%',
                        color: 'rgba(255, 198, 2, 0.65)'
                    },
                    {
                        label: '<70%',
                        color: 'rgba(255, 69, 69, 0.65)'
                    },
                    {
                        label: '无数据',
                        color: '#adadad'
                    }
                ];
            } else if (this.data && this.data.level100Type === '5G') {
                // 5G指纹模型定位精度图例
                this.mapLegend = [
                    {
                        label: '≥80%',
                        color: 'rgba(0, 191, 24, 1)'
                    },
                    {
                        label: '≥70%',
                        color: 'rgba(0, 191, 24, 0.65)'
                    },
                    {
                        label: '≥60%',
                        color: 'rgba(255, 198, 2, 0.65)'
                    },
                    {
                        label: '<60%',
                        color: 'rgba(255, 69, 69, 0.65)'
                    },
                    {
                        label: '无数据',
                        color: '#adadad'
                    }
                ];
            }
        },

        // 更新地图颜色
        updateMapColors() {
            if (!this.data || !this.data.level100List || !this.data.level100Type) return;

            // 更新图例
            this.updateMapLegend();

            const cityData = this.data.level100List;
            const type = this.data.level100Type;

            // 更新省级地图颜色
            const updatedRegions = this.regionData[this.currentLevel].map((region) => {
                const cityInfo = cityData.find((city) => city.name === region.name);
                return {
                    ...region,
                    itemStyle: {
                        ...region.itemStyle,
                        areaColor: this.getCityColor(cityInfo && cityInfo.value, type),
                        borderColor: 'rgba(0, 209, 255, 1)',
                        borderWidth: 1,
                        shadowColor: 'rgba(0, 209, 255, 0.3)',
                        shadowBlur: 5
                    },
                    label: {
                        show: true,
                        color: '#fff'
                    }
                };
            });

            // 更新地图配置
            if (this.centerChart) {
                this.centerChart.setOption({
                    geo: {
                        regions: updatedRegions,
                        itemStyle: {
                            borderColor: 'rgba(0, 209, 255, 1)',
                            borderWidth: 1,
                            shadowColor: 'rgba(0, 209, 255, 0.3)',
                            shadowBlur: 5
                        }
                    }
                });
            }
        },

        // 处理实时软采流量的颜色更新
        updateSoftCollectionColors() {
            if (!this.data || !this.data.softCollectionRankData) return;

            // 更新图例
            this.updateMapLegend();

            const updatedRegions = this.regionData[this.currentLevel].map((region) => {
                const cityInfo = this.data.softCollectionRankData.find(
                    (city) => city.cityName === region.name
                );
                return {
                    ...region,
                    itemStyle: {
                        ...region.itemStyle,
                        areaColor: this.getSoftCollectionColor(cityInfo),
                        borderColor: 'rgba(0, 209, 255, 1)',
                        borderWidth: 1,
                        shadowColor: 'rgba(0, 209, 255, 0.3)',
                        shadowBlur: 5
                    },
                    label: {
                        show: true,
                        color: '#fff'
                    }
                };
            });

            if (this.centerChart) {
                this.centerChart.setOption({
                    geo: {
                        regions: updatedRegions,
                        itemStyle: {
                            borderColor: 'rgba(0, 209, 255, 1)',
                            borderWidth: 1,
                            shadowColor: 'rgba(0, 209, 255, 0.3)',
                            shadowBlur: 5
                        }
                    }
                });
            }
        },
        handleBottomRadioClick(value) {
            this.bottomRadioActive = value;

            // 如果当前在地市层级，重新触发一次点击事件来更新颜色
            if (this.currentLevel === 'city') {
                this.handleMapClick({ name: this.activeRegionName });
                return;
            }

            // 处理省级地图颜色
            if (value === 1) {
                // 指纹模型定位精度
                this.updateMapColors();
            } else {
                // 实时软采流量
                this.updateSoftCollectionColors();
                this.updateMapLegend(); // 更新图例
            }
        },
        handleTopCardClick(type) {
            this.activeTopCardType = type;
            this.dialogVisible = true;
        },
        /** 点击地图下钻 */
        async handleMapClick(params) {
            const feature = this.geoJson.province.features.find(
                (f) => f.properties && f.properties.name === params.name
            );
            if (!feature) return;

            const name = feature.properties.name;
            const adcode = feature.properties.adcode;

            // 更新状态
            this.activeRegionName = name;
            this.activeRegionId = adcode;
            this.currentLevel = 'city';
            this.$emit('changeRegion', {
                name: this.activeRegionName,
                adcode: this.activeRegionId,
                level: 'city'
            });

            // 计算缩放及中心点（仍然使用省级底图）
            const containerSize = {
                width: this.$refs.centerGisMap.clientWidth,
                height: this.$refs.centerGisMap.clientHeight
            };
            const { zoom, center } = this.calculateFitBounds(feature, containerSize);

            // 获取所有地市的颜色配置
            let updatedRegions;
            if (this.bottomRadioActive === 1) {
                // 指纹模型定位精度
                if (this.data && this.data.level100List) {
                    updatedRegions = this.regionData.province.map((region) => {
                        const cityInfo = this.data.level100List.find(
                            (city) => city.name === region.name
                        );
                        const areaColor = this.getCityColor(
                            cityInfo && cityInfo.value,
                            this.data.level100Type
                        );
                        return {
                            ...region,
                            itemStyle: {
                                ...region.itemStyle,
                                areaColor,
                                borderColor: 'rgba(0, 209, 255, 1)',
                                borderWidth: 1,
                                shadowColor: 'rgba(0, 209, 255, 0.3)',
                                shadowBlur: 5
                            },
                            label: {
                                show: region.name !== name,
                                color: '#fff'
                            }
                        };
                    });
                }
            } else {
                // 实时软采流量
                if (this.data && this.data.softCollectionRankData) {
                    updatedRegions = this.regionData.province.map((region) => {
                        const cityInfo = this.data.softCollectionRankData.find(
                            (city) => city.cityName === region.name
                        );
                        const areaColor = this.getSoftCollectionColor(cityInfo);
                        return {
                            ...region,
                            itemStyle: {
                                ...region.itemStyle,
                                areaColor,
                                borderColor: 'rgba(0, 209, 255, 1)',
                                borderWidth: 1,
                                shadowColor: 'rgba(0, 209, 255, 0.3)',
                                shadowBlur: 5
                            },
                            label: {
                                show: region.name !== name,
                                color: '#fff'
                            }
                        };
                    });
                }
            }

            // 更新地图视图及选中状态（基础市级背景 geo）
            // 先构造/注册区县地图名
            const districtFeatures = this.geoJson.city.features.filter((f) => {
                if (!f.properties) return false;
                if (f.properties.parent && f.properties.parent.adcode === adcode) return true;
                const cityPrefix = String(adcode).slice(0, 4);
                return String(f.properties.adcode).startsWith(cityPrefix);
            });
            let districtMapName = null;
            if (districtFeatures.length) {
                districtMapName = `districts_${adcode}`;
                echarts.registerMap(districtMapName, {
                    type: 'FeatureCollection',
                    features: districtFeatures
                });
            }

            if (districtFeatures.length) {
                // 生成区县轮廓线数据和名称坐标
                const districtLines = [];
                const districtLabels = [];
                districtFeatures.forEach((feat) => {
                    const centerPoint = feat.properties.centroid || feat.properties.center;
                    if (centerPoint) {
                        districtLabels.push({ name: feat.properties.name, value: centerPoint });
                    }
                    const coordsSets =
                        feat.geometry.type === 'MultiPolygon'
                            ? feat.geometry.coordinates.flat()
                            : feat.geometry.coordinates;
                    coordsSets.forEach((ring) => {
                        districtLines.push({ coords: ring });
                    });
                });

                this.centerChart.setOption(
                    {
                        geo: {
                            map: 'shandong',
                            center,
                            zoom: zoom / 2.5,
                            animationDurationUpdate: 0,
                            roam: true,
                            scaleLimit: { ...this.mapScaleLimit, max: 10 },
                            emphasis: {
                                disabled: true
                            },
                            regions: updatedRegions,
                            itemStyle: {
                                borderColor: 'rgba(0, 209, 255, 1)',
                                borderWidth: 1,
                                shadowColor: 'rgba(0, 209, 255, 0.3)',
                                shadowBlur: 5
                            },
                            label: { show: false }
                        },
                        series: [
                            {
                                id: 'district-lines',
                                type: 'lines',
                                coordinateSystem: 'geo',
                                polyline: true,
                                silent: true,
                                data: districtLines,
                                animation: false,
                                lineStyle: {
                                    color: 'rgba(0, 209, 255, 1)',
                                    width: 1
                                },
                                z: 5
                            },
                            {
                                id: 'district-labels',
                                type: 'scatter',
                                coordinateSystem: 'geo',
                                symbolSize: 1,
                                label: {
                                    show: true,
                                    formatter: '{b}',
                                    fontSize: 12,
                                    color: '#fff'
                                },
                                data: districtLabels,
                                animation: false,
                                z: 6,
                                silent: true
                            }
                        ]
                    },
                    { replaceMerge: ['geo', 'series'] }
                );
            } else {
                // 如果没有区县数据，仅放大定位
                this.centerChart.setOption({
                    geo: {
                        map: 'shandong',
                        center,
                        zoom: zoom / 2.5,
                        animationDurationUpdate: 0,
                        roam: true,
                        scaleLimit: this.mapScaleLimit,
                        emphasis: {
                            disabled: true
                        },
                        regions: updatedRegions,
                        itemStyle: {
                            borderColor: 'rgba(0, 209, 255, 1)',
                            borderWidth: 1,
                            shadowColor: 'rgba(0, 209, 255, 0.3)',
                            shadowBlur: 5
                        },
                        label: { show: false }
                    },
                    series: []
                });
            }

            // 触发选中效果
            this.centerChart.dispatchAction({
                type: 'select',
                name
            });
        },

        /** 返回上一级 */
        handleBack() {
            this.activeRegionName = '山东';
            this.activeRegionId = null;
            this.currentLevel = 'province';
            this.$emit('changeRegion', {
                name: this.activeRegionName,
                adcode: this.activeRegionId,
                level: 'province'
            });
            echarts.registerMap('shandong', shandongGeoJson);

            // 根据当前模式获取正确的颜色配置
            let regions;
            if (this.bottomRadioActive === 1 && this.data && this.data.level100List) {
                // 指纹模型定位精度
                regions = this.regionData[this.currentLevel].map((region) => {
                    const cityInfo = this.data.level100List.find(
                        (city) => city.name === region.name
                    );
                    return {
                        ...region,
                        label: {
                            show: true,
                            color: '#fff'
                        },
                        itemStyle: {
                            ...region.itemStyle,
                            areaColor: this.getCityColor(
                                cityInfo && cityInfo.value,
                                this.data.level100Type
                            ),
                            borderColor: 'rgba(0, 209, 255, 1)',
                            borderWidth: 1,
                            shadowColor: 'rgba(0, 209, 255, 0.3)',
                            shadowBlur: 5
                        }
                    };
                });
            } else if (this.data && this.data.softCollectionRankData) {
                // 实时软采流量
                regions = this.regionData[this.currentLevel].map((region) => {
                    const cityInfo = this.data.softCollectionRankData.find(
                        (city) => city.cityName === region.name
                    );
                    return {
                        ...region,
                        label: {
                            show: true,
                            color: '#fff'
                        },
                        itemStyle: {
                            ...region.itemStyle,
                            areaColor: this.getSoftCollectionColor(cityInfo),
                            borderColor: 'rgba(0, 209, 255, 1)',
                            borderWidth: 1,
                            shadowColor: 'rgba(0, 209, 255, 0.3)',
                            shadowBlur: 5
                        }
                    };
                });
            } else {
                regions = this.regionData[this.currentLevel];
            }

            this.centerChart.setOption(
                {
                    geo: {
                        map: 'shandong',
                        zoom: 1.2,
                        center: [118.187759, 36.376092],
                        animationDurationUpdate: 0,
                        roam: true,
                        scaleLimit: this.mapScaleLimit,
                        regions: regions,
                        label: {
                            show: true,
                            fontSize: '12',
                            color: '#fff'
                        },
                        itemStyle: {
                            borderColor: 'rgba(0, 209, 255, 1)',
                            borderWidth: 1,
                            shadowColor: 'rgba(0, 209, 255, 0.3)',
                            shadowBlur: 5
                        },
                        emphasis: {
                            disabled: true
                        }
                    },
                    series: [] // 清理叠加的区县轮廓
                },
                { replaceMerge: ['series', 'geo'] }
            );
        },
        initCenterChart() {
            if (this.$refs.centerGisMap) {
                this.centerChart = echarts.init(this.$refs.centerGisMap);
                // 注册山东省地图
                echarts.registerMap('shandong', shandongGeoJson);

                // 初始化时根据当前模式设置颜色
                let regions = this.regionData[this.currentLevel];
                if (this.data) {
                    if (this.bottomRadioActive === 1 && this.data.level100List) {
                        // 指纹模型定位精度
                        regions = this.regionData[this.currentLevel].map((region) => {
                            const cityInfo = this.data.level100List.find(
                                (city) => city.name === region.name
                            );
                            return {
                                ...region,
                                label: {
                                    show: true,
                                    color: '#fff'
                                },
                                itemStyle: {
                                    ...region.itemStyle,
                                    areaColor: this.getCityColor(
                                        cityInfo && cityInfo.value,
                                        this.data.level100Type
                                    ),
                                    borderColor: 'rgba(0, 209, 255, 1)',
                                    borderWidth: 1,
                                    shadowColor: 'rgba(0, 209, 255, 0.3)',
                                    shadowBlur: 5
                                }
                            };
                        });
                    } else if (this.data.softCollectionRankData) {
                        // 实时软采流量
                        regions = this.regionData[this.currentLevel].map((region) => {
                            const cityInfo = this.data.softCollectionRankData.find(
                                (city) => city.cityName === region.name
                            );
                            return {
                                ...region,
                                label: {
                                    show: true,
                                    color: '#fff'
                                },
                                itemStyle: {
                                    ...region.itemStyle,
                                    areaColor: this.getSoftCollectionColor(cityInfo),
                                    borderColor: 'rgba(0, 209, 255, 1)',
                                    borderWidth: 1,
                                    shadowColor: 'rgba(0, 209, 255, 0.3)',
                                    shadowBlur: 5
                                }
                            };
                        });
                    }
                }

                const option = {
                    geo: {
                        show: true,
                        map: 'shandong',
                        center: [118.187759, 36.376092],
                        animationDurationUpdate: 0,
                        roam: true,
                        zoom: 1.2,
                        silent: false,
                        scaleLimit: this.mapScaleLimit,
                        label: {
                            show: true,
                            fontSize: '12',
                            color: '#fff'
                        },
                        itemStyle: {
                            borderColor: 'rgba(0, 209, 255, 1)',
                            borderWidth: 1,
                            shadowColor: 'rgba(0, 209, 255, 0.3)',
                            shadowBlur: 5
                        },
                        emphasis: {
                            disabled: true
                        },
                        selectedMode: 'single',
                        select: {
                            disabled: false,
                            label: {
                                color: '#fff'
                            }
                        },
                        regions: regions
                    },
                    series: [],
                    animation: true
                };
                this.centerChart.setOption(option);

                // 绑定点击事件
                this.centerChart.on('click', this.handleMapClick);

                // 同步多 geo 的拖拽 / 缩放
                this._geoRoamHandlerAttached = false;
                this.centerChart.on('georoam', () => {
                    if (this.currentLevel === 'city') {
                        const opt = this.centerChart.getOption();
                        if (opt.geo && opt.geo.length > 1) {
                            const baseGeo = opt.geo[0];
                            // 更新区县 geo 的中心与缩放
                            this.centerChart.setOption(
                                {
                                    geo: [
                                        {},
                                        {
                                            center: baseGeo.center,
                                            zoom: baseGeo.zoom,
                                            animationDurationUpdate: 0,
                                            emphasis: {
                                                disabled: true
                                            },
                                            roam: true,
                                            scaleLimit: this.mapScaleLimit
                                        }
                                    ]
                                },
                                false
                            );
                        }
                    }
                });

                this.resizeObserver = new ResizeObserver(() => {
                    this.centerChart.resize();
                });
                this.resizeObserver.observe(this.$refs.centerGisMap);
            }
        },
        /**
         * 计算地市轮廓的合适缩放大小和中心点（针对ECharts优化版）
         * @param {Object} feature GeoJSON 地市特征对象
         * @param {Object} containerSize 容器尺寸 {width: number, height: number}
         * @returns {Object} 包含中心点和缩放级别的对象 {center: [lng, lat], zoom: number}
         */
        calculateFitBounds(feature, containerSize) {
            // 1. 获取地市的几何数据
            const geometry = feature.geometry;

            // 2. 提取所有坐标点
            let coordinates = [];

            if (geometry.type === 'MultiPolygon') {
                // 处理MultiPolygon类型
                geometry.coordinates.forEach((polygon) => {
                    polygon.forEach((ring) => {
                        coordinates = coordinates.concat(ring);
                    });
                });
            } else if (geometry.type === 'Polygon') {
                // 处理Polygon类型
                geometry.coordinates.forEach((ring) => {
                    coordinates = coordinates.concat(ring);
                });
            }

            // 3. 计算边界框
            let minLng = Infinity,
                maxLng = -Infinity;
            let minLat = Infinity,
                maxLat = -Infinity;

            coordinates.forEach((coord) => {
                const [lng, lat] = coord;
                minLng = Math.min(minLng, lng);
                maxLng = Math.max(maxLng, lng);
                minLat = Math.min(minLat, lat);
                maxLat = Math.max(maxLat, lat);
            });

            // 4. 计算中心点
            const center = [(minLng + maxLng) / 2, (minLat + maxLat) / 2];

            // 5. 计算合适的缩放级别（针对ECharts优化）
            const lngDiff = maxLng - minLng;
            const latDiff = maxLat - minLat;

            // 计算地理区域的宽高比
            const geoAspectRatio = lngDiff / latDiff;

            // 获取容器宽高比
            const containerAspectRatio = containerSize.width / containerSize.height;

            // 根据宽高比调整
            let fitZoom;
            if (geoAspectRatio > containerAspectRatio) {
                // 以经度差为准
                fitZoom = this.getZoomLevelForSpan(lngDiff, containerSize.width);
            } else {
                // 以纬度差为准
                fitZoom = this.getZoomLevelForSpan(latDiff, containerSize.height);
            }

            // 限制最小和最大缩放级别
            const boundedZoom = Math.min(
                Math.max(fitZoom, this.mapScaleLimit.min),
                this.mapScaleLimit.max
            );

            return {
                center: center,
                zoom: boundedZoom
            };
        },

        /**
         * 根据跨度和像素尺寸计算缩放级别
         * @param {number} span 地理跨度（经度或纬度差）
         * @param {number} pixelSize 像素尺寸（宽度或高度）
         * @returns {number} 缩放级别
         */
        getZoomLevelForSpan(span, pixelSize) {
            // 这个公式需要根据ECharts的实际投影计算调整
            // 以下是近似计算，可能需要根据实际情况微调

            // 假设在zoom 0时，360度对应256像素（类似墨卡托投影）
            // 每放大一级，像素翻倍

            // 计算所需的比例
            const desiredScale = pixelSize / ((span * 256) / 360);

            // 计算zoom level
            return Math.log2(desiredScale);
        }
    },
    mounted() {
        this.initCenterChart();
    },
    beforeDestroy() {
        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
            this.resizeObserver = null;
        }
        if (this.centerChart) {
            this.centerChart.off('click', this.handleMapClick);
            this.centerChart.dispose();
        }
    }
};
</script>

<style lang="less" scoped>
.center-gis-map {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .top {
        height: 7.7778rem;
        display: flex;
        gap: 1.3333rem;

        .top-item {
            flex: 225;
            height: 78%;
            margin-top: 0.3333rem;
            padding: 0.8889rem 1rem 1.3333rem;
            display: flex;
            align-items: flex-start;
            gap: 1.1111rem;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            cursor: pointer;

            &.large {
                flex: 275;
                height: 100%;
                margin-top: 0;
                padding: 1.3333rem 1rem 1.6667rem;

                .icon {
                    width: 3.3333rem;
                    height: 3.3333rem;
                }

                .content {
                    .title {
                        .title-text {
                            font-size: 0.7778rem;
                        }
                    }
                    .info {
                        .info-value {
                            font-size: 1.5556rem;
                        }
                        .info-label {
                            font-size: 0.5556rem;
                        }
                    }
                }
            }

            .icon {
                width: 2.6667rem;
                height: 2.6667rem;
                background-size: 100% 100%;
                background-repeat: no-repeat;
            }

            .content {
                flex: 1;
                display: flex;
                flex-direction: column;
                justify-content: space-between;

                .title {
                    display: flex;
                    align-items: center;
                    gap: 0.4444rem;
                    height: 1.3333rem;

                    .title-text {
                        margin-left: 0.6667rem;
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: 0.6667rem;
                        color: #ffffff;
                        line-height: 1.1111rem;
                        text-shadow: 0 0 0.2778rem rgba(30, 198, 255, 0.8);
                        position: relative;

                        &::before {
                            content: '';
                            position: absolute;
                            left: -0.8889rem;
                            top: 50%;
                            transform: translateY(-50%);
                            width: 0.6667rem;
                            height: 0.6667rem;
                            background: url('../../../../img/effectivenessScreen/traingle.png');
                            background-size: 100% 100%;
                            background-repeat: no-repeat;
                        }
                    }

                    .title-info {
                        width: 0.8889rem;
                        height: 0.8889rem;
                        background: url('../../../../img/effectivenessScreen/title-info.png');
                        background-size: 100% 100%;
                        background-repeat: no-repeat;
                    }
                }

                .info {
                    margin-left: 0.6667rem;
                    display: flex;
                    flex-direction: column;
                    gap: 0.2222rem;

                    .info-value {
                        font-family: YouSheBiaoTiHei;
                        font-size: 1.1111rem;
                        background: linear-gradient(180deg, #ffffff 0%, #9be5ff 50%, #0dcaf5 80%);
                        -webkit-background-clip: text;
                        background-clip: text;
                        color: transparent;
                    }

                    .info-label {
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: 0.5556rem;
                        color: #b0d7ff;
                        text-shadow: 0 0 0.2778rem rgba(30, 198, 255, 0.8);
                    }
                }
            }
        }
    }
    .center {
        flex: 1;
        display: flex;
        flex-direction: column;
        .center-header {
            display: flex;
            align-items: center;
            gap: 0.5556rem;
            .header-back-btn {
                width: 4.4444rem;
                height: 1.4444rem;
                background-image: url('../../../../img/effectivenessScreen/btn-back.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                cursor: pointer;
                /* 点击返回 */
                &:active {
                    transform: scale(0.98);
                }
            }
            .header-location {
                display: flex;
                align-items: center;
                gap: 0.2222rem;
                img {
                    width: 1rem;
                    height: 1rem;
                }
                span {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 500;
                    font-size: 0.8889rem;
                    color: #ffffff;
                }
            }
        }
        .center-main {
            width: 100%;
            flex: 1;
            padding: 0.5556rem 0;
            position: relative;
            .center-gis-map {
                width: 100%;
                height: 100%;
            }
            .map-legend {
                user-select: none;
                position: absolute;
                right: 1rem;
                bottom: 1rem;
                width: 5.5556rem;
                height: fit-content;
                background: linear-gradient(
                    180deg,
                    rgba(26, 92, 179, 0.3) 0%,
                    rgba(26, 92, 179, 0.3) 100%
                );
                backdrop-filter: blur(0.8rem);
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                .map-legend-title {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 0.6667rem;
                    color: #b0d7ff;
                    padding: 0.6667rem 0.5556rem 0.2222rem;
                }
                .map-legend-content {
                    min-height: 0;
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-around;
                    gap: 0.2222rem;
                    padding: 0 0.5556rem;
                    padding-bottom: 0.6667rem;
                    .map-legend-item {
                        display: flex;
                        align-items: center;
                        gap: 0.3333rem;
                        .map-legend-item-color {
                            width: 1.2778rem;
                            height: 0.7222rem;
                            border-radius: 0.1111rem;
                        }
                        .map-legend-item-label {
                            font-family: PingFangSC, PingFang SC;
                            font-weight: 400;
                            font-size: 0.6667rem;
                            color: #b0d7ff;
                        }
                    }
                }
            }
        }
    }
    .bottom {
        height: 1.7778rem;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        .bottom-problem {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 0.6667rem;
            color: #b0d7ff;
            position: absolute;
            left: 0;
            box-shadow: inset 0 0 0.3889rem 0 rgba(18, 156, 237, 0.5);
            border-radius: 0.1111rem 0 0 0.1111rem;
            border: 0.0556rem solid #0b66b3;
            width: fit-content;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.2222rem;
            padding: 0 0.5556rem;
            user-select: none;
            cursor: pointer;
            img {
                width: 0.6667rem;
                height: 0.6667rem;
            }
        }
        .bottom-radio {
            height: 100%;
            display: flex;
            align-items: center;
            .bottom-radio-item {
                width: 7.7778rem;
                height: 100%;
                box-shadow: inset 0 0 0.3889rem 0 rgba(18, 156, 237, 0.5);
                border: 0.0556rem solid #0b66b3;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 0.6667rem;
                color: #b0d7ff;
                display: flex;
                align-items: center;
                justify-content: center;
                user-select: none;
                cursor: pointer;
                &:first-child {
                    border-radius: 0.1111rem 0 0 0.1111rem;
                }
                &:last-child {
                    border-radius: 0 0.1111rem 0.1111rem 0;
                }
                &.active {
                    background: rgba(5, 13, 50, 0.9);
                    box-shadow: inset 0 0 0.8889rem 0.1111rem rgba(0, 149, 255, 0.8);
                    border-radius: 0.1111rem 0 0 0.1111rem;
                    border: 0.0556rem solid #0095ff;
                    filter: blur(0px);
                    font-weight: 500;
                    color: rgba(0, 149, 255, 1);
                }
            }
        }
    }
}
</style>
