export const DEF_GRID_OPTION = {
    pageOn: true, //是否启用分页
    pageSkip: true, //是否启用分页中的跳页
    pageSize: 200, //分页行数
    pageNumberSize: 5, //显示页码数
    pageChangeSize: true, //是否可以改变每页的行数
    pageSizeArray: [50, 100, 200, 500], //Array，行数的可选值，比如[50, 100, 200, 500]。（必须开启changeSize才生效）

    sortable: true, //是否启用排序
    filterable: true, //是否显示过滤
    frozenColCount: 0, //冻结表头的列的数量
    frozenColShrinkableCount: 0, //冻结列的可收缩的列数（由右往左开始计数）
    frozenColShrinkingFields: false, //冻结列是否默认收起可收缩的列
    // multipleSelect: false,             //是否可以多选（若checkbox为true，则此配置失效，固定为多选）
    checkbox: false, //是否在第一列显示复选框
    checkboxOnly: false, //是否只有点击复选框，才能改变复选框状态
    nullValueChange: false, //是否修改数据为null的显示，默认显示成"-"，可以通过nullValueSet设置显示值
    nullValueSet: '-', //null的数据显示值，默认显示为"-"，需要通过nullValueChange开启此功能
    autoFillColumn: false, //是否自动补充表头的信息
    rowIndex: false, //是否显示行号
    autoFillEmptyRow: true, //是否自动填充空行

    mergeField: '', //合并单元格的列字段（多个用逗号分隔）
    mergeTogether: false, //合并单元格的列字段多个的时候，是否一起生效

    exportDatas: true, //导出表格数据
    exportInFooterPanel: false, //底部显示导出按钮
    exportDefaultName: '位置能力', //导出文件的默认名称

    treeField: '', //树结构，子节点的字段。（空字符串则表示没有子节点）
    treeCollapse: false, //树结构，是否默认收起

    footerPanel: true, //显示底层面板

    trSolidColor: false, //行数据的背景颜色（true：纯色，false：颜色相间）
};

export default {
};