<!-- 右键菜单 -->
<template>
  <div v-show="isShow" class="radiation-range" :style="{ top: y, left: x }">
    <div class="head">
      <span class="name">辐射范围</span>
      <i class="el-icon-close" @click="close()"></i>
    </div>
    <div class="body">
      <div class="distance">
        <span>距离：</span>
        <el-input-number
          v-model="distance"
          controls-position="right"
          size="mini"
          :min="3"
          :max="10"
        />
        <span>公里</span>
      </div>
      <el-button type="primary" size="mini" @click="sure">确认</el-button>
    </div>
  </div>
</template>

<script>
const WIDTH = 302;
const HEIGHT = 80;
export default {
  name: 'radiation-range',
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    coordinate: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      x: '',
      y: '',
      distance: 3,
    };
  },
  mounted() {
    this.$watch(
      'coordinate',
      (newVal) => {
        this.setPosition(newVal);
      },
      { immediate: true }
    );
  },
  methods: {
    setPosition({ x, y }) {
      const menuWidth = WIDTH;
      const menuHeight = HEIGHT;

      let innerWidth = window.innerWidth;
      let innerHeight = window.innerHeight;

      let posX = x;
      let posY = y;
      if (posX + menuWidth > innerWidth) {
        posX = innerWidth - menuWidth;
      }
      if (posY + menuHeight > innerHeight) {
        posY = innerHeight - menuHeight;
      }

      this.x = posX + 'px';
      this.y = posY + 'px';
    },
    sure() {
      this.$emit('sure', this.distance);
      this.close();
    },
    close() {
      this.distance = 3;
      this.$emit('update:isShow', false);
    },
  },
};
</script>

<style lang="less" scoped>
.radiation-range {
  // 固定定位，抬高层级，初始隐藏，右击时置为display:block显示
  position: fixed;
  display: block;
  margin: 0;
  padding: 12px;
  width: 302px;
  color: #333;
  background: #fff;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  box-sizing: border-box;
  z-index: 3001;
  box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
  .head {
    display: flex;
    justify-content: space-between;
    .name {
      font-size: 14px;
      font-weight: bold;
      cursor: pointer;
    }
    .el-icon-close {
      font-size: 14px;
    }
  }
  .body {
    margin-top: 10px;
    display: flex;
    justify-content: space-between;
  }
}
</style>
