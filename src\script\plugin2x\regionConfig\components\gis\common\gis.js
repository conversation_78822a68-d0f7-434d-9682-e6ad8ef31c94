const gisOptions = (mapType) => {
    return {
        initialZoom: 11,
        initPointLat: 36.6663163,
        initPointLng: 117.0702181,
        antialias: true,
        city: {
            lng: 117.0702181,
            lat: 36.6663163,
            cityID: 370100,
            cityName: '济南市'
        },
        options: {
            mapLayer: {
                visible: true,
                mapType,
                colorControl: true
            },
            amapMapLayer: {
                visible: true,
                colorControl: true
            },
            cameraControl: {
                type: '2D',
                minZoom: 4,
                maxZoom: 17,
                wheelLevelChange: true
            },
            showInfo: {
                visible: true,
                float: true
            },
            tool: {
                searchVisible: true,
                baiduSearch: false,
                buttonVisible: false,
                toolBoxVisible: false, //工具箱 不显示
                layerVisible: false,
                baiduReverseGeocoding: false,
                circleChoice: false,
                boxSelectLayer: true
            },
            lineLayer: true, //线条
            panoramaLayer: {
                visible: false
            },
            circleChoice: {
                //圈选
                circleColor: 0x4f2cde,
                circleOpacity: 0.5,
                circleFrame: true,
                circleFrameColor: 0x734ce3,
                circleShowRadius: true, // 该属性会导致辅助圆出现半径巨大的情况
                cirCleShowClose: true
            },
            boxSelectLayer: true,
            lineEditLayer: false,
            LacCiNumberTimer: null,
            areaEditLayer: true
        }
    };
};

const menuList = [
    {
        prop: 'outwardRadiate',
        label: '向外辐射'
    },
    {
        prop: 'cancelRadiation',
        label: '取消辐射'
    },
    {
        prop: 'hole',
        label: '设置空洞'
    },
    {
        prop: 'auxiliaryCircle',
        label: '画辅助圆'
    },
    {
        prop: 'delHole',
        label: '删除'
    },
    {
        prop: 'delCircle',
        label: '删除'
    },
    {
        prop: 'delPolygon',
        label: '删除区域'
    },
    {
        prop: 'delCircular',
        label: '删除区域'
    }
];
const testBaseStations = [
    {
        lng: 121.32697245898774,
        lat: 30.7362419408735,
        dir: 0,
        ht: 2,
        size: 120
    },
    {
        lng: 121.32346770500183,
        lat: 30.7367460619387,
        dir: 0,
        ht: 2,
        size: 120
    }
];

const mapStatus = {
    '': 'noAdd',
    noAdd: 'added',
    added: 'noAdd'
};

export { gisOptions, menuList, testBaseStations, mapStatus };
