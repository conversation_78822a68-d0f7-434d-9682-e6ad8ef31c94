<template>
    <div class="location">
        <div class="location-search">
            <searchBar class="searchBar" ref="formList" :formCols="formCols" :form="form">
                <template #search>
                    <el-button type="primary" size="small" @click="search">查询</el-button>
                </template>
            </searchBar>
        </div>
        <mtv-gis
            class="gis"
            ref="LocationGisMap"
            :totaloptions="gistotalOptions"
            @onLoad="gisOnLoad"
        ></mtv-gis>
    </div>
</template>

<script>
import searchBar from '_com/searchBar/searchBar.vue';
import commonMixins from '@/script/mixins/commonMixins.js';
import { gistotalOptions, getMayType } from '@/script/constant/gis.js';
export default {
    name: 'Location',
    components: {
        searchBar
    },
    mixins: [commonMixins],
    data() {
        return {
            formCols: [
                [
                    {
                        type: 'el-input',
                        prop: 'msisdn',
                        label: '用户号码：',
                        labelWidth: '85px',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true,
                            class: 'displayPass'
                        },
                        rules: [
                            // { required: true, message: '必填', trigger: 'submit' }
                        ],
                        span: 10,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'el-date-picker',
                        prop: 'queryMonth',
                        label: '日期选择：',
                        labelWidth: '100px',
                        attrs: {
                            placeholder: '请选择',
                            type: 'month',
                            clearable: true,
                            'value-format': 'yyyyMM'
                        },
                        rules: [
                            // { required: true, message: '必填', trigger: 'submit' }
                        ],
                        span: 10,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'tool',
                        prop: 'tool',
                        span: 4,
                        isShow: true,
                        labelWidth: '30px'
                    }
                ]
            ],
            form: {
                msisdn: '',
                queryMonth: ''
            },
            gistotalOptions: gistotalOptions,
            gisLoaded: false,
            imgLayer: null
        };
    },
    methods: {
        search() {
            if (!this.gisLoaded) {
                this.$message('地图尚未初始化完成，请稍候重试');
                return;
            }
            if (!this.form.msisdn) {
                this.$message.error('请先输入用户号码！');
                return;
            }
            this.clearGisData();
            this.$exloading1x();
            this.getPost(
                'post',
                'jobsHousing',
                {
                    ...this.form
                },
                '用户实时位置查询',
                (data) => {
                    this.$exloaded1x();
                    if (!data) {
                        this.$message('该查询条件暂无数据，请重试！');
                        return;
                    }
                    let list = {};
                    if (data.workLongitude && data.workLatitude) {
                        list['居住地'] = {
                            lng: data.workLongitude,
                            lat: data.workLatitude,
                            data: '居住地',
                            width: 56,
                            ht: 0.2
                        };
                    }
                    if (data.liveLongitude && data.liveLatitude) {
                        list['工作地'] = {
                            lng: data.liveLongitude,
                            lat: data.liveLatitude,
                            data: '工作地',
                            width: 56,
                            ht: 0.2
                        };
                    }
                    this.drawLocationImg(list);
                }
            );
        },
        clearGisData() {
            if (this.imgLayer) {
                this.imgLayer.removeAll();
            }
            const GIS = this.$refs['LocationGisMap'].getEntity();
            GIS.layerList.divLayer.removeAll();
        },
        gisOnLoad() {
            const gis = this.$refs['LocationGisMap'].getEntity();
            // 设置底图
            if (getMayType() === 'default') {
                gis.tileLayerList['底图图层'].visible = false;
                //重写getUrl方法
                gis.tileLayerList['高德底图'].getUrl = (x, y, z) => {
                    return `${location.origin}/MapUrl/api/appmaptile?x=${x}&y=${y}&z=${z}`;
                };
            } else {
                gis.tileLayerList['高德底图'] && (gis.tileLayerList['高德底图'].visible = false);
            }
            gis.downLoad.onDownloadFinish.addOneEvent(() => {
                this.gisLoaded = true;
            });
        },
        drawLocationImg(list) {
            if (!list['居住地'] && !list['工作地']) {
                return;
            }
            let data = Object.keys(list).map((item) => {
                return list[item];
            });
            const GIS = this.$refs['LocationGisMap'].getEntity();
            this.imgLayer = new GIS.layer();
            this.imgLayer.name = 'imgTest';
            GIS.gis.scene.add(this.imgLayer.Group);
            this.imgLayer.visible = true;
            //传入图片url获取材质
            let material1 = GIS.meshList.img.getMaterial({
                url: require('../../../img/gis/workPlace.png'),
                opacity: 1
            });
            let material2 = GIS.meshList.img.getMaterial({
                url: require('../../../img/gis/habitation.png'),
                opacity: 1
            });
            data.autoScale = false;
            //生成模型
            if (list['居住地']) {
                let imgMesh1 = GIS.meshList.img.create([list['居住地']], material1);
                this.imgLayer.add(imgMesh1);
            }
            if (list['工作地']) {
                let imgMesh2 = GIS.meshList.img.create([list['工作地']], material2);
                this.imgLayer.add(imgMesh2);
            }
            //模型添加进图层
            this.drawingTips(data);
            //更新GIS
            GIS.gis.needUpdate = true;
            GIS.cameraControl.move(data[0]);
            GIS.cameraControl.zoom = 17;
            GIS.cameraControl.zoomByPoints(data, 4);
        },
        drawingTips(data) {
            const GIS = this.$refs['LocationGisMap'].getEntity();
            if (!data.length) {
                return;
            }
            data.forEach((item, index) => {
                const data = {
                    dom: `<div class="tips-wrapper">
                            ${item.data}
                        </div>`,
                    point: {
                        lat: item.lat + 0.0004,
                        lng: item.lng
                    }
                };
                data.autoScale = true;
                GIS.layerList.divLayer.addDiv(data);
            });
        }
    }
};
</script>

<style lang="less" scoped>
.location {
    width: 100%;
    height: 100%;
    &-search {
        width: 50%;
        height: 80px;
        display: flex;
        align-items: center;
        padding-top: 10px;
    }
    .gis {
        width: 100%;
        height: calc(100% - 80px);
    }
}
/deep/.tips-wrapper {
    transform: translate(-50%, -50%);
    min-width: 80px;
    height: 24px;
    display: flex;
    align-items: center;
    position: relative;
    background: #fff;
    box-shadow: 0px 0px 7px 0px #b7b7b7;
    border-radius: 2px;
    padding-left: 15px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    text-align: center;
}
.searchBar {
    /deep/.displayPass {
        .el-input__inner {
            -webkit-text-security: disc !important;
        }
    }
}
</style>
