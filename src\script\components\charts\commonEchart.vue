<template>
    <div class="out-box">
        <el-empty
            v-if="JSON.stringify(targetData) === '{}'"
            description="暂无数据"
            :image="require('../../../img/noDataMon.png')"
        >
        </el-empty>
        <div v-else :ref="refName" :id="refName" class="common-echart"></div>
        <slot name="top"></slot>
    </div>
</template>

<script>
import * as echarts from 'echarts';
const legendIcon = {
    default: 'rect',
    solid: 'path://M833.1 475.2 H192.6 v61.5 H833.1 Z',
    dashed: 'path://M192.6 475.2 h80 v61.5 h-80 Z M379.43 475.2 h80 v61.5 h-80 Z M566.26 475.2 h80 v61.5 h-80 Z M753.1 475.2 h80 v61.5 h-80 Z'
};

// 提取生成SVG图标的函数
const generateIconHtml = (lineType, color) => {
    return {
        dashed: `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="12" viewBox="0 0 20 12">
                    <rect x="0" width="3" height="2" y="5" fill="${color}"></rect>
                    <rect x="6" width="3" height="2" y="5" fill="${color}"></rect>
                    <rect x="12" width="3" height="2" y="5" fill="${color}"></rect>
                    <rect x="17" width="3" height="2" y="5" fill="${color}"></rect>
                </svg>`,
        solid: `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="12" viewBox="0 0 20 12">
                    <rect width="20" height="2" y="5" fill="${color}"></rect>
                </svg>`,
        default: `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="12" viewBox="0 0 20 12">
                    <rect width="20" height="12" fill="${color}"></rect>
                </svg>`
    }[lineType];
};

// 提取创建系列项的函数
const createSeriesItem = (name, data, type, lineType, color, unit) => {
    return {
        name,
        data,
        type: type || 'line',
        symbolSize: 6,
        smooth: false,
        unit,
        barWidth: '30%',
        itemStyle: {
            color: (() => {
                if (type === 'bar') {
                    return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                            offset: 0,
                            color: color
                        },
                        {
                            offset: 0.2,
                            color: color + '90'
                        },
                        {
                            offset: 0.4,
                            color: color + '80'
                        },
                        {
                            offset: 0.6,
                            color: color + '40'
                        },
                        {
                            offset: 1,
                            color: color + '20'
                        }
                    ]);
                }
                return color || 'rgba(118, 131, 143, 1)';
            })()
        },
        lineStyle: {
            type: lineType || 'solid'
        },
        areaStyle: (() => {
            if (type === 'line') {
                return {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0,
                                color: color + '40' // 40% opacity
                            },
                            {
                                offset: 1,
                                color: color + '00' // 0% opacity
                            }
                        ]
                    }
                };
            }
            return undefined;
        })()
    };
};

const formatValue = (value, unit) => {
    let newVal = value;
    if (unit.includes('百万')) {
        newVal = (value / 10 ** 6).toFixed(2);
    }
    return newVal;
};

const initEcharts = (myChart, options) => {
    const seriesType = [];
    // const legendGroup = {};
    const option = {
        textStyle: {
            fontFamily: 'PingFangSC, PingFang SC'
        },
        title: {
            show: options.titleShow,
            left: 0,
            top: 0,
            text: options.title,
            textStyle: {
                color: '#fff',
                fontSize: 12,
                fontWeight: 500
            }
        },
        grid: {
            top: options.titleShow ? 70 : 40,
            left: 50,
            right: 15,
            bottom: 65
        },
        legend: {
            show: options.lengendShow,
            width: '80%',
            type: 'scroll',
            pageIconColor: '#c9dfff',
            pageIconInactiveColor: '#04346AFF',
            pageIconSize: 10,
            pageTextStyle: {
                color: '#c9dfff',
                fontSize: 12
            },
            top: options.titleShow ? 33 : 3,
            right: 10,
            textStyle: {
                color: '#c9dfff',
                fontSize: 12
            },
            itemWidth: 20,
            itemHeight: 12,
            itemGap: 10,
            data: [],
            formatter: function (params) {
                return params;
                // if (!options.isGroup) {
                //     return params;
                // }
                // const names = params.split('-');
                // options.lineData.forEach((item) => {
                //     if (item.name === names[0]) {
                //         legendGroup[names[0]] || (legendGroup[names[0]] = []);
                //         legendGroup[names[0]].push(params);
                //     }
                // });
                // return names.at(-1);
            }
        },
        tooltip: {
            confine: true,
            trigger: 'axis',
            backgroundColor: 'rgba(0,16,36,0.95)',
            borderWidth: 0,
            formatter: function (params) {
                // 创建基本标题
                let title = params[0].axisValue;
                let htmlStr = `<div style="color: #fff; padding: 5px 10px; margin-bottom: 5px;">${title}</div>`;

                // 添加每个数据系列
                htmlStr += '<div style="padding: 0 10px 5px;">';

                // 检查是否为分组数据
                if (options.isGroup) {
                    // 按分组名称对数据进行分类
                    const groupedData = {};

                    params.forEach((param) => {
                        if (param.data !== undefined && param.data !== null) {
                            // 从系列名称中提取分组名称和项目名称 (例如: "2分钟以下-今日")
                            const nameParts = param.seriesName.split('-');
                            if (nameParts.length === 2) {
                                const groupName = nameParts[0];
                                const itemName = nameParts[1];

                                if (!groupedData[groupName]) {
                                    groupedData[groupName] = [];
                                }

                                let value = formatValue(
                                    param.data,
                                    option.series[param.seriesIndex].unit || ''
                                );

                                groupedData[groupName].push({
                                    name: itemName,
                                    value,
                                    color: param.color,
                                    lineType: seriesType[param.seriesIndex],
                                    unit: option.series[param.seriesIndex].unit || ''
                                });
                            }
                        }
                    });

                    // 使用网格布局显示数据
                    for (const groupName in groupedData) {
                        // 创建网格容器
                        htmlStr += `
                        <div style="display: grid; gap: 20px; grid-template-columns: 70px 1fr 1fr; align-items: center; margin-top: 6px;">
                            <div style="color: #c9dfff; margin-bottom: 4px;">${groupName}：</div>
                        `;

                        // 创建网格项
                        groupedData[groupName].forEach((item) => {
                            const iconHtml = generateIconHtml(item.lineType, item.color);
                            // 中间的span(图例名) <span style="color: #c9dfff; margin-right: 10px;">${item.name}</span>
                            htmlStr += `
                            <div style="display: flex; align-items: center; margin-bottom: 4px;">
                                <span style="display: inline-flex; align-items: center; margin-right: 6px;">${iconHtml}</span>
                                <span style="color: #fff; font-weight: 500;">${item.value}${item.unit}</span>
                            </div>`;
                        });

                        htmlStr += '</div>';
                    }
                } else {
                    // 在这里存储我们已经显示的系列，以便跟踪重复项
                    const displayedSeries = new Set();

                    // 使用网格布局
                    htmlStr +=
                        '<div style="display: grid; grid-template-columns: auto 1fr; align-items: center; gap: 8px 16px;">';

                    params.forEach((param) => {
                        if (
                            param.data !== undefined &&
                            param.data !== null &&
                            !displayedSeries.has(param.seriesName)
                        ) {
                            // 标记这个系列已显示
                            displayedSeries.add(param.seriesName);

                            let value = formatValue(
                                param.data,
                                option.series[param.seriesIndex].unit || ''
                            );
                            // 获取单位
                            let unit = option.series[param.seriesIndex].unit || '';

                            const color = {
                                string: param.color,
                                object: param.color.colorStops && param.color.colorStops[0].color
                            };
                            const iconHtml = generateIconHtml(
                                seriesType[param.seriesIndex],
                                color[typeof param.color]
                            );

                            htmlStr += `
                            <div style="display: flex; align-items: center;">
                                <span style="display: inline-flex; align-items: center; margin-right: 6px;">${iconHtml}</span>
                                <span style="color: #c9dfff;">${param.seriesName}</span>
                            </div>
                            <div style="color: #fff; font-weight: 500; text-align: right;">
                                ${value}${unit}
                            </div>`;
                        }
                    });

                    htmlStr += '</div>';
                }

                htmlStr += '</div>';
                return htmlStr;
            }
        },
        dataZoom: [
            {
                type: 'inside',
                start: 0,
                end: 100
            },
            {
                start: 0,
                end: 100,
                bottom: 0,
                left: 50,
                right: 15,
                height: 20,
                showDetail: false,
                showDataShadow: false,
                backgroundColor: '#04346AFF',
                fillerColor: 'rgba(0, 149, 255, 0.3)',
                borderColor: '#00000000',
                borderRadius: 0,
                moveHandleSize: 0,
                handleIcon: 'image://' + require('../../../img/icon/handle-icon.png'),
                handleSize: 20,
                handleStyle: {
                    color: 'rgba(0, 149, 255, 1)',
                    borderWidth: 0
                }
            }
        ],
        xAxis: {
            type: 'category',
            data: options.xAxisData,
            axisTick: { show: false },
            axisLabel: {
                color: '#c9dfff',
                fontSize: 12,
                margin: 10
            },
            axisLine: {
                show: true,
                lineStyle: {
                    color: 'rgba(0, 149, 255, 0.3)'
                }
            },
            splitLine: {
                show: true,
                interval: 0,
                lineStyle: {
                    type: 'solid',
                    color: 'rgba(0, 149, 255, 0.3)'
                }
            },
            axisPointer: {
                show: true,
                type: 'shadow',
                shadowStyle: {
                    color: 'rgba(0, 149, 255, 0.3)'
                }
            }
        },
        yAxis: {
            type: 'value',
            name: options.yAxisName,
            nameTextStyle: {
                color: '#c9dfff',
                fontSize: 12,
                align: 'left',
                padding: [0, 0, 5, -45]
            },
            axisTick: { show: false },
            axisLabel: {
                margin: 4,
                fontSize: 12,
                color: '#c9dfff',
                formatter: function (val) {
                    return formatValue(val, options.yAxisName || '');
                }
            },
            axisLine: {
                show: true, // 是否显示坐标轴轴线
                lineStyle: {
                    color: 'rgba(0, 149, 255, 0.3)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'solid',
                    color: 'rgba(0, 149, 255, 0.3)'
                }
            }
        },
        series: []
    };

    // 添加判断，处理不同的lineData结构
    if (options.isGroup) {
        // 处理分组数据结构
        options.lineData.forEach((group) => {
            // 遍历每个分组内的数据
            for (const key in group) {
                if (key !== 'name') {
                    const item = group[key];
                    seriesType.push(item.lineType || 'default');

                    // 使用提取的函数创建系列项
                    const seriesItem = createSeriesItem(
                        `${group.name}-${item.name}`,
                        item.data,
                        item.type,
                        item.lineType,
                        item.color,
                        item.unit
                    );

                    option.series.push(seriesItem);

                    option.legend.data.push({
                        name: `${group.name}-${item.name}`,
                        icon: legendIcon[item.lineType || 'default'],
                        itemStyle: {
                            color: item.color || 'rgba(118, 131, 143, 1)',
                            borderWidth: 0
                        }
                    });
                }
            }
        });
    } else {
        // 原有的处理逻辑
        options.lineData.forEach((item, index) => {
            seriesType.push(item.lineType || 'default');

            // 使用提取的函数创建系列项
            const seriesItem = createSeriesItem(
                item.name,
                item.data,
                item.type,
                item.lineType,
                item.color,
                item.unit
            );

            option.series.push(seriesItem);

            option.legend.data.push({
                name: item.name,
                icon: legendIcon[item.lineType || 'default'],
                itemStyle: {
                    color: item.color || 'rgba(118, 131, 143, 1)',
                    borderWidth: 0
                }
            });
        });
    }

    myChart.clear();
    myChart.setOption(option);
};
export default {
    name: 'commonEchart',
    /**
     * targetData: {
                title: '每分钟记录总量',
                titleShow: true,
                xAxisData: [
                    '2024-01-09\n13:00',
                    '2024-01-09\n13:05',
                    '2024-01-09\n13:10',
                    '2024-01-09\n13:15',
                    '2024-01-09\n13:25',
                    '2024-01-09\n13:30',
                    '2024-01-09\n13:35',
                    '2024-01-09\n13:40',
                    '2024-01-09\n13:45',
                    '2024-01-09\n13:50',
                    '2024-01-09\n13:55',
                    '2024-01-09\n14:00'
                ],
                yAxisName: '记录数（条）',
                lengendShow: true,
                isGroup: false,
                // isGroup: false时lineData数据结构
                lineData: [
                    {
                        name: '今日',
                        type: 'line',
                        data: [
                            6423, 2000, 7248, 5432, 17084, 4824, 7442, 20270, 3215, 8527, 7473, 7413
                        ],
                        unit: '条',
                        lineType: 'solid', // type为bar时，不要设置lineType
                        color: '#01E6FE'
                    },
                    {
                        name: '昨日',
                        type: 'line',
                        data: [
                            3215, 8527, 7473, 7413, 6423, 2000, 7248, 5432, 7084, 4824, 7442, 22700
                        ],
                        unit: '条',
                        lineType: 'dashed',
                        color: '#FFDF12'
                    }
                ]
                // isGroup: true时lineData数据结构
                // lineData: [
                //     {
                //         name: '2分钟以下',
                //         1: {
                //             name: '今日',
                //             type: 'line',
                //             data: [
                //                 1250, 3450, 2800, 4200, 5100, 3800, 4500, 6700, 5200, 7800, 6300,
                //                 5500
                //             ],
                //             lineType: 'solid',
                //             color: '#0095FF'
                //         },
                //         2: {
                //             name: '昨日',
                //             type: 'line',
                //             data: [
                //                 2100, 1800, 3600, 2400, 3900, 5200, 2800, 4100, 6800, 5400, 3700,
                //                 4300
                //             ],
                //             lineType: 'dashed',
                //             color: '#0095FF'
                //         }
                //     },
                //     {
                //         name: '2~5分钟',
                //         1: {
                //             name: '今日',
                //             type: 'line',
                //             data: [
                //                 8500, 7200, 9400, 12300, 10500, 11800, 13200, 15700, 14300, 16800,
                //                 15200, 17500
                //             ],
                //             lineType: 'solid',
                //             color: '#FFDF12'
                //         },
                //         2: {
                //             name: '昨日',
                //             type: 'line',
                //             data: [
                //                 9200, 10500, 7800, 8300, 11200, 9600, 10800, 12500, 11000, 13400,
                //                 15800, 14200
                //             ],
                //             lineType: 'dashed',
                //             color: '#FFDF12'
                //         }
                //     },
                //     {
                //         name: '5分钟以上',
                //         1: {
                //             name: '今日',
                //             type: 'line',
                //             data: [
                //                 22500, 19800, 24300, 21700, 25800, 23400, 27600, 26100, 28900,
                //                 30200, 29500, 32100
                //             ],
                //             lineType: 'solid',
                //             color: '#0FFF81'
                //         },
                //         2: {
                //             name: '昨日',
                //             type: 'line',
                //             data: [
                //                 18200, 23500, 19700, 24800, 20300, 25600, 22100, 26800, 24200,
                //                 20800, 25300, 23800
                //             ],
                //             lineType: 'dashed',
                //             color: '#0FFF81'
                //         }
                //     }
                // ]
            }
     */
    props: {
        targetData: {
            type: Object,
            default: () => ({})
        },
        type: {
            type: String,
            default: 'line'
        },
        refName: {
            type: String,
            default: 'common-echart'
        }
    },
    data() {
        return {
            myChart: ''
        };
    },
    watch: {
        targetData: {
            handler(newV) {
                if (newV.lineData) {
                    // 只更新图表配置，不重新初始化
                    this.updateEchart();
                }
            },
            deep: true
        }
    },
    mounted() {
        if (this.targetData && this.targetData.lineData) {
            this.initEchart();
        }
    },
    methods: {
        initEchart() {
            this.$nextTick(() => {
                // 只有当图表实例不存在时才初始化
                if (!this.myChart) {
                    this.myChart = echarts.init(document.getElementById(this.refName));
                }
                initEcharts(this.myChart, this.targetData);
            });
        },
        updateEchart() {
            this.$nextTick(() => {
                // 确保图表实例已经创建
                if (!this.myChart) {
                    this.initEchart();
                    return;
                }
                // 只更新配置，不重新创建实例
                initEcharts(this.myChart, this.targetData);
            });
        }
    }
};
</script>

<style lang="less" scoped>
.out-box {
    width: 100%;
    height: 100%;
    position: relative;
}
.common-echart {
    width: 100%;
    height: 100%;
}
.el-empty {
    height: 100%;
}
</style>
