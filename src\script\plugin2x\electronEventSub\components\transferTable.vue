<template>
    <div class="transfer-table">
        <div class="transfer-header">
            <div :class="['transfer-header-l', headerIsActive ? 'header80' : 'header40']">
                <com-tool-bar
                    size="small"
                    :gutter="'24px'"
                    :toolbarItems="toolbarItems"
                    @changeToolBarItem="changeToolBarItem"
                >
                    <template #city>
                        <el-cascader
                            style="margin-top: 2px; width: 160px"
                            v-model="searchOption.city"
                            :options="casCity"
                            placeholder="全部"
                            :props="{ expandTrigger: 'hover', label: 'name' }"
                            size="small"
                            filterable
                            popper-class="cascader-menu-dark popover-dark"
                            @change="changeToolBarItem('city')"
                        ></el-cascader>
                    </template>
                    <template #timer>
                        <el-date-picker
                            v-model="times"
                            type="datetimerange"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            size="small"
                            popper-class="date-picker-dark popover-dark"
                            :default-time="['00:00:00', '23:59:59']"
                        >
                        </el-date-picker>
                    </template>
                </com-tool-bar>
            </div>
            <div class="transfer-header-r">
                <el-button type="primary" size="small" @click="getAreaData()">查询</el-button>
            </div>
        </div>
        <div class="table-out" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.2)">
            <table-list
                ref="tableL"
                :title="'区域列表'"
                :btn="'全选'"
                :hasSelectAll="hasSelectAll"
                :regionList="newData"
                :totalNumber="totalNumber"
                :selectList="selectList"
                @removeRow="removeRow"
                @click-row="checkChange"
                @checkedAll="checkedAll"
                @getNewList="getNewList"
                @changeSelectItem="changeSelectItem"
                @changeSelectList="changeSelectList"
            ></table-list>
            <div class="middle" style="width: 30px">
                <i class="el-icon-right"></i>
            </div>
            <table-list
                ref="selectTableList"
                :title="'已选列表'"
                :btn="'清空'"
                :isSelectList="true"
                :selectMore="false"
                :regionList="showSelectList"
                :totalNumber="selectList.length"
                @click-row="checkChange"
                @cleanAll="cleanAll"
                @getNewList="changShowList"
                @changeSelectList="changeSelectList"
                @removeRow="removeRow"
            ></table-list>
            <!-- <tree-list
                ref="treeR"
                :title="'已选列表'"
                :showCheckbox="false"
                :btn="'清空'"
                :treeData="checkedTreeData"
                :slotRight="true"
                @checkedTree="checkedTree"
                @remove-node="removeNode"
            ></tree-list> -->
        </div>
    </div>
</template>
<script>
import treeList from '_com/treeList.vue';
import tableList from '_com/tableList.vue';
import comToolBar from '_com/comToolBar.vue';
import commonMixins from '@/script/mixins/commonMixins.js';

export default {
    components: { comToolBar, tableList, treeList },
    props: {},
    mixins: [commonMixins],
    data() {
        return {
            loading: false,
            typeOption: {
                eventType: [],
                areaType: [],
                userType: [],
                status: [],
                searchType: [],
                shapeType: []
            },
            headerIsActive: true,
            listLoading: true,
            staffTemp: {
                shapeType: '',
                regionId: '',
                staffTypeId: ''
            },
            leftTableList: [],
            rightTableList: [],
            checkedLeftList: [],
            checkedRightList: [],
            citys: [],
            city: '',
            province: '',
            toolbarItems: [
                [
                    {
                        field: 'regionId',
                        type: 'input',
                        width: '160px',
                        label: '    区域ID:'
                    },
                    {
                        field: 'regionName',
                        width: '160px',
                        type: 'input',
                        label: '区域名称:'
                    },
                    { field: 'city', type: 'slot', label: '地市:' },

                    {
                        field: 'shapeType',
                        type: 'select',
                        label: '轮廓类型:',
                        width: '160px',
                        options: [
                            {
                                value: '全部',
                                label: '全部'
                            },
                            {
                                value: 1,
                                label: '圆形'
                            },
                            {
                                value: 2,
                                label: '多边形'
                            }
                        ]
                    },

                    { field: 'timer', type: 'slot', label: '创建时间:' }
                ]
            ],
            toolbarItems2: [[]],
            times: '',
            layerTypes: [],
            areaList: [],
            casCity: [], //级联城市
            searchOption: {
                city: [],
                layerIDs: [],
                regionTypeIDs: [],
                isValid: '',
                endTime: '',
                fieldName: '',
                fieldOrder: '',
                startTime: '',
                searchType: 1
            },
            checkedTreeData: [],
            newData: [],
            enumList: [],
            roadDirOptions: [],
            shapeTypeOptions: [],
            totalNumber: 0,
            selectList: [],
            showSelectList: [],
            hasSelectAll: false
        };
    },
    methods: {
        removeRow(row) {
            const index = this.selectList.findIndex((item) => item.regionId === row.regionId);
            if (index !== -1) {
                this.selectList.splice(index, 1);
            }
            const page = this.$refs.selectTableList.page;
            this.changShowList(page);
            const indexL = this.newData.findIndex((item) => item.regionId === row.regionId);
            if (indexL !== -1) {
                this.$refs.tableL.toggleRowSelection(row, false);
            }
        },
        checkedAll() {
            this.hasSelectAll = true;
            this.getAreaData(
                {
                    num: 1,
                    size: this.totalNumber
                },
                true
            );
        },
        cleanAll() {
            this.selectList = [];
            this.$refs.tableL.clearSelection();
            this.changShowList();
        },
        checkChange(list) {},
        changShowList(page) {
            if (!this.selectList.length) {
                this.showSelectList = [];
                return;
            }
            let list = [];
            let start = (page.num - 1) * page.size;
            let end = page.num * page.size;
            if (end > this.selectList.length) {
                end = this.selectList.length;
            }
            list = this.selectList.slice(start, end);
            this.showSelectList = list;
        },
        // --------------
        checkedTreeL(data) {
            this.checkedTreeData = JSON.parse(JSON.stringify(data));
        },
        checkedTree() {
            if (this.checkedTreeData.length) {
                this.checkedTreeData.forEach((item) => {
                    this.removeAllNode(item);
                    this.checkedTreeData = [];
                });
            } else {
                this.checkedTreeData = [];
                this.cleanAll();
            }
        },
        initData() {
            this.checkedTree();
        },
        checkedParents(data, node) {
            let flag = false;
            if (!data.parent) {
                return true;
            }
            let checkedNodes = node.checkedNodes;
            for (let i = 0; i < checkedNodes.length; i++) {
                if (
                    data.id === checkedNodes[i].id &&
                    data.parent.id === checkedNodes[i].parent.id
                ) {
                    flag = true;
                }
            }
            return flag;
        },
        clickCheckbox(data, node) {
            if (node.checkedKeys.includes(data.id) && this.checkedParents(data, node)) {
                this.setCheckedTreeData(data);
            } else {
                this.removeNode({}, data);
            }
        },
        hasParent(id) {
            return this.checkedTreeData.findIndex(function filter(item, index, _this) {
                return item.id === id;
            });
        },
        setCheckedTreeData(_data) {
            const data = JSON.parse(JSON.stringify(_data));
            if (data.parent) {
                const index = this.hasParent(data.parent.id);
                if (index !== -1) {
                    this.checkedTreeData[index].children.push(data);
                } else {
                    this.checkedTreeData.push({
                        ...data.parent,
                        children: [data]
                    });
                }
            } else if (this.hasParent(data.id) === -1) {
                this.checkedTreeData.push(data);
            } else if (this.hasParent(data.id) !== -1) {
                this.$set(this.checkedTreeData, this.hasParent(data.id), data);
            }
        },
        removeAllNode(data) {
            this.$refs.treeL.setChecked(data.id, false, true);
        },
        removeNode(node, data) {
            if (
                data.parent &&
                this.checkedTreeData[this.hasParent(data.parent.id)].children.length === 1
            ) {
                this.$refs.treeL.setChecked(data.parent.id, false, true);
                this.handlerRemoveNode(this.checkedTreeData, data.parent.id);
            } else {
                this.$refs.treeL.setChecked(data.id, false, true);
                this.handlerRemoveNode(this.checkedTreeData, data.id);
            }
        },
        handlerRemoveNode(list, id) {
            for (let i = 0; i < list.length; i++) {
                const item = list[i];
                if (item.id === id) {
                    list.splice(i, 1);
                }
                if (item.children) {
                    this.handlerRemoveNode(item.children, id);
                }
            }
            for (let i = 0; i < list.length; i++) {
                if (list[i].children && list[i].children.length === 0) {
                    list.splice(i, 1);
                }
            }
        },
        async getCityList() {
            this.getPost('post', 'getCityDistrict', {}, '地市区县获取', (results) => {
                const citys = results.cityList || [];
                this.handleCityList(citys);
                citys.unshift({ name: '全部', value: '全部' });
                this.citys = citys;
            });
        },
        // 转级联城市
        handleCityList(citys) {
            let hasCity = JSON.parse(JSON.stringify(citys));
            if (hasCity.length === 1 && hasCity[0].children.length === 1) {
                this.searchOption.city = [hasCity[0].value, hasCity[0].children[0].value];
            } else {
                hasCity.unshift({
                    value: '全部',
                    name: '全部'
                });
                this.searchOption.city = ['全部'];
            }
            this.casCity = hasCity;
        },

        async getsearchOptions() {
            await this.getCityList();
        },
        async changeToolBarItem(data) {
            if (data === 'city') {
                this.getAreaData();
                return;
            }
            this.searchOption[data.field] = data.value;
            // this.getAreaData();
        },
        async getAreaData(page, isSelect = false) {
            this.loading = true;
            if (!page) {
                this.hasSelectAll = false; // 清空多选
            }
            //请求参数
            let searchCity = this.searchOption.city;
            let caseCity = searchCity.length === 2 ? searchCity[1] : '';
            let params = {
                ...this.searchOption,
                isValid: this.searchOption.isValid,
                startTime: this.times ? this.times[0] : '',
                endTime: this.times ? this.times[1] : '',
                city: caseCity,
                pageIndex: 1,
                rowsPage: 100,
                shapeType: this.searchOption.shapeType === '全部' ? '' : this.searchOption.shapeType
            };
            if (page) {
                params.pageIndex = page.num;
                params.rowsPage = page.size;
            }
            if (!isSelect) {
                this.newData = [];
            }
            this.getPost(
                'post',
                'searchRegion',
                params,
                '区域查询',
                (res) => {
                    this.loading = false;
                    this.handlerNewList(res, isSelect);
                },
                () => {
                    this.loading = false;
                }
            );
        },
        handlerNewList(data, isSelect = false) {
            const regionList = data.regionList;
            let newData = [];
            regionList.forEach((item) => {
                let region = { ...item };
                let layerNamesCn = '';
                if (item.attributes && item.attributes.layerNames) {
                    layerNamesCn = item.attributes.layerNames.join(',');
                }
                region.layerNamesCn = layerNamesCn;
                newData.push(region);
            });
            if (isSelect) {
                this.selectList = [];
                this.selectList = newData;
                this.$refs.selectTableList.initPage();
                this.changShowList({ num: 1, size: 100 });
                return;
            }
            this.newData = newData;
            this.totalNumber = data.totalNumber;
        },
        getNewList(page) {
            this.getAreaData(page);
        },
        changeSelectList(list) {
            list.forEach((item) => {
                const index = this.selectList.findIndex(
                    (child) => child.regionId === item.regionId
                );
                if (index === -1) {
                    this.selectList.push(item);
                }
            });
            const page = this.$refs.selectTableList.page;
            this.changShowList(page);
        },
        changeSelectItem(row) {
            const index = this.selectList.findIndex((child) => child.regionId === row.regionId);
            if (index === -1) {
                this.selectList.push(row);
                const page = this.$refs.selectTableList.page;
                this.changShowList(page);
            }
        },
        getCheckedTreeList() {
            let areaList = [];
            if (!this.selectList.length) {
                return [];
            }
            this.selectList.forEach((item) => {
                areaList.push(item.regionId);
            });
            areaList = [...new Set(areaList)];
            return areaList;
        }
    },
    async created() {
        await this.getsearchOptions();
        this.getAreaData();
    },
    watch: {
        selectList(val) {
            let areaList = [];
            if (!val.length) {
                areaList = [];
            } else {
                val.forEach((item) => {
                    if (areaList.indexOf((child) => child.regionId === item.id) === -1) {
                        areaList.push(item);
                    }
                });
            }
            this.$emit('changSelectList', areaList);
        }
    }
};
</script>
<style lang="less" scoped>
.transfer-table {
    width: 100%;
    padding: 0 0px 10px 0px;

    .com-tool-bar2 {
        margin-left: 12px;
    }

    .table-out {
        position: relative;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;

        .tree-list {
            width: 50%;
            margin: 0 10px;
        }

        .middle {
            text-align: center;
            width: 30px;
            height: 410px;
            line-height: 410px;
            font-size: 20px;
            color: #c9dfff;
        }

        .btn-group {
            text-align: center;
            width: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }

        .label-flag {
            position: absolute;
            top: 0;
            right: calc(50% - 30px);
            z-index: 1;
            color: #f56c6c;
            margin-right: 4px;
        }
    }

    .transfer-header {
        margin-top: 9px;
        margin-bottom: 16px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;
        padding: 16px 0;
        background: rgba(0, 50, 102, 0.5);

        .transfer-header-l {
            flex: 1;
            overflow: hidden;
        }

        .header40 {
            height: 40px;
        }

        .header80 {
            height: auto;
        }

        .transfer-header-r {
            width: 150px;
            margin-left: 71px;
        }
    }

    .flex {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;
        height: 60px;
        line-height: 60px;

        .form-item {
            margin: 0 10px;
        }

        .select {
            width: 35%;
        }

        .search-input {
            width: 60%;
        }
    }
}
</style>
