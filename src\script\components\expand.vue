<template>
    <div class="expand-box">
        <div class="header" @click="changeExpanding">
            <slot name="header">
                <span class="title">{{ title }}</span>
                <i :class="['icon', isExpand ? 'el-icon-arrow-down' : 'el-icon-arrow-up']"></i
            ></slot>
        </div>
        <div
            class="content"
            :style="{
                height: height
            }"
        >
            <slot name="content"></slot>
        </div>
    </div>
</template>
<script>
export default {
    name: 'expand',
    props: {
        title: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            isExpand: true,
            height: 'auto'
        };
    },
    methods: {
        changeExpanding() {
            this.isExpand = !this.isExpand;
            this.$emit('change-expanding', this.isExpand);
            if (this.isExpand) {
                this.height = '';
            } else {
                this.height = '0px';
            }
        }
    },
    watch: {}
};
</script>
<style lang="less" scoped>
.expand-box {
    .header {
        width: 100%;
        height: 40px;
        line-height: 40px;
        background: var(--dark-blue);
        text-indent: 16px;
        color: var(--white-text);
        font-size: 16px;
        cursor: pointer;
        .title {
        }
        .icon {
            float: right;
            width: 40px;
            height: 40px;
            line-height: 40px;

            &:hover {
                // background: #fff;
            }
        }
    }
    .content {
        overflow: hidden;
        transition: height 1s ease-in-out;
    }
}
</style>
