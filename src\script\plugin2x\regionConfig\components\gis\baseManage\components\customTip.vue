<template functional>
    <div class="custom-tip">
        <slot>
            <i class="el-icon-warning"></i>
            <span>{{ props.tip }}</span>
        </slot>
    </div>
</template>
<script>
export default {
    name: 'custom-tip',
    props: {
        tip: {
            type: String,
            default: '请加入同一图层类型的多边形区域'
        }
    }
};
</script>
<style lang="less" scoped>
.custom-tip {
    padding-left: 10px;
    width: 100%;
    height: 32px;
    line-height: 32px;
    color: #0091ff;
    border-radius: 4px;
    background: linear-gradient(180deg, rgba(0, 145, 255, 0.1) 0%, rgba(0, 145, 255, 0.25) 100%);
}
</style>
