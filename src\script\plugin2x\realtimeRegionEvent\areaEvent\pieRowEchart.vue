<template>
    <div class="out-box">
        <el-empty
            v-if="JSON.stringify(pieData) === '{}'"
            description="暂无数据"
            :image="require('../../../../img/noDataMon.png')"
        ></el-empty>
        <div v-else ref="pieRowEchart" :id="name" class="pie-row-echart"></div>
    </div>
</template>

<script>
import * as echarts from 'echarts';
const initEcharts = (myChart, options, bgWH = 240, bgLeft = '5.6%') => {
    const option = {
        // 添加 graphic 配置（图片背景）
        graphic: {
            type: 'image',
            top: 'center',
            left: bgLeft,
            style: {
                // 使用网络图片或本地静态资源（需通过 require 引入）
                image: require('../../../../img/realtimeRegionEvent/pie-bg.png'), // 本地图片路径
                width: bgWH, // 图片宽度（适配饼图大小）
                height: bgWH,
                opacity: 1 // 透明度
            },
            z: -1 // 确保图片在饼图下方
        },
        title: {
            left: 10,
            top: 10,
            text: options.title,
            textStyle: {
                color: '#fff',
                fontSize: 14
            }
        },
        tooltip: {
            trigger: 'item'
        },
        legend: {
            top: 'center',
            left: '50%',
            align: 'left',
            orient: 'vertical',
            itemWidth: 10,
            itemHeight: 10,
            startAngle: 270,
            formatter: function (name) {
                return '{a|' + name + '}{b|' + '|    ' + options.percent[name] + '}';
            },
            textStyle: {
                color: '#B3DAF4FF',
                rich: {
                    a: {
                        width: 80
                    },
                    b: {
                        color: '#fff'
                    }
                }
            }
        },
        grid: {
            top: 'center',
            left: '5%',
            right: '5%',
            bottom: 10
        },
        series: [
            {
                type: 'pie',
                name: options.title,
                left: '10%',
                right: '60%',
                radius: ['50%', '80%'],
                avoidLabelOverlap: false,
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    scaleSize: 15,
                    label: {
                        show: true,
                        color: '#00FFFFFF',
                        fontSize: 18,
                        fontWeight: 'bolder'
                    }
                },
                labelLine: {
                    show: false
                },
                data: options.pieData
            }
        ]
    };
    myChart.clear();
    myChart.setOption(option);
};
export default {
    name: 'pieRowEchart',
    props: {
        pieData: {
            type: Object,
            default: () => ({})
        },
        name: {
            type: String,
            default: 'pie'
        }
    },
    data() {
        return {
            myChart: ''
        };
    },
    watch: {
        pieData: {
            handler(newV) {
                if (newV.pieData) {
                    this.initEchart();
                }
            },
            deep: true
        }
    },
    mounted() {
        if (this.pieData && this.pieData.pieData) {
            this.initEchart();
        }
    },
    methods: {
        initEchart() {
            this.$nextTick(() => {
                this.myChart = echarts.init(document.getElementById(this.name));
                initEcharts(this.myChart, this.pieData, this.pieData.bgWH, this.pieData.bgLeft);
            });
        }
    }
};
</script>

<style lang="less" scoped>
.el-empty {
    height: 100%;
}
.out-box,
.pie-row-echart {
    width: 100%;
    height: 100%;
    position: relative;
    // &::before {
    //     content: '';
    //     position: absolute;
    //     top: 2%;
    //     left: 8%;
    //     width: 34%;
    //     height: 95%;
    //     z-index: 0;
    //     background-image: url('../../../../img/realtimeRegionEvent/pie-bg.png');
    //     background-repeat: no-repeat;
    //     background-size: 100% 100%;
    // }
}
</style>
