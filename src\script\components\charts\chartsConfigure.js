// 折线图柱状图配置

const chartList = {
    // 折线图有区域颜色
    singleLineArea:(list) => singleLineArea(list),
    // 折线图无区域颜色+legend居右
    lineRightLegend:(list) =>lineRightLegend(list),
    // 居中图例折线图
    lineCenterLengend:(list) =>lineCenterLengend(list),
};
const lineChartsCon = (name,options) =>  {
    return chartList[name](options);
};
// 公共折线图配置
const comomLineCon = (options) => {
    const option={
        title: { 
            left:20,
            top:0,
            text: options.title || '',
            textStyle:{
                color:'#ffffff',
                fontSize:14,
            }
        },
        tooltip:{
            confine: true,
            trigger: 'axis',
            axisPointer:{
                type:'shadow',
                shadowStyle:{
                    color:'rgba(52, 134, 255, 0.1)',
                }
            },
            formatter:function (params) {
                let res = `<div>${options.time[params[0].dataIndex]}</div>`;
                params.forEach((item,index) => {
                    res += `<div style="display:flex;justify-content: space-between;"><span style="display:inline-block;">${params[index].marker}${params[index].seriesName}</span> <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;${params[index].data!=null ? params[index].data :'-'}&nbsp;${options.unit[index]}</span></div>`;
                    if(options.topList[params[index].seriesName] && options.topList[params[index].seriesName][item.dataIndex] && options.topList[params[index].seriesName][item.dataIndex].length){
                        options.topList[params[index].seriesName][item.dataIndex].forEach((val) =>{
                            res +=`<div style="padding-left:20px;color:#999"><span style="display:inline-block;">${val.provinceName}</span><span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;${val.percent!= null ?val.percent:'-'}&nbsp;${options.unit[index]}</span></div>`;
                        });
                    }
                });
                return res;
            },
            backgroundColor:'rgba(0, 0, 0, 0.5)',
            borderWidth:0,
            textStyle:{
                color:'#ccc',
            }
        },
        dataZoom: [
            {
                type: 'inside',
                start: 0,
                end: 100,
            },
        ],
        grid: {
            top: 55,
            left: 30,
            right: 30,
            bottom: 10,
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: options.xAxisData,
            axisTick: { show: false },
            boundaryGap:true,
            axisLabel: {
                color: 'RGBA(255, 255, 255, 0.8)',
                fontSize: 12,
            },
            axisLine: {
                lineStyle: {
                    color: 'RGBA(128, 128, 128, 0.8)',
                },
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type:'dashed',
                    color: 'RGBA(128, 128, 128, 0.2)',
                },
            },
        },
        yAxis:options.yAxis.map((item,index) => {
            const data = {
                type: 'value',
                name: item || '',
                nameTextStyle: {
                    fontSize: 11,
                    padding: index === 0 ?[20, 0, 0, 15]:[20,10,0,0],
                    color:'RGBA(255, 255, 255, 0.8)',
                },
                min:options.minValue[index] != null ? options.minValue[index] : function (value) {
                    return Math.floor(value.min - 1) || 0;
                },
                axisLine: { show: false },
                axisTick: { show: false },
                axisLabel: {
                    margin: 4,
                    fontSize: 11,
                    color:'RGBA(255, 255, 255, 0.8)',
                },
                splitLine: {
                    lineStyle: {
                        type:'dashed',
                        color: 'RGBA(128, 128, 128, 0.2)',
                    },
                },
            };
            if(options.minInterval[index]){
                data.minInterval = options.minInterval[index];
            }
            return data;
        }),
        series:[],
    };
    return option;
};
const singleLineArea= (options)=>{
    const option= comomLineCon(options);
    option.series =  options.lineData.map((item,index) => {
        return{
            name: item.name,
            data: item.data,
            type: 'line',
            smooth:true,
            unit:item.unit,
            symbol: 'circle',
            yAxisIndex: item.yAxisIndex?item.yAxisIndex:0,
            areaStyle:item.areaStyle,
            itemStyle: {
                color: item.color || 'rgba(118, 131, 143, 1)',
                borderColor: 'rgba(0,136,212,0.2)',
            },
        };
    });
    return option;
};
const lineRightLegend = (options) => {
    const option= comomLineCon(options);
    option.legend = {
        show: true,
        top: 25,
        right: 20,
        itemHeight: 4,
		itemWidth: 12,
        itemGap:30,
        textStyle: {
            fontSize: 11,
            color:'RGBA(255, 255, 255, 0.8)',
        },
        icon:'rect',
        formatter:function (name) {
            if(options.NotShowGranularity){
                return name;
            }
            const index = options.lineData.findIndex((item) => item.name === name);
            return name+'('+options.granularity[index] +')';
        },
    };
    option.series =  options.lineData.map((item,index) => {
        const data ={
            name: item.name,
            data: item.data,
            type: 'line',
            unit:item.unit,
            symbol: 'circle',
            yAxisIndex: item.yAxisIndex?item.yAxisIndex:0,
            itemStyle: {
                color: item.color || 'rgba(118, 131, 143, 1)',
                borderColor: 'rgba(0,136,212,0.2)',
            },
        };
        if(JSON.stringify(item.areaStyle)!=='{}'){
            data['smooth'] = true;
            data['areaStyle'] = item.areaStyle;
        }
        return data;
    });
    return option;
};
const lineCenterLengend = (options) => {
    const option= comomLineCon(options);
    option.legend = {
        show: true,
        left:'center',
        top:25,
        itemHeight: 4,
		itemWidth: 12,
        itemGap:30,
        textStyle: {
            fontSize: 11,
            color:'RGBA(255, 255, 255, 0.8)',
        },
        icon:'rect',
        formatter:function (name) {
            // 不显示括号内时间粒度
            if(options.NotShowGranularity){
                return name;
            }
            const index = options.lineData.findIndex((item) => item.name === name);
            return name+'('+options.granularity[index] +')';
        },
    };
    option.series =  options.lineData.map((item,index) => {
        const data ={
            name: item.name,
            data: item.data,
            type: 'line',
            unit:item.unit,
            symbol: 'circle',
            yAxisIndex: item.yAxisIndex?item.yAxisIndex:0,
            itemStyle: {
                color: item.color || 'rgba(118, 131, 143, 1)',
                borderColor: 'rgba(0,136,212,0.2)',
            },
        };
        if(JSON.stringify(item.areaStyle)!=='{}'){
            data['smooth'] = true;
            data['areaStyle'] = item.areaStyle;
        }
        return data;
    });
    return option;
};
export{
    lineChartsCon
};