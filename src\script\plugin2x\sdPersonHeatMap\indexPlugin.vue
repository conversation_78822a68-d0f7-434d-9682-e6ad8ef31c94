<template>
    <!-- 地市级常驻人员分布热力图 -->
    <div class="person-heat-map mtex-shandong-devops-dark-theme">
        <comNavTitle
            :navList="navList"
            :isShowInfo="true"
            :infoText="'说明：地市级常驻人员分布热力图'"
        >
            <searchBar
                class="searchBar"
                ref="formList"
                :formCols="formCols"
                :form="form"
                :isTransparentBg="true"
            >
                <template #search>
                    <el-button type="primary" size="small" @click="searchHandler">查询</el-button>
                </template>
                <template #reset>
                    <el-button class="reset-btn" size="small" @click="reset">重置</el-button>
                </template>
            </searchBar>
        </comNavTitle>
        <mtv-gis
            class="gis"
            ref="personGisMap"
            :totaloptions="gistotalOptions"
            @onLoad="gisOnLoad"
        ></mtv-gis>
        <heat-map-legend title="图例" :values="legendValues" v-if="ledgendShow" />
    </div>
</template>

<script>
import searchBar from '_com/searchBar/searchBar.vue';
import comNavTitle from '_com/comNavTitle.vue';
import heatMapLegend from '_com/heatMapLegend.vue';
import commonMixins from '@/script/mixins/commonMixins.js';
import { gistotalOptions, getMayType, changeGisColor } from '@/script/constant/gis.js';
export default {
    components: {
        searchBar,
        comNavTitle,
        heatMapLegend
    },
    props: {
        outsideParam: {
            type: Array,
            default: () => []
        }
    },
    mixins: [commonMixins],
    data() {
        return {
            navList: this.outsideParam,
            formCols: [
                [
                    {
                        span: 8,
                        isShow: true
                    },
                    {
                        type: 'select',
                        prop: 'cityId',
                        label: '地市：',
                        labelWidth: '78px',
                        attrs: {
                            'popper-class': 'select-dropdown-dark popover-dark'
                        },
                        rules: [
                            // { required: true, message: '必填', trigger: 'submit' }
                        ],
                        span: 6,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'select',
                        prop: 'residentType',
                        label: '职住模型：',
                        labelWidth: '85px',
                        attrs: {
                            'popper-class': 'select-dropdown-dark popover-dark'
                        },
                        rules: [
                            // { required: true, message: '必填', trigger: 'submit' }
                        ],
                        span: 6,
                        isDisabled: false,
                        isShow: true,
                        opts: [
                            {
                                value: 1,
                                label: '居住'
                            },
                            {
                                value: 2,
                                label: '工作'
                            }
                        ]
                    },
                    {
                        type: 'tool',
                        prop: 'tool',
                        span: 4,
                        isShow: true,
                        labelWidth: '15px'
                    }
                ]
            ],
            form: {
                cityId: '',
                residentType: ''
            },
            gistotalOptions: gistotalOptions,
            gisLoaded: false,
            hotList: [],
            legendValues: ['0', '1500', '3000', '4500+'],
            ledgendShow: false
        };
    },
    created() {
        this.getCityData();
    },
    methods: {
        // 获取地市数据
        getCityData() {
            this.getPost('post', 'getCityDistrict', {}, '地市区县获取', (data) => {
                const { cityList } = data;
                if (!cityList) {
                    return;
                }
                const list = cityList.map((item) => {
                    return {
                        label: item.name,
                        value: item.value
                    };
                });
                this.$set(this.formCols[0][1], 'opts', list);
            });
        },
        gisOnLoad() {
            const gis = this.$refs['personGisMap'].getEntity();
            // 设置底图
            if (getMayType() === 'default') {
                gis.tileLayerList['底图图层'].visible = false;
                //重写getUrl方法
                gis.tileLayerList['高德底图'].getUrl = (x, y, z) => {
                    return `${location.origin}/MapUrl/api/appmaptile?x=${x}&y=${y}&z=${z}`;
                };
            } else {
                gis.tileLayerList['高德底图'] && (gis.tileLayerList['高德底图'].visible = false);
            }
            changeGisColor(gis);
            gis.downLoad.onDownloadFinish.addOneEvent(() => {
                this.gisLoaded = true;
            });
            gis.cameraControl.onNeedDownLoad.addEvent(this.reRenderHotMap);
        },
        searchHandler() {
            if (!this.gisLoaded) {
                this.$message('地图尚未初始化完成，请稍候重试');
                return;
            }
            if (!this.form.cityId) {
                this.$message.error('请先选择地市！');
                return;
            }
            if (!this.form.residentType) {
                this.$message.error('请先选择职住模型！');
                return;
            }
            this.search(this.form);
            this.ledgendShow = true;
        },
        search(params) {
            this.getPost(
                'post',
                'cityHotMap',
                {
                    ...params
                },
                '地市职住热力信息查询',
                (data) => {
                    let minLat = 0;
                    let maxLat = 0;
                    let minLng = 0;
                    let maxLng = 0;
                    this.hotList = data.cityHeatList.map((item) => {
                        minLat = minLat === 0 ? item.latitude : Math.min(minLat, item.latitude);
                        maxLat = Math.max(maxLat, item.latitude);
                        minLng = minLng === 0 ? item.longitude : Math.min(minLng, item.longitude);
                        maxLng = Math.max(maxLng, item.longitude);
                        return {
                            lat: item.latitude,
                            lng: item.longitude,
                            personNum: item.personNum
                        };
                    });
                    if (this.hotList.length) {
                        this.reRenderHotMap(true);

                        const gis = this.$refs['personGisMap'].getEntity();
                        const list = {
                            lat: (minLat + maxLat) / 2,
                            lng: (minLng + maxLng) / 2
                        };
                        gis.cameraControl.move(list);
                        gis.cameraControl.zoom = 12;
                        gis.cameraControl.zoomByPoints(list, 1.2);
                    }
                }
            );
        },
        reset() {
            this.form.cityId = '';
            this.form.residentType = '';
            this.ledgendShow = false;
            if (this.gisLoaded && window.hotLayer) {
                window.hotLayer.removeAll();
            }
        },
        getRadius() {
            const gis = this.$refs['personGisMap'].getEntity();
            return Math.pow((38 - gis.getGisPosition().zoom) * 2, 2);
        },
        reRenderHotMap(firstLoad) {
            const radius = this.getRadius();
            const list = this.hotList.map((item) => {
                let opacity = item.personNum / 4500;
                return {
                    lat: Number(item.lat),
                    lng: Number(item.lng),
                    opacity: opacity > 1 ? 1 : opacity,
                    radius
                };
            });
            this.renderHotMap(list, true, firstLoad);
        },
        // 绘制热力图
        renderHotMap(list, clearData, firstLoad) {
            if (clearData && window.hotLayer) {
                window.hotLayer.removeAll();
            }
            const gis = this.$refs['personGisMap'].getEntity();
            let tuli = {
                legends: {
                    0.25: 'rgb(9,36,128)',
                    0.55: 'rgb(0,255,0)',
                    0.65: 'rgb(170,255,50)',
                    0.7: 'rgb(200,255,50)',
                    0.85: 'rgb(255,200,0)',
                    0.9: 'rgb(255,160,0)',
                    0.94: 'rgb(255,100,0)',
                    0.98: 'rgb(255,40,0)',
                    1.0: 'rgb(255,0,0)'
                },
                opacity: 1, //热力图最高不透明度，比如这里配置的红色，对应的渲染不透明度为1，本属性可进一步控制生成的图片的不透明度
                opacityHold: 1, //热力图图例不透明度超过该值的都设置为上面opacity的值，默认与opacity相同
                minOpacity: 0 //最小不透明度，即完全透明，代表没有数值的区域的不透明度，默认值为0，设置为其他>0的值则可为空白区域填充像素
            };
            //生成热力图材质
            let mat = gis.meshList.hotMap.getMaterial(tuli);
            if (firstLoad) {
                gis.cameraControl.zoomByPoints(list, 10);
            }
            // 准备热力图数据

            const { leftDown, rightTop } = gis.math.getViewPointsLatLng();
            list.maxLat = rightTop.lat;
            list.minLat = leftDown.lat;
            list.minLng = leftDown.lng;
            list.maxLng = rightTop.lng;
            //传入数据材质生成热力图模型
            let mesh = gis.meshList.hotMap.create(list, mat);
            mesh.name = 'hotmap';
            //创建图层装载热力图模型
            if (!window.hotLayer) {
                window.hotLayer = new gis.layer();
            }
            window.hotLayer.visible = true;
            window.hotLayer.name = 'hotmap';
            window.hotLayer.renderOrder = true;
            window.hotLayer.renderOrderIndex = 2;
            window.hotLayer.add(mesh);
            gis.gis.scene.add(window.hotLayer.Group);
            //更新GIS
            gis.gis.needUpdate = true;
        }
    }
};
</script>

<style lang="less" scoped>
@import url('../../../style/custom.less');
.person-heat-map {
    width: 100%;
    height: 100%;
    position: relative;
    .gis {
        width: 100%;
        height: calc(100% - 80px);
    }
}
.searchBar {
    width: 1000px;
    margin-left: auto;
    /deep/.el-select .el-input__inner {
        background-color: transparent;
        border: 1px solid rgba(18, 139, 207, 0.6);
        color: #fff;
    }
    /deep/.el-select .el-input .el-select__caret {
        color: #fff;
    }
}
</style>
