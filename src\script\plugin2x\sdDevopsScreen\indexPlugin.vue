<template>
    <div class="big-screen sd-devops-overflow-y--black" @click="screenClick">
        <!-- 拖拽区域 -->
        <vuedraggable
            class="draggable"
		 	:group="{name:'devops'}"
            :sort="true"
			animation="300"
            handle=".canmove"
			:list="cardList"
            @end="onEnd"
		>
            <template v-for="(list,listIndex) in cardList">
                <component
                    class="box"
                    :ref="list.uuid"
                    :key="listIndex"
                    :is="list.comp"
                    :name="list.name"
                    :uuid="list.uuid"
                    :widgetId="list.widgetId"
                    :widgetParams="list.widgetParams"
                />
            </template>
            <!-- 添加组件 -->
            <div class="add-com box" @click="dialogVisible = true">
                <i class="el-icon-plus plus"></i>
            </div>
        </vuedraggable>
        <add-widget :visible.sync="dialogVisible" @addCom="addCom"></add-widget>
    </div>
</template>

<script>
import vuedraggable from 'vuedraggable';
import addWidget from './components/addWidget.vue';
import {currencyComp} from './components/index.js';
import {getUuid} from '@/script/common/util.js';
import {widgetConfig,widgetNameList} from './common/index.js';
import commonMixins from '@/script/mixins/commonMixins.js';
export default {
    name:'devopsScreen',
    mixins:[commonMixins],
    components:{
        vuedraggable,
        addWidget,
        ...currencyComp,
    },
    provide(){
        return{
            closeCard:this.closeCard,
            replaceCard:this.replaceCard,
            updateComList:this.updateComList,
        };
    },
    data(){
        return{
            cardList:[
            
            ],
            dialogVisible:false,
            replaceIndex:null,
            closeCardTimer:null,
        };
    },
    beforeDestroy() {
        this.closeCardTimer && clearTimeout(this.closeCardTimer);
    },
    created(){
        
    },
    mounted(){
        // 窗口大小变化
        window.onresize = () => {
            this.cardList.forEach((ele,index) => {
                const name = ele.uuid;
                this.$refs[name][0].resize();
            });
        };
        this.getComList();
        const that = this;
        // token失效清空轮询定时器
        window.webSocketService.addEvent('devops_getofflineMsg',webSocketService.type.OFFLINE_MESSAGE,msg=>{
            that.cardList.forEach((ele,index) => {
                const name = ele.uuid;
                that.$refs[name][0].clearRefreshTimer();
            });
        });
    },
    methods:{
        // 点击其他区域关闭更多弹窗
        screenClick(e){
            if(e.target.className === 'el-icon-arrow-up' || e.target.className === 'el-icon-arrow-down' ||
                e.target.className === 'el-input__inner'){
                return;
            }
            this.cardList.forEach((ele,index) => {
                const name = ele.uuid;
                this.$refs[name][0].closeShowMore();
            });
        },
        cloneData(origin) {
			const data = JSON.parse(JSON.stringify(origin));
			return data;
		},
        onEnd(evt){
            const list = [];
            this.cardList.forEach((item,index) => {
                item.widgetSequence = index+1;
                list.push({
                    widgetId:item.widgetId,
                    widgetSequence:index+1,
                    widgetParams:item.widgetParams,
                });
            });
            this.updateComList(2,list);
        },
        addCom(item){
            const widgetParams = widgetConfig(item.comp,this).widgetParams;
            const uuid = getUuid();
            const params = {
                widgetId:item.comp+'_'+uuid,
                widgetSequence:this.cardList.length +1,
                widgetParams:widgetParams,
            };
            if(this.replaceIndex !== null){
                const widgetSequence = this.cardList[this.replaceIndex].widgetSequence;
                const list ={
                    ...item,
                    uuid:uuid,
                    ...params,
                    widgetSequence:widgetSequence,
                };
                this.updateComList(1,[{...list}],() => {
                    this.cardList.splice(this.replaceIndex,1,{...list});
                    this.replaceIndex = null;
                });
            }else{
                this.addComlist([{
                    ...params
                }],() => {
                    this.cardList.push(
                        {
                            ...item,
                            uuid:uuid,
                            ...params
                        }
                    );
                });
            }
        },
        closeCard(widgetId){
            this.delComlist([{
                widgetId:widgetId,
            }],() => {
                const uuid = widgetId.split('_')[1];
                const index = this.cardList.findIndex((item) => item.uuid === uuid);
                // 添加关闭动画
                this.$refs[uuid][0].$el.classList.add('animated');
                this.$refs[uuid][0].$el.classList.add('zoomOut');
                this.closeCardTimer && clearTimeout(this.closeCardTimer);
                const that = this;
                this.closeCardTimer = setTimeout(() => {
                    that.$refs[uuid][0].$el.classList.remove('zoomOut');
                    that.cardList.splice(index,1);
                },500);
            });
        },
        replaceCard(uuid){
            this.dialogVisible = true;
            this.replaceIndex = this.cardList.findIndex((item) => item.uuid === uuid);
        },
        // 添加组件
        addComlist(list,callBack){
            this.$exloading1x();
            this.getPost('post','addList',{
                list:list,
                userName:this.userId
            },'添加组件',(data) => {
                this.$exloaded1x();
                callBack && callBack();
            });
        },
        // 删除组件
        delComlist(list,callBack){
            this.$exloading1x();
            this.getPost('post','delList',{
                list:list,
                userName:this.userId
            },'删除组件',(data) => {
                this.$exloaded1x();
                callBack && callBack();
            });
        },
        // 更新组件
        updateComList(type,list,callBack){
            if(callBack){
                this.$exloading1x();
            }
            let params = list;
            // 如果为对象，得替换cardList对应的参数
            if(Object.prototype.toString.call(list) === '[object Object]'){
                const index = this.cardList.findIndex((item) => item.uuid === list.uuid);
                this.cardList[index].widgetParams = list.widgetParams;
                params = [
                    {
                        widgetId:this.cardList[index].widgetId,
                        widgetSequence:this.cardList[index].widgetSequence,
                        widgetParams:list.widgetParams,
                    }
                ];
            }
            this.getPost('post','updateList',{
                type:type,
                list:params,
                userName:this.userId
            },'更新组件',(data) => {
                this.$exloaded1x();
                callBack && callBack();
            });
        },
        // 获取组件信息
        getComList(){
            this.getPost('post','getconfigList',{
                userName:this.userId
            },'查询组件列表',(data) => {
                console.warn('data',data);
                this.cardList = data.map((item) => {
                    const split = item.widgetId.split('_');
                    return{
                        name:widgetNameList[split[0]],
                        comp:split[0],
                        uuid:split[1],
                        widgetId:item.widgetId,
                        widgetSequence:item.widgetSequence,
                        widgetParams:item.widgetParams,
                    };
                });
            });
        },
    }

};
</script>

<style lang="less" scoped>
@import '../../../style/animate.min.css';
.big-screen{
    width:100%;
    height:100%;
    padding-top:.89rem;
    overflow-y: auto;
    background: #292929;
    .draggable{
        width:100%;
        display: flex;
        padding-left: .89rem;
        flex-wrap: wrap;
    }
    .add-com{
        width: calc(25% - .89rem);
        height: 25.11rem;
        background: #555555;
        border-radius: .67rem .67rem .67rem .67rem;
        position: relative;  
        min-width: 22.22rem;
        cursor:pointer;
        .plus{
            position: absolute;
            top:calc(50% - 1.11rem);
            left:calc(50% - 1.11rem);
            font-size: 2.22rem;
            color:#7B7B7B;
        }
    }
    .box{
        margin-right: .89rem;
        margin-bottom: .89rem;
    }
}
</style>
<style lang="less">
html{
    font-size:calc(100vw * 18 / 1920);
}
</style>