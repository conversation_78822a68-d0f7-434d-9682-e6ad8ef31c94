<template>
    <div class="parameter-quality">
        <com-titled :title="name" :searchParams.sync="searchParams" @iconClick="isShowMore = !isShowMore" :selectList="selectList" 
        :widgetParams="widgetParams"
        @closeShowMore="closeShowMore"
        ></com-titled>
        <div class="charts">
           <template v-for="(list,listIndex) in configure">
                <component
                    class="charts-item"
                    :ref="uuid+listIndex"
                    :key="listIndex"
                    :is="list.comp"
                    :name="uuid+listIndex"
                    :data="list.data"
                    :type="list.type"
                />
            </template>
            <el-tooltip class="item" effect="dark"  placement="right">
                <div slot="content"><span class="hight-text">一经接口工参</span><br/>
                    <span class="normal-text">（06054\06055\06069）</span><br/>
                    <span class="normal-text">覆盖综资现网工参的比率</span><br/>
                </div>
                <i class="el-icon-warning-outline tips"></i>
            </el-tooltip>
            <el-tooltip class="item" effect="dark"  placement="right">
                <div slot="content"><span class="hight-text">小区标识字段存在重复的记录数</span><br/>
                    <span class="normal-text">2G：Province+LAC+CI</span><br/>
                    <span class="normal-text">4G：Province+ECI</span><br/>
                    <span class="normal-text">5G：Province+NCI</span><br/>
                </div>
                <i class="el-icon-warning-outline tips2"></i>
            </el-tooltip>
            <el-tooltip class="item" effect="dark"  placement="right">
                <div slot="content">
                    <span class="normal-text">通过行政区图层，检查小区经纬度</span><br/>
                    <span class="normal-text">打点所在区县与工参区县一致</span><br/>
                    <span class="normal-text">的小区占比（排除白名单）</span><br/>
                </div>
                <i class="el-icon-warning-outline tips3"></i>
            </el-tooltip>
        </div>
        <more-tips-bar
            :isShow.sync="isShowMore"
            :widgetParams="widgetParams"
            :refresh.sync ="refresh"
            @close="closeCard(widgetId)"
            @replace="replaceCard(uuid)"
            @refresh="init"
        ></more-tips-bar>
    </div>
</template>

<script>
import comTitled from './comTitled.vue';
import lineEchart from '@/script/components/charts/lineEchart.vue';
import moreTipsBar from './moreTipsBar.vue';
import screenCommon from '../mixins/commonMixin.js';
export default {
    name:'parameterQuality',
    components:{
        comTitled,
        lineEchart,
        moreTipsBar
    },
    inject: ['closeCard','replaceCard','updateComList'],
    props:{
        name:{
            type:String,
            default:'工参质量',
        },
        uuid:{
            type:String,
            default:'67bfd6-e75d-4bd1-8a9f-1a1922fb5c16a'
        },
        widgetParams:{
            type:Object,
            default:() => ({}),
        },
        widgetId:{
            type:String,
            default:'',
        }
    },
    mixins:[screenCommon],
    data(){
        return{
            configure:[
                {
                    type:'lineRightLegend',
                    comp:'lineEchart',
                    data:{
                        title:'小区数',
                        xAxisData:[],
                        yAxis:['记录数(亿)'],
                        unit:['亿','亿'],
                        isNull:true,
                        granularity:['',''],
                        minValue:['',''],
                        minInterval:['',''],
                        NotShowGranularity:true,//图例不显示时间粒度
                        lineData:[
                            {
                                name:'一经工参',
                                data:[],
                                color:'#40C6FF',
                                areaStyle:{
                                    color:{
                                        type: 'linear',
                                        x: 0,
                                        y: 0,
                                        x2: 0,
                                        y2: 1,
                                        colorStops: [{
                                            offset: 0, color: 'rgba(64,198,255,0.4)' // 0% 处的颜色
                                        }, {
                                            offset: 1, color: 'rgba(64,198,255,0.1)' // 100% 处的颜色
                                        }],
                                        global: false // 缺省为 false
                                    }
                                }
                            },
                            {
                                name:'融合工参',
                                data:[],
                                color:'#FF84A8',
                                areaStyle:{
                                    color:{
                                        type: 'linear',
                                        x: 0,
                                        y: 0,
                                        x2: 0,
                                        y2: 1,
                                        colorStops: [{
                                            offset: 0, color: 'rgba(255,132,168, 0.4)' // 0% 处的颜色
                                        }, {
                                            offset: 1, color: 'rgba(255,132,168, 0.1)' // 100% 处的颜色
                                        }],
                                        global: false // 缺省为 false
                                    }
                                }
                            },
                        ]
                    }
                },
                {
                    type:'singleLineArea',
                    comp:'lineEchart',
                    data:{
                        title:'一经工参完整性',
                        xAxisData:[],
                        yAxis:['异常比率(%)'],
                        unit:['%'],
                        isNull:true,
                        granularity:[''],
                        minValue:[''],
                        minInterval:[''],
                        NotShowGranularity:true,
                        lineData:[
                            {
                                name:'一经工参完整性',
                                data:[],
                                color:'#CCCC4B',
                                areaStyle:{
                                    color:{
                                        type: 'linear',
                                        x: 0,
                                        y: 0,
                                        x2: 0,
                                        y2: 1,
                                        colorStops: [{
                                            offset: 0, color: 'RGBA(204,204,75, 0.4)' // 0% 处的颜色
                                        }, {
                                            offset: 1, color: 'RGBA(204,204,75, 0.1)' // 100% 处的颜色
                                        }],
                                        global: false // 缺省为 false
                                    }
                                },
                            },
                        ]
                    }
                },
                {
                    type:'lineRightLegend',
                    comp:'lineEchart',
                    data:{
                        title:'小区标识重复数',
                        xAxisData:[],
                        yAxis:['单位（亿行）'],
                        unit:['十亿','分'],
                        isNull:true,
                        granularity:['',''],
                        minValue:['',''],
                        minInterval:['',''],
                        NotShowGranularity:true,
                        lineData:[
                            {
                                name:'一经工参',
                                data:[],
                                color:'#40C6FF',
                            },
                            {
                                name:'融合工参',
                                data:[],
                                color:'#FF84A8',
                            }
                        ]
                    }
                },
                {
                    type:'lineRightLegend',
                    comp:'lineEchart',
                    data:{
                        title:'经纬度区县匹配率',
                        xAxisData:[],
                        yAxis:['单位（亿行）'],
                        unit:['十亿','分'],
                        isNull:true,
                        granularity:['',''],
                        minValue:['',''],
                        minInterval:['',''],
                        NotShowGranularity:true,
                        lineData:[
                            {
                                name:'一经工参',
                                data:[],
                                color:'#40C6FF',
                            },
                            {
                                name:'融合工参',
                                data:[],
                                color:'#FF84A8',
                            }
                        ]
                    }
                },
            ],
            isShowMore:false,
            tips1:'',
            searchParams:{},
            selectList:{
                isShowtimePick:true,
                isShowNetType:true,
            },
            ofRefreshTimer:null,
        };
    },
    computed:{
        postParams(){
            let startDate = new Date();
            let endDate = new Date().format('yyyy-MM-dd HH:mm:00');
            if(this.searchParams.relativeTime){
                startDate = new Date(startDate.setTime(startDate.getTime() - 60 * 1000 * this.searchParams.relativeTime)).format('yyyy-MM-dd HH:mm:00');
            }else{
                startDate = this.searchParams.startTime;
                endDate = this.searchParams.endTime;
            }
            return {
                startTime:startDate || this.defaultStartTime,
                endTime:endDate || this.defaultNewDate,
                province:this.searchParams.province || 'QG',
                netType:this.searchParams.netType,
            };
        }
    },
    watch:{
        searchParams:{
            handler(newV,oldV){
                this.init();
                if(JSON.stringify(oldV) !=='{}'){
                    const params = Object.assign({},this.widgetParams,newV);
                    this.updateComList(2,{
                        uuid:this.uuid,
                        widgetParams:{
                            ...params,
                        }
                    });
                }
            },
            deep:true,
        },
        refresh:{
            handler(newV,oldV){
                this.setIntervalTimer();
                if(JSON.stringify(oldV) !=='{}'){
                    const params = Object.assign({},this.widgetParams,{refresh:newV});
                    this.updateComList(2,{
                        uuid:this.uuid,
                        widgetParams:{
                            ...params,
                        }
                    });
                }
            }
        }
    },
    beforeDestroy() {
        this.clearRefreshTimer();
    },
    methods:{
        resize(){
            this.configure.forEach((ele,index) => {
                const name = this.uuid+index;
                this.$refs[name][0].resize();
            });
        },
        init(){
            this.getCellCount();
            this.getOnceWorkParamIntegrality();
            this.getCellLogoRepeatNum();
            this.getCountyMatchingRate();
        },
        getCellCount(){
            // this.getPost('getCellCount',this.postParams,'工参质量_小区数',({data}) => {
            //     this.handlerLineData(data,0);
            // });
        },
        getOnceWorkParamIntegrality(){
            // this.getPost('getOnceWorkParamIntegrality',this.postParams,'工参质量_一经工参完整性',({data}) => {
            //     this.handlerLineData(data,1);
            // });
        },
        getCellLogoRepeatNum(){
            // this.getPost('getCellLogoRepeatNum',this.postParams,'工参质量_小区标识重复数',({data}) => {
            //     this.handlerLineData(data,2);
            // });
        },
        getCountyMatchingRate(){
            // this.getPost('getCountyMatchingRate',this.postParams,'工参质量_经纬度区县匹配率',({data}) => {
            //     this.handlerLineData(data,3);
            // });
        },
        setIntervalTimer(){
            this.clearRefreshTimer();
            const that = this;
            this.ofRefreshTimer = setInterval(() => {
                that.init();
            },that.refresh * 60000);
        },
        clearRefreshTimer(){
            this.ofRefreshTimer && clearInterval(this.ofRefreshTimer);
        },
    }
};
</script>

<style lang="less" scoped>
.parameter-quality{
    width:calc(50% - .89rem);
    height: 25.11rem;
    background: linear-gradient(116deg, #6B785F 0%, #35452A 100%);
    border-radius: .67rem .67rem .67rem .67rem;
    position: relative;
}
.charts{
    width: 100%;
    height:calc(100% - 3.11rem);
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    position: relative;
    .charts-item{
        width:50%;
        height:50%;
    }
    .tips{
        position: absolute;
        top: .28rem;
        left: calc(50% + 7rem);
        color:RGBA(255, 255, 255, 0.8);
        font-size: .78rem;
    }
    .tips2{
        position: absolute;
        bottom: calc(50% - 1rem);
        left: 7.11rem;
        color:RGBA(255, 255, 255, 0.8);
        font-size: .78rem;
    }
    .tips3{
        position: absolute;
        bottom: calc(50% - 1rem);
        left: calc(50% + 7.78rem);
        color:RGBA(255, 255, 255, 0.8);
        font-size: .78rem;
    }
}
</style>