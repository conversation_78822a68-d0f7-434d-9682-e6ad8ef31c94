import { request } from '@/script/utils/request.js';
import { goldApprovalApis } from '@/script/api/module/regionResource.js';
import GoldApproval from '@/script/components/goldApproval/index.vue';

export default {
    components: {
        GoldApproval
    },
    computed: {
        userName() {
            return frameService.getUser().describe; //中文名
        },
        userEnName() {
            return frameService.getUser().name; //英文名
        },
        userId() {
            return frameService.getUser().id;
        },
        phone() {
            return frameService.getUser().phone;
        }
    },
    methods: {
        /**
         * 接口请求封装
         * @param method 请求方式
         * @param postName 接口名
         * @param _param 入参
         * @param errContent 错误提示内容
         * @param callBack 成功逻辑业务代码callBack
         * @param catchCallback catch时回调
         */
        async getPost(method, postName, _param, errContent, callBack, catchCallback) {
            await request(method, postName, _param)
                .then((rcvData) => {
                    this.$exloaded1x();
                    //此处成功逻辑业务代码
                    if (typeof rcvData === 'string') {
                        this.$message(rcvData);
                        return;
                    }
                    callBack && callBack(rcvData);
                })
                .catch((err) => {
                    // 失败回调
                    if (catchCallback) {
                        catchCallback(err);
                        return;
                    }
                    this.$exloaded1x();
                    let options = {
                        title: '消息提示',
                        content: errContent + '接口请求失败！',
                        detail: `详细内容：${err.errorMessage || err}`
                    };
                    this.$popupMessageWindow(options);
                });
        },
        /**
         * 动态挂载 GoldApproval 弹窗
         * @param postName 接口名
         * @param callBack 成功回调(可以)
         */
        showGoldApproval(postName, callBack) {
            // 使用 Vue.extend 创建组件构造函数
            const GoldApprovalConstructor = Vue.extend(GoldApproval);

            const instance = new GoldApprovalConstructor({
                propsData: {
                    title: '金库审批',
                    ids: goldApprovalApis[postName]
                }
            });

            instance.$mount();
            document.body.appendChild(instance.$el);

            instance.$on('close', () => {
                instance.$destroy();
                document.body.removeChild(instance.$el);
            });
            instance.$on('success', (params) => {
                sessionStorage.setItem(`sessionId_${postName}`, params.sessionId);
                callBack && callBack(params);
            });
        },
        /**
         * 接口请求封装(金库审批需要传请求头)
         * @param method 请求方式
         * @param postName 接口名
         * @param _param 入参
         * @param errContent 错误提示内容
         * @param callBack 成功逻辑业务代码callBack
         * @param catchCallback catch时回调
         */
        async getPostGoldApproval(method, postName, _param, errContent, callBack, catchCallback) {
            const sessionId = sessionStorage.getItem(`sessionId_${postName}`);
            const headers = {
                sessionId: sessionId || undefined
            };
            await request(method, postName, _param, undefined, headers)
                .then((rcvData) => {
                    this.$exloaded1x();
                    //此处成功逻辑业务代码
                    if (typeof rcvData === 'string') {
                        this.$message(rcvData);
                        return;
                    }
                    callBack && callBack(rcvData);
                })
                .catch((err) => {
                    // 失败回调
                    if (catchCallback) {
                        catchCallback(err);
                        return;
                    }
                    // 金库审批特别处理
                    if (typeof err === 'object' && err.returnCode === '400000') {
                        this.$exloaded1x();
                        // 弹出金库审批弹窗
                        this.showGoldApproval(postName);
                        return;
                    }
                    this.$exloaded1x();
                    let options = {
                        title: '消息提示',
                        content: errContent + '接口请求失败！',
                        detail: `详细内容：${err.errorMessage || err}`
                    };
                    this.$popupMessageWindow(options);
                });
        }
    }
};
