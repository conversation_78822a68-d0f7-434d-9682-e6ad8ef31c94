<template>
    <div class="date-picker-wrapper">
        <el-date-picker
            v-if="pickerType === 'dateRangePicker'"
            ref="picker"
            popper-class="date-picker-dark popover-dark"
            v-model="dateValue"
            :type="type"
            :start-placeholder="startPlaceholder"
            :end-placeholder="endPlaceholder"
            :default-time="currentDefaultTime"
            align="right"
            size="small"
            :format="format"
            :value-format="format"
            :clearable="clearable"
            :picker-options="currentOptions"
            @change="handleDateChange"
        >
        </el-date-picker>
        <div class="oneday-time-picker" v-else-if="pickerType === 'oneday-time'">
            <el-date-picker
                popper-class="date-picker-dark popover-dark"
                v-model="day"
                type="date"
                placeholder="选择日期"
                value-format="yyyy-MM-dd"
                :clearable="false"
                @change="handleDayChange"
            >
            </el-date-picker>
            <div class="time-selectors">
                <el-time-select
                    class="time-selector"
                    popper-class="date-picker-dark popover-dark"
                    placeholder="开始时间"
                    v-model="startTime"
                    :picker-options="{
                        start: '00:00',
                        step: '01:00',
                        end: '23:00'
                    }"
                    :clearable="false"
                    @change="handleTimeChange"
                >
                </el-time-select>
                <el-select
                    class="time-selector"
                    popper-class="select-dropdown-dark popover-dark time-selector-dropdown"
                    placeholder="结束时间"
                    v-model="endTime"
                    size="small"
                    :clearable="false"
                    @change="handleTimeChange"
                >
                    <template slot="prefix"><i class="el-input__icon el-icon-time"></i></template>
                    <el-option
                        v-for="(item, index) in endTimeOptions"
                        :key="index"
                        :label="item"
                        :value="item"
                        :disabled="startTime && item < startTime"
                    ></el-option>
                </el-select>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'DatePicker',
    props: {
        // 双向绑定的日期值
        value: {
            type: Array,
            default: () => []
        },
        // [dateRangePicker, oneday-time]
        pickerType: {
            type: String,
            default: 'dateRangePicker'
        },
        // dateRangePicker时生效
        type: {
            type: String,
            default: 'datetimerange'
        },
        // dateRangePicker时生效
        format: {
            type: String,
            default: 'yyyy-MM-dd HH:mm:ss'
        },
        startPlaceholder: {
            type: String,
            default: '开始时间'
        },
        endPlaceholder: {
            type: String,
            default: '结束时间'
        },
        defaultTime: {
            type: Array,
            default: () => ['00:00:00', '23:59:59']
        },
        clearable: {
            type: Boolean,
            default: false
        },
        // 是否自动初始化时间
        isInitTime: {
            type: Boolean,
            default: true
        },
        // 初始化时间方式：recent表示最近几天，month表示当月， today表示今天
        initTimeType: {
            type: String,
            default: 'today'
        },
        // 如果是recent模式，表示最近几天
        recentDays: {
            type: Number,
            default: 30
        },
        // 是否展示侧边快捷选项
        showOptionsShortcuts: {
            type: Boolean,
            default: true
        },
        // 限制最大可选时间范围跨度(天)，0不限制
        maxTimeSpan: {
            type: Number,
            default: 0
        }
    },
    computed: {
        currentDefaultTime() {
            if (this.maxTimeSpan > 0) {
                return ['00:00:00', '00:00:00'];
            }
            return this.defaultTime;
        },
        currentOptions() {
            const result = { ...this.pickerOptions };
            if (!this.showOptionsShortcuts) {
                delete result.shortcuts;
            }
            if (this.maxTimeSpan <= 0) {
                delete result.onPick;
                delete result.disabledDate;
            }
            return result;
        }
    },
    data() {
        return {
            dateValue: this.value,
            day: '',
            startTime: '',
            endTime: '',
            selectedFirstDate: null, // 存储用户选择的第一个日期
            endTimeOptions: this.generateTimeOptions(),
            pickerOptions: {
                shortcuts: [
                    {
                        text: '最近5分钟',
                        onClick: (picker) => {
                            this.datePicker(picker, 5);
                        }
                    },
                    {
                        text: '最近15分钟',
                        onClick: (picker) => {
                            this.datePicker(picker, 15);
                        }
                    },
                    {
                        text: '最近30分钟',
                        onClick: (picker) => {
                            this.datePicker(picker, 30);
                        }
                    },
                    {
                        text: '最近1小时',
                        onClick: (picker) => {
                            this.datePicker(picker, 60);
                        }
                    },
                    {
                        text: '最近3小时',
                        onClick: (picker) => {
                            this.datePicker(picker, 180);
                        }
                    },
                    {
                        text: '最近6小时',
                        onClick: (picker) => {
                            this.datePicker(picker, 360);
                        }
                    },
                    {
                        text: '最近12小时',
                        onClick: (picker) => {
                            this.datePicker(picker, 720);
                        }
                    },
                    {
                        text: '最近24小时',
                        onClick: (picker) => {
                            this.datePicker(picker, 1440);
                        }
                    },
                    {
                        text: '最近7天',
                        onClick: (picker) => {
                            this.datePicker(picker, 10080);
                        }
                    }
                ],
                // 当用户点击日期选择面板时触发
                onPick: ({ maxDate, minDate }) => {
                    // 如果只选择了一个日期（第一个日期），记录下来用于禁用逻辑
                    if (minDate && !maxDate) {
                        this.selectedFirstDate = new Date(minDate);
                    }
                },
                // 禁用日期的判断函数
                disabledDate: (time) => {
                    // 如果未设置最大时间跨度限制，或者还没选择第一个日期，则不禁用
                    if (this.maxTimeSpan <= 0 || !this.selectedFirstDate) {
                        return false;
                    }

                    // 计算当前日期与已选择的第一个日期之间的时间差（毫秒）
                    const firstTime = this.selectedFirstDate.getTime();
                    const currentTime = time.getTime();
                    const diffTime = Math.abs(currentTime - firstTime);

                    // 转换为天数
                    const diffDays = diffTime / (24 * 60 * 60 * 1000);

                    // 如果日期差超过最大跨度，则禁用
                    return diffDays > this.maxTimeSpan;
                }
            }
        };
    },
    watch: {
        value: {
            handler(newVal) {
                this.dateValue = newVal;

                // 当外部value变化时，同步更新day和time
                if (
                    this.pickerType === 'oneday-time' &&
                    Array.isArray(newVal) &&
                    newVal.length === 2
                ) {
                    const startDate = newVal[0].split(' ')[0];
                    const startTime = newVal[0].split(' ')[1];
                    const endTime = newVal[1].split(' ')[1];

                    this.day = startDate;

                    // 更新时间选择器的值
                    if (startTime) {
                        this.startTime = startTime.substring(0, 5); // 只取HH:mm部分
                    }

                    if (endTime) {
                        this.endTime = endTime.substring(0, 5); // 只取HH:mm部分
                    } else if (this.startTime) {
                        // 如果结束时间为空但开始时间存在，自动设置合适的结束时间
                        const nextTimeOption = this.endTimeOptions.find(
                            (time) => time > this.startTime
                        );
                        this.endTime = nextTimeOption || '23:59';
                    }
                }
            },
            deep: true
        }
    },
    mounted() {
        // 如果需要初始化且没有默认值，自动设置时间范围
        if (this.isInitTime && !this.dateValue.length) {
            this.initDateTime();
        }
    },
    methods: {
        // 处理快捷选择，计算时间范围
        datePicker(picker, num) {
            const startDate = new Date();
            const endDate = new Date();
            startDate.setTime(startDate.getTime() - 60 * 1000 * num);
            picker.$emit('pick', [startDate, endDate]);
        },
        // 处理日期变更，触发事件
        handleDateChange(val) {
            this.$emit('input', val);
            this.$emit('change', val);
        },
        // 处理单日期选择器日期变化
        handleDayChange(day) {
            if (!day) return;

            // 如果时间为空，设置默认值
            if (!this.startTime) {
                this.startTime = '00:00';
            }
            if (!this.endTime) {
                this.endTime = '23:59';
            }

            this.updateTimeValue();
        },

        // 处理时间选择器变化
        handleTimeChange() {
            if (!this.day) return;

            // 当开始时间变更且结束时间为空或小于开始时间时，自动选择合适的结束时间
            if (this.startTime && (!this.endTime || this.endTime < this.startTime)) {
                // 在endTimeOptions中找到大于等于startTime的最小值
                const nextTimeOption = this.endTimeOptions.find((time) => time > this.startTime);
                // 如果找到下一个时间选项，则设置为结束时间；否则设置为23:59
                this.endTime = nextTimeOption || '23:59';
            }

            this.updateTimeValue();
        },

        // 更新时间值
        updateTimeValue() {
            if (!this.day || !this.startTime || !this.endTime) return;

            // 添加秒数
            let startSeconds = ':00';
            let endSeconds = this.endTime === '23:59' ? ':59' : ':00';

            const startDateTime = `${this.day} ${this.startTime}${startSeconds}`;
            const endDateTime = `${this.day} ${this.endTime}${endSeconds}`;

            this.dateValue = [startDateTime, endDateTime];
            this.handleDateChange(this.dateValue);
        },

        // 初始化日期范围
        initDateTime() {
            let endDate = new Date();
            // 设置时分秒为23:59:59
            endDate.setHours(23, 59, 59, 0);

            let startDate;

            // 根据初始化类型设置起始日期
            if (this.initTimeType === 'recent') {
                startDate = new Date(endDate.getTime() - this.recentDays * 24 * 3600 * 1000);
            } else if (this.initTimeType === 'month') {
                startDate = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
            } else {
                startDate = new Date();
            }
            startDate.setHours(0, 0, 0, 0);

            this.dateValue = [
                startDate.format('yyyy-MM-dd HH:mm:ss'),
                endDate.format('yyyy-MM-dd HH:mm:ss')
            ];

            this.handleDateChange(this.dateValue);

            // 如果是oneday-time类型，还需要初始化day和time
            if (this.pickerType === 'oneday-time') {
                this.day = startDate.format('yyyy-MM-dd');
                this.startTime = startDate.format('HH:mm');
                this.endTime = '23:59';
            }
        },
        generateTimeOptions() {
            const options = [];
            for (let i = 0; i <= 23; i++) {
                options.push(i.toString().padStart(2, '0') + ':00');
            }
            // 添加23:59
            options.push('23:59');
            return options;
        }
    }
};
</script>

<style lang="less" scoped>
.oneday-time-picker {
    display: grid;
    grid-template-columns: 1fr 1.5fr;
    gap: 5px;
}
.time-selectors {
    display: flex;
    flex: 1;
    width: 0;
    gap: 5px;
}
</style>
<style lang="less">
.time-selector {
    flex: 1;
    min-width: 100px;
    &.el-input--suffix .el-input__inner {
        padding-right: 15px !important;
    }
    .el-input--suffix .el-input__inner {
        padding-right: 15px !important;
    }
    .el-select__caret {
        display: none !important;
    }
    .el-icon-circle-close {
        display: block !important;
    }
}
.time-selector-dropdown {
    .el-select-dropdown__wrap {
        height: 200px !important;
    }
}
</style>
