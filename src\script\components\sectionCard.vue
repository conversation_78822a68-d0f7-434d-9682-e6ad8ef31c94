<template>
    <div class="section-card">
        <div class="section-card-title">
            <span>{{ title }}</span>
        </div>
        <div class="section-card-content">
            <slot></slot>
        </div>
    </div>
</template>
<script>
export default {
    name: 'sectionCard',
    props: {
        title: {
            type: String,
            default: '标题'
        }
    },
    data() {
        return {};
    },
    methods: {}
};
</script>
<style lang="less">
.section-card {
    width: 100%;
    .section-card-title {
        width: 100%;
        height: 2.78rem;
        background-image: url('../../img/homePage/section-title-bg.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        span {
            height: 100%;
            padding-left: 3.33rem;
            display: flex;
            align-items: center;
            font-family: YouSheBiaoTiHei;
            font-size: 2.22rem;
            color: #d1e4ff;
            line-height: 2.22rem;
            letter-spacing: 0.17rem;
        }
    }
    .section-card-content {
        margin-top: 0.56rem;
        width: 100%;
        height: calc(100% - 2.5rem);
        background: linear-gradient(to bottom, transparent 0%, rgb(0, 50, 102) 100%);
        border-radius: 0.56rem 0.56rem 1.67rem 1.67rem;
    }
}
</style>
