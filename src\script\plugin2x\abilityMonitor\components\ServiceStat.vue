<template>
    <cornerCard class="content-bg content-first">
        <section-title-one title="服务统计">
            <el-form class="search-bar-first">
                <el-form-item label="时间粒度：" label-width="100px">
                    <el-select
                        v-model="localForm.granularity"
                        size="small"
                        popper-class="select-dropdown-dark popover-dark"
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="item in granularityOpt"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="时间范围：" label-width="100px">
                    <date-picker
                        v-model="localForm.time"
                        :is-init-time="false"
                        :showOptionsShortcuts="false"
                        type="daterange"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                    ></date-picker>
                </el-form-item>
                <el-form-item label-width="16px">
                    <el-button type="primary" size="small" @click="search">查询</el-button>
                    <el-button type="primary" class="reset-btn" size="small" @click="reset"
                        >重置</el-button
                    >
                </el-form-item>
            </el-form>
        </section-title-one>
        <div class="content-first-wrapper">
            <div class="content-first-full">
                <section-title-two title="热门服务调用统计"></section-title-two>
                <div class="content-first-full-wrapper">
                    <div class="table-box">
                        <div class="table-box-title">热门API排行</div>
                        <dark-table
                            class="table-box-content"
                            :columns="tableColumns"
                            :tableData="tableData"
                            :defaultSelectedRow="selectedRow"
                            @row-click="handleRowClick"
                        ></dark-table>
                    </div>
                    <div class="chart-box">
                        <div class="chart-box-title">API调用量趋势</div>
                        <common-echart
                            class="chart-box-content"
                            :targetData="hasDetailData ? chartData : {}"
                            refName="api-usage-trend"
                        >
                            <template #top>
                                <div class="chart-box-top">
                                    <div class="chart-box-top-item">
                                        <div class="chart-box-top-item-title">最低响应时长</div>
                                        <div class="chart-box-top-item-value">
                                            {{ minResponseTime }}
                                        </div>
                                    </div>
                                    <div class="chart-box-top-item">
                                        <div class="chart-box-top-item-title">响应成功率均值</div>
                                        <div class="chart-box-top-item-value">
                                            {{ avgSuccessRate }}
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </common-echart>
                    </div>
                </div>
            </div>
        </div>
    </cornerCard>
</template>
<script>
import commonComponentsMixin from './comMixin.js';
import darkTable from '@/script/components/darkTable.vue';
export default {
    name: 'ServiceStat',
    components: {
        darkTable
    },
    mixins: [commonComponentsMixin],
    props: {
        form: Object,
        granularityOpt: Array
    },
    data() {
        return {
            initialForm: {}, // 保存初始数据
            localForm: {
                granularity: '',
                time: []
            },
            minResponseTime: '- ms',
            avgSuccessRate: '- %',
            chartData: {
                xAxisData: [],
                yAxisName: '单位:次',
                lengendShow: true,
                isGroup: false,
                lineData: [
                    {
                        name: '今日',
                        type: 'line',
                        data: [],
                        lineType: 'solid',
                        color: '#01E6FE',
                        unit: ''
                    },
                    {
                        name: '昨日',
                        type: 'line',
                        data: [],
                        lineType: 'dashed',
                        color: '#FFDF12',
                        unit: ''
                    }
                ]
            },
            tableColumns: [
                {
                    label: '功能名称',
                    prop: 'name'
                },
                {
                    label: '平均调用量(次)',
                    prop: 'averageUsage',
                    width: 125
                }
            ],
            tableData: [],
            selectedRow: null,
            currentApiName: '', // 当前选中的API名称
            hasDetailData: false // 是否有详情数据
        };
    },
    watch: {
        form: {
            handler(newVal) {
                // 当外部form变化时，更新本地表单
                this.localForm = JSON.parse(JSON.stringify(newVal));
            },
            immediate: true,
            deep: true
        }
    },
    mounted() {
        // 保存初始数据副本
        this.initialForm = JSON.parse(JSON.stringify(this.form));

        // 初始化时，如果有时间和粒度，则请求一次接口
        if (this.localForm.time && this.localForm.time.length === 2 && this.localForm.granularity) {
            this.search();
        }
    },
    methods: {
        // 查询按钮点击事件
        search() {
            if (!this.localForm.time || this.localForm.time.length !== 2) {
                this.$message.warning('请选择时间范围');
                return;
            }
            if (!this.localForm.granularity) {
                this.$message.warning('请选择时间粒度');
                return;
            }

            this.getApiCallRank();
        },

        // 重置按钮点击事件
        reset() {
            // 使用初始数据重置表单
            this.localForm = JSON.parse(JSON.stringify(this.initialForm));

            // 重置后执行一次查询
            if (
                this.localForm.time &&
                this.localForm.time.length === 2 &&
                this.localForm.granularity
            ) {
                this.search();
            }
        },

        // 获取功能板块使用统计数据
        getApiCallRank() {
            const params = {
                startTime: this.formatDate(this.localForm.time[0], true),
                endTime: this.formatDate(this.localForm.time[1], false),
                timeType: this.localForm.granularity,
                topN: 15,
                name: ''
            };

            this.$exloading1x();
            this.getPost('post', 'apiCallRank', params, '系统成效-热门API排行', (res) => {
                if (res && Array.isArray(res) && res.length > 0) {
                    // 处理表格数据
                    this.tableData = res.map((item) => ({
                        name: item.name,
                        averageUsage: item.avgCnt
                    }));

                    // 如果有数据，默认选中第一条查询详情
                    if (this.tableData.length > 0) {
                        this.selectedRow = 0;
                        this.handleRowClick(this.tableData[0]);
                    }
                } else {
                    // 没有数据时清空表格
                    this.tableData = [];
                    this.hasDetailData = false;
                }
                this.$exloaded1x();
            });
        },

        // 处理行点击事件，获取接口调用详情
        handleRowClick(row) {
            if (!this.localForm.time || this.localForm.time.length !== 2) {
                this.$message.warning('请选择时间范围');
                return;
            }

            this.currentApiName = row.name;

            const params = {
                startTime: this.formatDate(this.localForm.time[0], true),
                endTime: this.formatDate(this.localForm.time[1], false),
                timeType: this.localForm.granularity,
                name: row.name
            };

            this.$exloading1x();
            this.getPost('post', 'apiCallDetail', params, '系统成效-API调用量趋势', (res) => {
                // 默认设置为无数据
                this.hasDetailData = false;

                if (res && res.today && res.today.trend && Array.isArray(res.today.trend)) {
                    this.minResponseTime = res.today.minTime + 'ms';
                    this.avgSuccessRate = res.today.ratio + '%';

                    // 处理图表数据
                    const xAxisData = [];
                    const todayData = [];
                    const yesterdayData = [];

                    // 处理今日数据
                    res.today.trend.forEach((item) => {
                        xAxisData.push(item.time);
                        todayData.push(item.count);
                    });

                    // 处理昨日数据
                    if (
                        res.yesterday &&
                        res.yesterday.trend &&
                        Array.isArray(res.yesterday.trend)
                    ) {
                        res.yesterday.trend.forEach((item) => {
                            yesterdayData.push(item.count);
                        });
                    }

                    if (xAxisData.length > 0) {
                        this.chartData.xAxisData = xAxisData;
                        this.chartData.isGroup = false;
                        this.chartData.lineData = [
                            {
                                name: '今日',
                                type: 'line',
                                data: todayData,
                                lineType: 'solid',
                                color: '#01E6FE',
                                unit: '次'
                            },
                            {
                                name: '昨日',
                                type: 'line',
                                data: yesterdayData,
                                lineType: 'dashed',
                                color: '#01E6FE',
                                unit: '次'
                            }
                        ];
                        this.hasDetailData = true;
                    }
                }
                this.$exloaded1x();
            });
        }
    }
};
</script>

<style lang="less" scoped>
@import '../PanelCommon.less';
.content-first-wrapper {
    width: 100%;
    height: 560px;
    .content-first-full-wrapper {
        width: 100%;
        height: 0;
        flex: 1;
        display: flex;

        .table-box {
            width: 400px;
            height: 100%;
            display: flex;
            flex-direction: column;
            padding: 10px 10px 0 0;
            position: relative;
            &::after {
                content: '';
                position: absolute;
                top: 0;
                left: 100%;
                transform: translateX(100%);
                display: block;
                width: 1px;
                height: calc(100% + 16px);
                background-color: #0095ff;
            }
            &-title {
                font-size: 14px;
                font-weight: 500;
                font-family: 'PingFangSC, PingFang SC';
                color: #fff;
                margin-bottom: 10px;
            }
            &-content {
                width: 100%;
                height: 0;
                flex: 1;
            }
        }
        .chart-box {
            width: 0;
            height: 100%;
            display: flex;
            flex-direction: column;
            flex: 1;
            padding: 10px 10px 0 16px;
            &-title {
                font-size: 14px;
                font-weight: 500;
                font-family: 'PingFangSC, PingFang SC';
                color: #fff;
                margin-bottom: 10px;
            }
            &-content {
                width: 100%;
                height: 0;
                flex: 1;
            }
            &-top {
                pointer-events: none; /* 点击事件穿透 */
                position: absolute;
                top: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 100%;
                height: 36px;
                display: flex;
                gap: 24px;
                align-items: center;
                justify-content: center;
                &-item {
                    width: 20%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    background: linear-gradient(90deg, rgba(9, 62, 121, 0) 0%, #093e79 100%);
                    border-radius: 6px;
                    backdrop-filter: blur(14.4px);
                    &:nth-child(1)::before {
                        content: '';
                        display: block;
                        margin-right: 5px;
                        width: 36px;
                        height: 36px;
                        background-image: url('../../../../img/abilityMonitor/time-card.png');
                        background-size: 100% 100%;
                        background-repeat: no-repeat;
                    }
                    &:nth-child(2)::before {
                        content: '';
                        display: block;
                        margin-right: 5px;
                        width: 30px;
                        height: 30px;
                        background-image: url('../../../../img/abilityMonitor/rate-card.png');
                        background-size: 100% 100%;
                        background-repeat: no-repeat;
                    }
                    &-title {
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: 14px;
                        color: #c9dfff;
                        margin-right: 30px;
                    }
                    &-value {
                        font-family: DIN, DIN;
                        font-weight: 500;
                        font-size: 16px;
                        color: #01e6fe;
                    }
                }
            }
        }
    }
}
</style>
