<template>
    <div class="map-public-bg map-legend">
        <p>{{ title }}</p>
        <div class="legend-content">
            <div class="legend-content-range"></div>
            <div class="legend-content-text">
                <span v-for="(value, index) in values" :key="index">{{ value }}</span>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'heatMapLegend',
    props: {
        title: {
            type: String,
            default: '图例'
        },
        values: {
            type: Array,
            default: () => ['0', '100', '200', '300']
        }
    }
};
</script>

<style lang="less" scoped>
.map-legend {
    position: absolute;
    bottom: 13px;
    right: 13px;
    width: 86px;
    height: 178px;
    display: flex;
    flex-direction: column;
    padding: 16px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: #ffffff;
    line-height: 16px;
    .legend-content {
        width: 100%;
        height: 0;
        flex: 1;
        display: flex;
        gap: 10px;
        .legend-content-range {
            width: 12px;
            height: 100%;
            background: linear-gradient(180deg, #29ea2b 0%, #f8f825 33%, #ffa85d 68%, #ff0000 100%);
        }
        .legend-content-text {
            line-height: 12px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
    }
}
.map-public-bg {
    background: rgba(18, 61, 124, 0.9);
    border-radius: 2px;
    border: 1px solid rgba(0, 149, 255, 0.5);
}
</style>
