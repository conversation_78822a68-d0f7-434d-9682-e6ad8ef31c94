<template>
    <div class="off-line">
        <com-titled :title="name" :searchParams.sync="searchParams" @iconClick="isShowMore = !isShowMore" 
        :widgetParams="widgetParams"
        @closeShowMore="closeShowMore"
        ></com-titled>
        <div class="charts">
           <template v-for="(list,listIndex) in configure">
                <component
                    class="charts-item"
                    :ref="uuid+listIndex"
                    :key="listIndex"
                    :is="list.comp"
                    :name="uuid+listIndex"
                    :data="list.data"
                    :type="list.type"
                />
            </template>
        </div>
        <more-tips-bar
            :isShow.sync="isShowMore"
            :widgetParams="widgetParams"
            :refresh.sync ="refresh"
            @close="closeCard(widgetId)"
            @replace="replaceCard(uuid)"
            @refresh="init"
        ></more-tips-bar>
    </div>
</template>

<script>
import comTitled from './comTitled.vue';
import lineEchart from '@/script/components/charts/lineEchart.vue';
import moreTipsBar from './moreTipsBar.vue';
import screenCommon from '../mixins/commonMixin.js';
import pieRowEchart from '@/script/components/charts/pieRowEchart.vue';
export default {
    name:'zipperQuality',
    components:{
        comTitled,
        lineEchart,
        moreTipsBar,
        pieRowEchart,
    },
    props:{
        name:{
            type:String,
            default:'拉链质量',
        },
        uuid:{
            type:String,
            default:'67bfd6-e75d-4bd1-8a9f-1a1922fb5c16a'
        },
        widgetParams:{
            type:Object,
            default:() => ({}),
        },
        widgetId:{
            type:String,
            default:'',
        }
    },
    mixins:[screenCommon],
    data(){
        return{
            configure:[
                {
                    type:'lineCenterLengend',
                    comp:'lineEchart',
                    data:{
                        title:'实时栅格拉链',
                        xAxisData:[],
                        yAxis:['数据量(亿)','用户数(万)'],
                        unit:['亿','万'],
                        isNull:true,
                        granularity:['',''],
                        minValue:['',''],
                        minInterval:['',''],
                        NotShowGranularity:true,//图例不显示时间粒度
                        lineData:[
                            {
                                name:'数据量',
                                data:[],
                                color:'#61DCFD',
                                yAxisIndex:0,
                                areaStyle:{
                                    color:{
                                        type: 'linear',
                                        x: 0,
                                        y: 0,
                                        x2: 0,
                                        y2: 1,
                                        colorStops: [{
                                            offset: 0, color: 'RGBA(34, 179, 250, 0.8)' // 0% 处的颜色
                                        }, {
                                            offset: 1, color: 'RGBA(36, 75, 117, 0.3)' // 100% 处的颜色
                                        }],
                                        global: false // 缺省为 false
                                    }
                                }
                            },
                            {
                                name:'用户数',
                                data:[],
                                color:'#6BDCAF',
                                yAxisIndex:1,
                            },
                        ]
                    }
                },
                {
                    type:'pie',
                    comp:'pieRowEchart',
                    data:{
                        pieData: [
                            { value: 0, name: '<60 s' },
                            { value: 0, name: '60-120 s' },
                            { value: 0, name: '120-300 s' },
                            { value: 0, name: '300-1200 s' },
                            { value: 0, name: '>1200 s' },
                        ],
                        percent: {
                            '<60 s': '0.0%',
                            '60-120 s': '0.0%',
                            '120-300 s': '0.0%',
                            '300-1200 s': '0.0%',
                            '>1200 s':'0.0%'
                        },
                        title: '时延',
                    }
                },
            ],
            isShowMore:false,
            tips1:'',
            searchParams:{},
            ofRefreshTimer:null,
        };
    },
    computed:{
        postParams(){
            let startDate = new Date();
            let endDate = new Date().format('yyyy-MM-dd HH:mm:00');
            if(this.searchParams.relativeTime){
                startDate = new Date(startDate.setTime(startDate.getTime() - 60 * 1000 * this.searchParams.relativeTime)).format('yyyy-MM-dd HH:mm:00');
            }else{
                startDate = this.searchParams.startTime;
                endDate = this.searchParams.endTime;
            }
            return {
                startTime:startDate || this.defaultStartTime,
                endTime:endDate || this.defaultNewDate,
                dataType:4,
                granularity:this.searchParams.granularity,
            };
        }
    },
    inject: ['closeCard','replaceCard','updateComList'],
    watch:{
        searchParams:{
            handler(newV,oldV){
                this.init();
                if(JSON.stringify(oldV) !=='{}'){
                    const params = Object.assign({},this.widgetParams,newV);
                    this.updateComList(2,{
                        uuid:this.uuid,
                        widgetParams:{
                            ...params,
                        }
                    });
                }
            },
            deep:true,
        },
        refresh:{
            handler(newV,oldV){
                this.setIntervalTimer();
                if(JSON.stringify(oldV) !=='{}'){
                    const params = Object.assign({},this.widgetParams,{refresh:newV});
                    this.updateComList(2,{
                        uuid:this.uuid,
                        widgetParams:{
                            ...params,
                        }
                    });
                }
            }
        }
    },
    beforeDestroy() {
        this.clearRefreshTimer();
    },
    methods:{
        resize(){
            this.configure.forEach((ele,index) => {
                const name = this.uuid+index;
                this.$refs[name][0].resize();
            });
        },
        init(){
            this.getDelayChart();
            this.getTrendChart();
        },
        getDelayChart(){
            this.getPost('delayChart',this.postParams,'时延饼图查询',(data) => {
                this.handlerPieData(data,1);
            });
        },
        getTrendChart(){
            this.getPost('trendChart',this.postParams,'数据量趋势图',(data) => {
                this.handlerLineData(data,0);
            });
        },
        setIntervalTimer(){
            this.clearRefreshTimer();
            const that = this;
            this.ofRefreshTimer = setInterval(() => {
                that.getDelayChart();
                that.getTrendChart();
            },that.refresh * 60000);
        },
        clearRefreshTimer(){
            this.ofRefreshTimer && clearInterval(this.ofRefreshTimer);
        },
    }
};
</script>

<style lang="less" scoped>
.off-line{
    width:calc(25% - 16px);
    height: 452px;
    background: linear-gradient(135deg, #404a1b 0%, #1a3c4c 100%);
    border-radius: 12px 12px 12px 12px;
    position: relative;
    min-width: 400px;
}
.charts{
    width: 100%;
    height:calc(100% - 56px);
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    position: relative;
    .charts-item{
        width:100%;
        height:50%;
    }
}

</style>