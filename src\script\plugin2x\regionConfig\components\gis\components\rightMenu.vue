<!-- 右键菜单 -->
<template>
  <ul v-show="isShow" ref="rightMenu" class="right-menu" :style="{ top: y, left: x }">
    <li v-for="item in menuList" :key="item.prop" class="item" @click.stop="fnHandler(item)">
      <span>{{ item.label }}</span>
    </li>
  </ul>
</template>

<script>
const MENU_HEIGHT = 94;
const UNIT_ITEM_HEIGHT = 34;
export default {
  name: 'rightMenu',
  props: {
    // 接收右键点击的信息
    menuList: {
      type: Array,
      default: () => [],
    },
    isShow: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      x: '0px',
      y: '0px',
    };
  },
  mounted() {
    document.addEventListener('contextmenu', this.handleMenuClick);
    document.addEventListener('mouseup', this.close);
  },
  beforeDestroy() {
    document.removeEventListener('contextmenu', this.handleMenuClick);
    document.removeEventListener('mouseup', this.close);
  },
  methods: {
    handleMenuClick(e) {
      //阻止默认事件
      e.preventDefault();

      const menuWidth = MENU_HEIGHT;
      const menuHeight = this.menuList.length * UNIT_ITEM_HEIGHT;

      let innerWidth = window.innerWidth;
      let innerHeight = window.innerHeight;

      let posX = e.clientX;
      let posY = e.clientY;
      if (posX + menuWidth > innerWidth) {
        posX = innerWidth - menuWidth;
      }
      if (posY + menuHeight > innerHeight) {
        posY = innerHeight - menuHeight;
      }

      this.x = posX + 'px';
      this.y = posY + 'px';
    },
    close(e) {
      // 0是左键、1是滚轮按钮或中间按钮（若有）、2鼠标右键
      if (e.button === 0) {
         this.$emit('update:isShow', false);
      }
    },
    fnHandler(item) {
      // this.$emit('事件名',事件参数)
      this.$emit('selectType', item.prop)
    },
  },
};
</script>

<style lang="less" scoped>
.right-menu {
  // 固定定位，抬高层级，初始隐藏，右击时置为display:block显示
  position: fixed;
  display: block;
  margin: 0;
  padding: 4px 0;
  color: #333;
  background: #fff;
  border-radius: 4px;
  list-style-type: none;
  font-size: 12px;
  font-weight: 500;
  box-sizing: border-box;
  z-index: 3000;
  box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
  .item {
    box-sizing: border-box;
    padding: 5px 12px;
    font-size: 14px;
    font-family: AlibabaPuHuiTiR;
    color: #303133;
    border-radius: 4px;
    transition: all 0.36s;
    cursor: pointer;
    &:hover {
      color: #0091ff;
      background-color: #e6f6ff;
    }
  }
}
</style>
