<template>
    <div class="gis-com">
        <mtv-gis
            :id="gisId"
            :ref="gisId"
            :totaloptions="gisOptions"
            :autoActive="false"
            @onLoad="gisOnLoad"
        >
        </mtv-gis>
        <div class="gis-com__tools">
            <!-- 搜索 -->
            <!-- <searchBar v-if="showPanel" :city="city" :gisCenterMove="gisCenterMove" /> -->
            <!-- 工具箱、多边形、输入坐标 -->
            <div v-if="isShowTool && isAllowChangeLayer" class="gis-tool-modern">
                <div
                    class="tool-item"
                    :style="{
                        '--tool-icon': `url(${imgList.tool})`,
                        '--tool-icon-active': `url(${imgList.tool})`
                    }"
                >
                    <el-dropdown @command="clickTool" class="gis-dropdown" placement="bottom">
                        <span class="el-dropdown-link">
                            <div class="tool-icon"></div>
                            <div class="tool-label">工具箱</div>
                            <i class="el-icon-arrow-down el-icon--right"></i>
                        </span>
                        <el-dropdown-menu slot="dropdown" class="dropdown-menu-dark popover-dark">
                            <el-dropdown-item command="lineLayer">
                                <img :src="imgList.line" alt="" srcset="" />
                            </el-dropdown-item>
                            <el-dropdown-item command="clean">
                                <img :src="imgList.clean" alt="" srcset="" />
                            </el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                </div>
                <div
                    v-if="shapeType === 2"
                    class="tool-item"
                    :style="{
                        '--tool-icon': `url(${imgList['box-select']})`,
                        '--tool-icon-active': `url(${imgList['box-select']})`
                    }"
                    @click="drawAreas()"
                >
                    <div class="tool-icon"></div>
                    <div class="tool-label">画多边形</div>
                </div>
                <div
                    v-if="shapeType === 1"
                    class="tool-item"
                    :style="{
                        '--tool-icon': `url(${imgList['draw-circle']})`,
                        '--tool-icon-active': `url(${imgList['draw-circle']})`
                    }"
                    @click="drawAreas('circular')"
                >
                    <div class="tool-icon"></div>
                    <div class="tool-label">画圆形</div>
                </div>
            </div>

            <!-- 隐藏基站 && 基站管理 -->
            <div
                v-if="isShowHideManage || isShowBaseStationManage"
                class="gis-tool-modern"
                style="margin-left: 10px"
            >
                <div
                    v-if="isShowHideManage"
                    class="tool-item"
                    :style="{
                        '--tool-icon': `url(${getBaseIcon})`,
                        '--tool-icon-active': `url(${getBaseIcon})`
                    }"
                    @click="viewBaseStation()"
                >
                    <div class="tool-icon"></div>
                    <div class="tool-label">{{ getCheckBaseLabel }}</div>
                </div>
                <div
                    v-if="isShowBaseStationManage"
                    class="tool-item"
                    :style="{
                        '--tool-icon': `url(${imgList['base-station']})`,
                        '--tool-icon-active': `url(${imgList['base-station']})`
                    }"
                    @click="baseManage"
                >
                    <div class="tool-icon"></div>
                    <div class="tool-label">基站管理</div>
                </div>
            </div>
        </div>

        <!-- 资源 -->
        <!-- <layerResource
            v-if="isShowLayers"
            ref="layerResource"
            :curType="curType"
            :shapeType="shapeType"
            v-bind="$attrs"
        /> -->
        <!-- 右键菜单 -->
        <rightMenu
            :isShow.sync="isShowMenu"
            :menuList="menuList"
            @selectType="(prop) => layerObj.selectMenu(prop)"
        />
        <!-- 右键辐射菜单 -->
        <radiationRange
            ref="radiationRangeRef"
            :isShow.sync="isShowRadiation"
            :coordinate="coordinate"
            @sure="(distance) => layerObj.updateRadiation(distance)"
        />
        <!-- 基站管理 -->
        <baseManage
            v-if="isShowBaseManage"
            :drawer.sync="isShowBaseManage"
            :baseStations="allBaseStations"
            @getAddedStations="getAddedStations"
            @handleClose="setBasePoints"
        />
    </div>
</template>
<script>
import layerResource from './layerResource/index.vue';
import imgList from './common/imgList';
import entryRegion from './components/entryRegion.vue';
import * as gisUtils from './common/gisUtils';
import { removeDuplicateLocations, formatEnterAreas } from './common/method.js';
import { gisOptions, menuList } from './common/gis';
import Polygon from './polygonInstance.js';
import Circular from './circleInstance.js';
import addedIcon from '@/img/gis/basePoint.png';
import noAddIcon from '@/img/gis/noAdd.png';
import activeBaseIcon from '@/img/gis/activeBase.png';
import { request } from '@/script/utils/request.js';
import { getAddress } from '@/script/common/BaiduPlaceApi.js';
export default {
    name: 'gis-com',
    components: {
        layerResource,
        entryRegion,
        selectDistrict: () => import('_com/selectDistrict.vue'),
        rightMenu: () => import('./components/rightMenu.vue'),
        radiationRange: () => import('./components/radiationRange.vue'),
        baseManage: () => import('./baseManage/index.vue'),
        searchBar: () => import('./components/searchPlace.vue')
    },
    props: {
        // 展示搜索框
        showPanel: {
            type: Boolean,
            default: true
        },
        curType: {
            type: String
        },
        city: {
            type: Array,
            default: () => []
        },
        showExtend: {
            type: Boolean,
            default: false
        },
        isShowTool: {
            type: Boolean,
            default: false
        },
        showResourcesData: {
            type: Boolean,
            default: false
        },
        // 区块数据
        gisData: {
            type: Object,
            default: () => ({})
        },
        // 区域数据
        regionCoors: {
            type: Array,
            default: () => []
        },
        // 资源数据
        resourcesData: {
            type: Array,
            default: () => []
        },
        isShowLayers: {
            type: Boolean,
            default: true
        },
        isJustShowLayer: {
            type: Boolean,
            default: false
        },
        isShowHideManage: {
            type: Boolean,
            default: false
        },
        isShowBaseStationManage: {
            type: Boolean,
            default: false
        },
        isCreated: {
            type: Boolean,
            default: true
        },
        baseInfo: {
            type: Object,
            default: () => ({})
        },
        defExpansion: {
            type: Number,
            default: 0
        },
        shapeType: {
            // 多边形或者圆形，默认多边形
            type: Number,
            default: 2
        }
    },
    data() {
        return {
            addedIcon,
            noAddIcon,
            activeBaseIcon, // todo，待定
            isCollapse: false,
            imgList,
            gisId: 'refs_rams_gis_api_config',
            g: null,
            menuList,
            isShowMenu: false,
            isShowRadiation: false,
            isShowBaseManage: false,
            baseStations: [],
            allBaseStations: [],
            DrawArea: Polygon, // 默认是多边形
            planeObjs: [],
            layerObj: {},
            circleChoices: {},
            curCircleChoiceObj: {},
            coordinate: {},
            isCheckBase: false,
            addStations: [],
            multiCreatedStations: [], // 已创建的复合区域的区域内基站
            circleType: '', //圆形圈选类型 'circular'圆 'circleChoice'辅助圆
            entryHoleList: [],
            isManualEntry: false
        };
    },
    computed: {
        curCityPath({ city }) {
            if (city && city.length) {
                return city.slice(0, 2).join(',');
            }
            return '';
        },
        isAllowChangeLayer() {
            return ['singleEntry'].includes(this.curType);
        },
        getCheckBaseLabel() {
            return this.isCheckBase ? '隐藏基站' : '查看基站';
        },
        getBaseIcon() {
            return this.isCheckBase ? imgList.hideManage : imgList.eyes;
        },
        // 是否为已创建的复合区域
        isMultiRegion() {
            return Boolean(
                this.baseInfo.multiPolygonList && this.baseInfo.multiPolygonList.length > 1
            );
        },
        isPolygon() {
            return this.shapeType === 2;
        },
        currentCity() {
            return frameService.citySelected.now.name;
        },
        gisOptions() {
            const options = JSON.parse(JSON.stringify(gisOptions(this.getMayType())));
            options['city']['cityName'] = this.currentCity || '济南';
            return options;
        }
    },
    watch: {
        curCityPath: {
            async handler(newPath) {
                const areas = newPath.split(',');
                const city = areas[1];
                if (!city) return;

                const result = await request('post', 'getCenter', {
                    type: 'district',
                    code: areas[1]
                });
                console.log(result);
                const { latitude, longitude } = result;

                if (this.g) {
                    this.g.cameraControl.gradualChangeRadius({
                        targetPoint: {
                            lat: latitude,
                            lng: longitude
                        },
                        zoom: 10
                    });
                }
            }
        },
        shapeType: {
            handler(newV) {
                // 切换轮廓类型，清空数据
                this.clearAll(true);
                this.DrawArea = this.getDrawArea(newV);
                this.DrawArea.initGisInfo(this.g, this);
            }
        }
    },
    methods: {
        // 设置坐标
        setAreaCoors(regionList, holeList) {
            if (!regionList.length) {
                this.$message.warning('请输入区域坐标');
            }
            this.clearAll();
            const { holeRegionCoors, invalidHoleList } = formatEnterAreas(
                regionList,
                holeList,
                this.g,
                this.shapeType
            );
            this.entryHoleList = holeRegionCoors;
            if (invalidHoleList.length) {
                this.$message.info(`存在以下无效的空洞坐标：${invalidHoleList.join('、')}`);
            }
            // this.DrawArea.initRegionCoors(regionList, false, false, true);
            this.isManualEntry = true;
        },
        clickTool(type) {
            this.start(type);
        },
        gisCenterMove(pointData) {
            const data = pointData;
            const point = {
                lat: data.Y,
                lng: data.X
            };
            this.g.cameraControl.move(point);
            setTimeout(() => {
                this.g.gis.needUpdate = true;
                this.g.cameraControl.zoom = 17;
            }, 300);
        },
        //
        getMayType() {
            let hostName = window.location.hostname;
            // 判断是本地、测试内网还是生产地址。
            if (
                hostName.indexOf('localhost') >= 0 ||
                hostName.indexOf('192.168') >= 0 ||
                hostName.indexOf('127.0') >= 0
            ) {
                return 'tx';
            }
            return 'default';
        },
        gisOnLoad() {
            this.g = this.$refs[this.gisId].getEntity();
            this.g.tool.addSearch({
                type: '地址',
                show: '请输入地址',
                callBack: (value) => getAddress(value, this.g),
                index: Infinity
            });
            if (this.getMayType() === 'default') {
                this.g.tileLayerList['底图图层'].visible = false;
                //重写getUrl方法
                this.g.tileLayerList['高德底图'].getUrl = (x, y, z) => {
                    return `${location.origin}/MapUrl/api/appmaptile?x=${x}&y=${y}&z=${z}`;
                };
            } else {
                this.g.tileLayerList['高德底图'] &&
                    (this.g.tileLayerList['高德底图'].visible = false);
            }

            // 改变底图颜色
            // 获取底图图层
            const mapName = (() => {
                if (this.getMayType() === 'default') {
                    return '高德底图';
                }
                return '底图图层';
            })();
            let map = this.g.tileLayerList[mapName];
            let colors = [0x09ecf6, 0x090c3e];
            map.colorChangeDate.dark.setHex(colors[0]);
            map.colorChangeDate.light.setHex(colors[1]);
            this.g.gis.color = colors[1];

            this.initGisOps();
            // 加载后监听
            this.$watch(
                'regionCoors',
                (val) => {
                    if (val && val.length) {
                        // this.DrawArea.initRegionCoors(val);
                    }
                },
                { immediate: true, deep: true }
            );
        },
        initGisOps() {
            const g = this.g;

            this.baseLayer = new g.layer();
            this.baseLayer.visible = true;
            g.gis.scene.add(this.baseLayer);

            g.layerList.lineLayer.autoButton = false;
            g.layerList.boxSelectLayer.autoButton = false;
            g.layerList.areaEdit.autoButton = true;
            g.layerList['圈选'].autoClear = false;
            g.layerList.圈选.unitMeter = true;
            g.layerList.圈选.Group.position.y = 0.005;
            gisUtils.addBoxSelectLayerListen(g, this.stop, this);
            gisUtils.addLineLayerListen(g, this.stop, this);
            // 基点点击事件
            g.event.addClick(this.baseLayer, (data, event) => {
                if (this.isJustShowLayer || event.button !== 0) return;
                const pointType = data.object.name;
                const inx = g.meshList.img.getIndex(data);
                let repeatIndex = [];
                if (pointType === 'added') {
                    repeatIndex = this.addedPoints[inx].repeatIndex;
                } else if (pointType === 'noAdd') {
                    repeatIndex = this.noAddPoints[inx].repeatIndex;
                }
                this.allBaseStations.forEach((item, index) => {
                    if (!repeatIndex.includes(index)) {
                        return;
                    }
                    if (item.status === 'added') {
                        item.status = 'noAdd';
                        item.onceAdded = true;
                    } else {
                        item.status = 'added';
                        item.onceAdded = true;
                    }
                });
                this.setBasePoints();
            });
            // 圆形 && 圈选点击事件-右键
            g.event.addClick(g.layerList['圈选'], (data, event) => {
                if (this.isJustShowLayer) return;
                const obj = data.object;
                const name = String(obj.name);
                if (!name.startsWith('circular')) {
                    this.curCircleChoiceObj = data.object;
                }
                if (event.button === 2) {
                    // 右键
                    const i = Number(name.replace(/[^\d]/g, ''));
                    if (!this.layerObj || i !== this.layerObj.i) {
                        this.layerObj = this.planeObjs[i];
                    }
                    if (name.startsWith('circular')) {
                        this.menuList = menuList.filter((item) => item.prop === 'delCircular');
                        this.layerObj.region.plane = obj;
                    }
                    this.isShowMenu = true;
                }
            });
            //监听圈选
            g.layerList['圈选'].onCircleChoice.addEvent((data, event) => {
                let { startPoint, radius, name } = data;
                // 第一次画圆
                if (typeof name === 'number') {
                    if (this.circleType === 'circular') {
                        data.name = `${this.circleType}-${this.layerObj.i}`;
                    } else {
                        data.name = `${this.circleType}${name}`;
                    }
                    name = data.name;
                } else {
                    const i = Number(name.replace(/[^\d]/g, ''));
                    if (!this.layerObj || i !== this.layerObj.i) {
                        this.layerObj = this.planeObjs[i];
                    }
                }

                if (name.startsWith('circular')) {
                    this.DrawArea.circleFinish(radius, startPoint, name);
                } else {
                    this.circleChoices[name] = {
                        radius,
                        centerLatitude: startPoint.lat,
                        centerLongitude: startPoint.lng,
                        choicePoints: []
                    };
                    const choicePoints = [];
                    for (const item of this.allBaseStations) {
                        const distance = g.math.worldDistance(item, startPoint);
                        if (distance <= radius && item.status === 'noAdd') {
                            item.status = 'added';
                            item.onceAdded = true;
                            choicePoints.push(item);
                        }
                    }
                    if (choicePoints.length) {
                        this.circleChoices[name]['choicePoints'] = choicePoints;
                        this.setBasePoints();
                    }
                }
            });
            //
            this.DrawArea = this.getDrawArea(this.shapeType);
            this.DrawArea.initGisInfo(g, this);
        },
        getDrawArea(shapeType = 2) {
            return shapeType === 2 ? Polygon : Circular;
        },
        clearAll(isClearAll = false) {
            this.g.layerList['圈选'].removeAll();
            this.g.layerList.areaEdit.removeAll();
            this.baseLayer.removeAll();
            this.DrawArea.clearAll(isClearAll);
            this.layerObj = {};
            this.planeObjs.forEach((item) => {
                if (item) item.hole.plane = [];
            });
            this.planeObjs = [];
            this.circleChoices = {};
            this.curCircleChoiceObj = {};
            this.multiCreatedStations = [];
            this.entryHoleList = [];
            if (this.$refs.entryRegion) {
                this.$refs.entryRegion.clear();
            }
        },
        // 测距等工具栏功能专用
        stop() {
            const g = this.g;
            gisUtils.runGisFun(g, 'lineLayer', 'outMode');
            gisUtils.runGisFun(g, 'boxSelectLayer', 'outMode');
        },
        drawAreas(type = 'areaEdit') {
            const len = this.planeObjs.length;
            this.planeObjs[len] = new this.DrawArea(len);
            this.layerObj = this.planeObjs[len];
            this.DrawArea.setCurOperateItem(this.layerObj.region);
            this.start(type);
        },
        start(type) {
            const g = this.g;
            this.stop();
            if (type === 'clean') {
                this.clearAll();
            } else if (type === 'areaEdit') {
                this.$message(
                    '请开始绘制图形,双击结束绘制,点击图形进入编辑模式，双击边缘点退出编辑'
                );
                gisUtils.runGisFun(g, 'areaEdit', 'startEdit');
            } else if (type === 'circular') {
                this.$message(
                    '请开始绘制圆形，左击地图上任意一点，并拖动鼠标，拖动结束后即完成绘制'
                );
                g.layerList.圈选.circleColor = 0x0085f9;
                g.layerList.圈选.circleFrameColor = 0x1a7cff;
                g.layerList.圈选.startMode();
                this.circleType = 'circular';
            } else {
                // 测距
                gisUtils.runGisFun(g, type, 'startMode');
            }
        },
        // 基站打点
        setBasePoints(isEditedHole = false) {
            // 修改空洞里的基站
            isEditedHole && this.layerObj.handlerHolePoints(this.allBaseStations);
            this.baseStations = removeDuplicateLocations(this.allBaseStations);
            if (!this.isCheckBase) return;
            const g = this.g;
            const addedPoints = [];
            const noAddPoints = [];

            for (const item of this.baseStations) {
                if (item.status === 'added') {
                    addedPoints.push(item);
                } else if (item.status === 'noAdd') {
                    noAddPoints.push(item);
                }
            }
            Object.assign(this, { addedPoints, noAddPoints });
            // 删除所有模型
            this.baseLayer.removeAll();
            //模型添加进图层
            const addedMesh = this.createPointsMesh(addedPoints, 'added');
            const noAddMesh = this.createPointsMesh(noAddPoints, 'noAdd');
            addedMesh && this.baseLayer.add(addedMesh);
            noAddMesh && this.baseLayer.add(noAddMesh);
            //更新GIS
            g.gis.needUpdate = true;
        },
        createPointsMesh(points, status) {
            if (!points.length) return;
            const g = this.g;
            const icon = status === 'added' ? addedIcon : noAddIcon;
            const material = g.meshList.img.getMaterial({ url: icon, opacity: 1 });
            points.autoScale = true;
            const mesh = g.meshList.img.create(points, material);
            mesh.name = status;
            return mesh;
        },
        // 查看/隐藏基站
        viewBaseStation() {
            this.isCheckBase = !this.isCheckBase;
            if (!this.isCheckBase) {
                this.baseLayer.removeAll();
                return;
            }
            this.setBasePoints(this.isManualEntry);
            if (this.isManualEntry) {
                this.isManualEntry = false;
            }
        },
        // 单一多边形区域获取基站点/辐射
        baseManage() {
            this.isShowBaseManage = true;
        },
        // 圈选初始化
        drawCircleChoice(circleType = 'circleChoice') {
            const circleList = this.baseInfo.auxiliaryCircleList;
            circleList &&
                circleList.forEach((item, inx) => {
                    const { centerLatitude, centerLongitude, radius } = item;
                    const name = `${circleType}-${inx + 1}`;
                    this.g.layerList.圈选.create({
                        name,
                        ...gisOptions().circleChoice,
                        circleShowRadius: !this.isJustShowLayer,
                        radius,
                        startPoint: { lat: centerLatitude, lng: centerLongitude }
                    });
                    this.circleChoices[name] = {
                        ...item,
                        choicePoints: []
                    };
                    const addedBaseStations = this.allBaseStations.filter(
                        (item) => item.status === 'added'
                    );
                    for (const baseStation of addedBaseStations) {
                        const distance = this.g.math.worldDistance(baseStation, {
                            lat: centerLatitude,
                            lng: centerLongitude
                        });
                        if (distance <= radius) {
                            this.circleChoices[name].choicePoints.push(baseStation);
                        }
                    }
                });
        },
        /* -------------外层组件调用----------------- */
        getAddedBasePoints() {
            // 如果没有打开基站管理，过滤下全部基站数据
            if (!this.addStations.length) {
                return this.allBaseStations
                    .filter((item) => item.onceAdded)
                    .filter((item) => item.status === 'added');
            }
            return this.addStations;
        },
        getAddedStations(addStations) {
            this.addStations = addStations;
        },
        getHolePlanes() {
            return this.DrawArea.holeLayer.Group.children;
        }
    }
};
</script>
<style lang="less" scoped>
@import url('./gisCom.less');
/deep/.pcGis .searchControl {
    left: 10px;
    top: 10px;
    width: 360px;
    height: 40px;
    background: rgba(18, 61, 124, 0.9);
    border-radius: 2px;
    border: 1px solid rgba(0, 149, 255, 0.5);
    .search_loadding {
        height: 100%;
        .search_loadding_box {
            margin: 5px 16px;
        }
    }
    select {
        height: 100%;
        color: #c9dfff;
        background: rgba(18, 61, 124, 0.9);
        border-right: 1px solid rgba(0, 149, 255, 0.5);
        option {
            color: #c9dfff;
            background-color: var(--dark-bg) !important;
        }
    }
    input {
        height: 100%;
        color: #c9dfff;
        background: rgba(18, 61, 124, 0.9);
        border: none;
        &::placeholder {
            color: #ffffff73;
        }
    }
    .searchButton {
        height: 100%;
        background-size: 16px 16px;
    }
}
</style>
<style lang="less">
/deep/ .el-dropdown-menu__item {
    padding: 0 5px;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
        width: 20px;
        height: 20px;
    }
}

/deep/ .el-dropdown-menu {
    background: rgba(18, 61, 124, 0.9);
    border: 1px solid rgba(0, 149, 255, 0.5);
}

/deep/ .el-dropdown-menu__item:hover {
    background-color: rgba(1, 230, 254, 0.1);
    color: #01e6fe;
}
</style>
