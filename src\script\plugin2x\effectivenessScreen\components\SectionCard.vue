<template>
    <section class="section-card">
        <header class="section-card-header" :class="titleBg">
            <div class="section-card-header-title">
                <span class="section-card-header-title-text"> {{ title }} </span>
                <div class="section-card-header-title-icon" v-if="showIcon"></div>
            </div>
            <div class="section-card-header-right">
                <div class="section-card-header-right-item">
                    <slot name="titleRight"></slot>
                </div>
            </div>
        </header>
        <main class="section-card-main">
            <slot></slot>
        </main>
    </section>
</template>
<script>
export default {
    name: 'sectionCard',
    props: {
        title: {
            type: String,
            default: ''
        },
        titleBg: {
            type: String,
            default: 'title-bg1' // [title-bg1, title-bg2]
        },
        showIcon: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {};
    },
    methods: {}
};
</script>
<style lang="less" scoped>
.section-card {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    .section-card-header {
        width: 100%;
        height: 1.7778rem;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        display: flex;
        align-items: center;
        &.title-bg1 {
            background-image: url('../../../../img/effectivenessScreen/section-card-bg1.png');
        }
        &.title-bg2 {
            background-image: url('../../../../img/effectivenessScreen/section-card-bg2.png');
        }
        &-title {
            width: fit-content;
            height: 100%;
            display: flex;
            align-items: center;
            padding: 0 0.6667rem;
            &-text {
                margin-left: 1.1111rem;
                font-family: PingFangSC, PingFang SC;
                font-weight: 600;
                font-size: 0.8889rem;
                color: #ffffff;
                line-height: 1.2222rem;
                text-shadow: 0px 0.1111rem 0.2222rem rgba(0, 0, 0, 0.15);
            }
            &-icon {
                margin-left: 0.4444rem;
                width: 0.8889rem;
                height: 0.8889rem;
                background-image: url('../../../../img/effectivenessScreen/title-info.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
            }
        }
        &-right {
            min-width: 0;
            flex: 1;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-right: 0.4444rem;
        }
    }
    .section-card-main {
        width: 100%;
        min-height: 0;
        flex: 1;
    }
}
</style>
