<template>
  <el-dialog
    class="confirm-create"
    title=""
    :visible.sync="visible"
    :close-on-click-modal="false"
    width="500px"
    destroy-on-close
    @open="handleOpen"
    @close="handleClose"
  >
    <!-- 主体 -->
    <div class="confirm-create__body">
      <div class="tip">
        <img :src="tipImg" alt="" />
        <span class="text">创建区域有重叠</span>
      </div>
      <div class="select-create">
        <el-radio v-model="radio" :label="1">继续创建</el-radio>
        <el-radio v-model="radio" class="mt-8" :label="2">
          <span class="mt-6">覆盖重复区域</span>
          <el-select v-if="radio === 2" v-model="area" size="mini">
            <el-option
              v-for="item in overlaps"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            />
          </el-select>
        </el-radio>
      </div>
    </div>
    <!-- 底部 -->
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="handleClose">取消</el-button>
      <el-button type="primary" size="small" @click="sure">确定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import tipImg from '@/img/createResource/tip.png';
export default {
  name: 'confirm-create',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    areas: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      tipImg,
      area: '',
      radio: 1,
    };
  },
  computed: {
    overlaps() {
      return this.areas.map((item) => ({
        value: item.regionId,
        label: item.regionName,
      }));
    },
  },
  methods: {
    sure() {
        this.$emit('createResource', this.radio, this.area);
    },
    handleOpen() {},
    handleClose() {
      this.$emit('update:visible', false);
      this.$message.warning('资源还未创建完成，若返回将清除已填写内容');
    },
  },
};
</script>
<style lang="less" scoped>
.confirm-create {
  &__body {
    .tip {
      font-size: 18px;
      font-weight: bold;
      height: 40px;
      .text {
        vertical-align: middle;
      }
    }

    .select-create {
      margin: 12px 0 0 28px;
      display: flex;
      flex-direction: column;
    }
  }
  /deep/ .el-dialog {
    border-radius: 10px;
  }
  /deep/ .el-dialog__body {
    padding: 16px 30px 8px 30px;
  }
}
.bold {
  font-weight: bold;
  color: #454545;
}
.mt-6 {
  margin-right: 6px;
}
.mt-8 {
  margin-top: 8px;
}
</style>
