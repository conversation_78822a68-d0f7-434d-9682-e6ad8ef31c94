<template>
    <div class="home-page">
        <div class="home-content">
            <!-- 最上部分 标题以及介绍 -->
            <div class="home-content-header">
                <h1 class="home-content-header-title">位置定位分析平台</h1>
                <p
                    class="home-content-header-desc"
                    v-for="(item, index) in headerDescList"
                    :key="index"
                >
                    {{ item }}
                </p>
                <div class="home-content-header-data">
                    <div
                        class="home-content-header-data-item"
                        v-for="(item, index) in headerDataList"
                        :key="index"
                    >
                        <div class="home-content-header-data-item-value">
                            <span class="item-value">{{ item.value }}</span>
                            <span class="item-unit">{{ item.unit }}</span>
                        </div>
                        <div class="home-content-header-data-item-title">
                            {{ item.title }}
                        </div>
                    </div>
                </div>
            </div>
            <!-- 中间部分 section -->
            <div class="home-content-body">
                <sectionCard class="home-content-body-section" title="平台功能架构">
                    <img :src="platformGif" alt="" style="height: 33.33rem; width: 100%" />
                    <!-- <div style="height: 600px; width: 100%"></div> -->
                </sectionCard>
                <sectionCard class="home-content-body-section" title="热门排行榜">
                    <el-row class="body-section hot-rank-list">
                        <el-col
                            :span="8"
                            v-for="(item, index) in hotRankList"
                            :key="index"
                            class="list-item"
                        >
                            <div class="list-item-title">
                                <div class="title-icon"></div>
                                <span>{{ item.title }}</span>
                            </div>
                            <div class="list-item-rank">
                                <div
                                    class="rank-list"
                                    v-for="(rank, index) in item.rank"
                                    :key="index"
                                >
                                    <div
                                        class="rank-index"
                                        :class="index < 3 ? 'yellow-index' : 'blue-index'"
                                    >
                                        {{ index + 1 }}
                                    </div>
                                    <div class="rank-item">{{ rank }}</div>
                                </div>
                            </div>
                        </el-col>
                    </el-row>
                </sectionCard>
                <sectionCard class="home-content-body-section" title="精品能力展示">
                    <div class="body-section excellent-ability-list">
                        <el-carousel
                            arrow="never"
                            indicator-position="none"
                            :autoplay="false"
                            :loop="false"
                            class="ability-carousel"
                            ref="abilityCarousel"
                            @change="handleAbilityCarouselChange"
                        >
                            <el-carousel-item
                                v-for="(group, groupIndex) in abilityGroups"
                                :key="groupIndex"
                            >
                                <div class="ability-carousel-content">
                                    <div
                                        class="ability-item"
                                        v-for="(item, index) in group"
                                        :key="index"
                                    >
                                        <div class="ability-card flex-col">
                                            <div class="ability-header flex-row">
                                                <div class="ability-icon flex-col"></div>
                                                <span class="ability-title">{{ item.title }}</span>
                                            </div>
                                            <span class="ability-desc">{{ item.desc }}</span>
                                        </div>
                                    </div>
                                </div>
                            </el-carousel-item>
                        </el-carousel>
                        <!-- 左按钮 -->
                        <div
                            class="carousel-btn carousel-btn-left"
                            :class="{ 'carousel-btn-disabled': currentAbilityIndex === 0 }"
                            @click="prevItems('abilityCarousel')"
                        >
                            <img src="../../../../img/homePage/btn-left.png" alt="上一页" />
                        </div>
                        <!-- 右按钮 -->
                        <div
                            class="carousel-btn carousel-btn-right"
                            :class="{
                                'carousel-btn-disabled':
                                    currentAbilityIndex >= Math.ceil(abilityList.length / 3) - 1
                            }"
                            @click="nextItems('abilityCarousel')"
                        >
                            <img src="../../../../img/homePage/btn-right.png" alt="下一页" />
                        </div>
                    </div>
                </sectionCard>
                <sectionCard class="home-content-body-section" title="应用案例介绍">
                    <div class="body-section application-case-list">
                        <el-carousel
                            arrow="never"
                            indicator-position="none"
                            :autoplay="false"
                            :loop="false"
                            class="application-carousel"
                            ref="applicationCarousel"
                            @change="handleApplicationCarouselChange"
                        >
                            <el-carousel-item v-for="(item, index) in applicationList" :key="index">
                                <div class="application-carousel-content">
                                    <div class="application-left">
                                        <h3 class="application-left-title">
                                            {{ item.title }}
                                        </h3>
                                        <div class="application-left-relation">
                                            <div
                                                class="application-left-relation-item"
                                                v-for="(itm, ind) in item.relation"
                                                :key="ind"
                                            >
                                                <div class="item-left">
                                                    【痛点{{ ind + 1 }}】{{ itm.painPoint }}
                                                </div>
                                                <img
                                                    class="item-flowicon"
                                                    src="../../../../img/homePage/right-flow.png"
                                                    alt=""
                                                />
                                                <div class="item-right">
                                                    【成效{{ ind + 1 }}】{{ itm.effect }}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="application-left-desc">
                                            <h3 class="application-left-desc-title">案例介绍</h3>
                                            <p class="application-left-desc-content">
                                                {{ item.desc }}
                                            </p>
                                        </div>
                                    </div>
                                    <div class="application-right">
                                        <cornerCard class="application-right-card">
                                            <img
                                                class="application-right-card-img"
                                                :src="item.image"
                                                alt=""
                                            />
                                        </cornerCard>
                                    </div>
                                </div>
                            </el-carousel-item>
                        </el-carousel>
                        <!-- 左按钮 -->
                        <div
                            class="carousel-btn carousel-btn-left"
                            :class="{ 'carousel-btn-disabled': currentApplicationIndex === 0 }"
                            @click="prevItems('applicationCarousel')"
                        >
                            <img src="../../../../img/homePage/btn-left.png" alt="上一页" />
                        </div>
                        <!-- 右按钮 -->
                        <div
                            class="carousel-btn carousel-btn-right"
                            :class="{
                                'carousel-btn-disabled':
                                    currentApplicationIndex >= applicationList.length - 1
                            }"
                            @click="nextItems('applicationCarousel')"
                        >
                            <img src="../../../../img/homePage/btn-right.png" alt="下一页" />
                        </div>
                    </div>
                </sectionCard>
            </div>
            <div class="bottom-bg"></div>
        </div>
    </div>
</template>
<script>
import sectionCard from '_com/sectionCard.vue';
import cornerCard from '_com/cornerCard.vue';
export default {
    name: 'homePage',
    components: {
        sectionCard,
        cornerCard
    },
    data() {
        return {
            headerDescList: [
                '位置定位分析系统是"2+5+N"网管架构中的专项应用系统，构建基于位置大数据的全网精准定位能力。该系统汇聚无线网络及位置类数据，通过构建指纹库、4/5G融合定位、实时电子围栏等技术，实现用户全时空精准定位，并形成电子围栏服务、手机号精准位置查询两大核心能力。',
                '系统提供实时与离线双模式服务，对内封装标准API，支持单用户实时定位、职住信息检索及区域统计查询；对外输出实时定位数据、职住数据及电子围栏事件通知，赋能精准营销、区域管控等业务场景。同时，核心能力已沉淀至省级能力开放平台，支撑位置数据价值变现，助力智慧城市、应急管理等行业应用。'
            ],
            headerDataList: [
                {
                    value: '<500',
                    unit: '(MS)',
                    title: '位置数据时延'
                },
                {
                    value: '1.1',
                    unit: '(万次)',
                    title: '服务调用次数'
                },
                {
                    value: '160',
                    unit: '(次)',
                    title: '应用支撑频率'
                },
                {
                    value: '500',
                    unit: '(万元)',
                    title: '平台应用价值'
                }
            ],
            platformGif: require('../../../../img/homePage/platform-fun.gif'),
            hotRankList: [
                {
                    title: '软采数据质量城市TOP5',
                    rank: ['济南市', '济宁市', '烟台市', '枣庄市', '东营市']
                },
                {
                    title: '热门服务调用次数TOP5',
                    rank: [
                        '用户最新位置点查询接口',
                        '区域热力信息获取接口',
                        '用户轨迹查询接口',
                        '路网拟合轨迹查询',
                        '事件任务订阅接口'
                    ]
                },
                {
                    title: '热门应用点击次数TOP5',
                    rank: [
                        '实时位置定位功能',
                        '历史轨迹查询（基站级）',
                        '常驻地查询',
                        '电子围栏分布热力图',
                        '地市级常驻人员热力图'
                    ]
                }
            ],
            abilityList: [
                {
                    title: '电子围栏动态监测与触达服务能力',
                    desc: '通过构建自定义电子围栏，实时监测区域内人口动态，精准识别用户进出、位置及常驻属性，支持围栏的新增、删除及短信触达服务，可应用于应急管理、文旅分析等场景，提供流动监控、安全预警及信息推送一体化的解决方案。'
                },
                {
                    title: '基于手机号码的位置通定位能力',
                    desc: '基于LBS定位技术，提供实时位置监测、轨迹回放、电子围栏及位置分析能力，支持紧急定位与智能告警，本能力面向企业、司法、及各类应急部门，助力人员管控、安全调度与快速响应，提升位置服务效能。'
                },
                {
                    title: '路网拟合定位能力',
                    desc: '获取MDT/MR样本数据，通过对样本数据进行清洗，用机器学习的方法进行训练，输出道路拟合模型。应用于用户行驶轨迹，实现用户级定位轨迹信息打涝，支撑全网用户终端24小时连续位置特性。'
                }
            ],
            currentAbilityIndex: 0,
            applicationList: [
                {
                    title: '黑龙江-基于数字孪生的实时智能场景保障',
                    relation: [
                        {
                            painPoint: '多源数据融合复杂',
                            effect: '数字孪生统一展示关键指标'
                        },
                        {
                            painPoint: '智能分析准确率不足',
                            effect: '情景再现辅助根因分析'
                        },
                        {
                            painPoint: '动态场景还原挑战',
                            effect: '实时数据驱动资源调度自动化'
                        }
                    ],
                    desc: '以数字孪生为展示基础技术，实现保障资源、用户数、业务量、资源效率、干扰、无线性能、用户业务、数据感知、语音感知多维指标和预警问题点的监控一页展示，支持问题指标高亮预警，支持以时间轴方式对保障场景情景再现，可与实时问题自动驾驶能力进行融合保障。',
                    image: require('../../../../img/homePage/temp-1.png')
                }
            ],
            currentApplicationIndex: 0
        };
    },
    computed: {
        abilityGroups() {
            const groups = [];
            for (let i = 0; i < this.abilityList.length; i += 3) {
                groups.push(this.abilityList.slice(i, i + 3));
            }
            return groups;
        }
    },
    methods: {
        prevItems(ref) {
            this.$refs[ref].prev();
        },
        nextItems(ref) {
            this.$refs[ref].next();
        },
        handleAbilityCarouselChange(index) {
            this.currentAbilityIndex = index;
        },
        handleApplicationCarouselChange(index) {
            this.currentApplicationIndex = index;
        }
    }
};
</script>
<style lang="less" scoped>
.home-page {
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, rgb(1, 23, 60) 0%, rgb(1, 23, 60) 100%);
    position: relative;

    .home-content {
        position: relative;
        z-index: 1;
        width: 100%;
        height: 100%;
        overflow-y: auto;
        &::before {
            content: '';
            position: absolute;
            top: -5.56rem;
            left: 0;
            right: 0;
            width: 100%;
            background-image: url('../../../../img/homePage/home-bg-top.png');
            background-size: cover;
            background-repeat: no-repeat;
            background-position: top center;
            clip-path: inset(5.56rem 0 0 0);
            padding-top: 38.8%; /* 保持宽高比 745/1920=0.388 */
            height: 0;
        }
        &::-webkit-scrollbar {
            z-index: 999;
            width: 0.44rem;
            height: 0.33rem;
        }
        &::-webkit-scrollbar-thumb {
            border-radius: 0.56rem;
            box-shadow: inset 0 0 0.28rem rgba(0, 0, 0, 0.1);
            background: #5c6f92;
        }
        &::-webkit-scrollbar-track {
            /* 滚动条里面轨道 */
            box-shadow: inset 0 0 0.28rem rgba(0, 0, 0, 0.1);
            border-radius: 0.56rem;
            background: transparent;
        }
        &::-webkit-scrollbar-corner {
            background: rgba(0, 0, 0, 0);
        }

        .home-content-header {
            width: 100%;
            position: relative;
            padding: 0 10rem;
            margin-bottom: 3.67rem;
            &-title {
                font-family: YouSheBiaoTiHei;
                font-size: 2.78rem;
                color: #ffffff;
                line-height: 2.78rem;
                letter-spacing: 0.06rem;
                text-shadow: 0 0.06rem 0.11rem rgba(0, 0, 0, 0.5);
                margin-top: 3.33rem;
            }
            &-desc {
                text-indent: 2em;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 1rem;
                color: #d1e4ff;
                line-height: 1.78rem;
                text-align: left;
                font-style: normal;
            }
            &-data {
                display: flex;
                flex-direction: row;
                justify-content: space-around;
                margin-top: 1.33rem;
                &-item {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    &-value {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        background-image: url('../../../../img/homePage/header-data-bg.png');
                        background-size: 100% 100%;
                        background-repeat: no-repeat;
                        background-position: center center;
                        width: 11.72rem;
                        height: 11.89rem;
                        .item-value {
                            font-family: HONORSansCN, HONORSansCN;
                            font-weight: 800;
                            font-size: 2.22rem;
                            color: #ffffff;
                            line-height: 2.94rem;
                            text-shadow: 0 0 0.44rem rgba(1, 102, 255, 0.8);
                        }
                        .item-unit {
                            font-family: PingFangSC, PingFang SC;
                            font-weight: 500;
                            font-size: 1rem;
                            color: #ffffff;
                            line-height: 1.39rem;
                            text-shadow: 0 0 0.44rem rgba(1, 102, 255, 0.8);
                        }
                    }
                    &-title {
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 600;
                        font-size: 1.33rem;
                        color: #d1e4ff;
                        line-height: 1.83rem;
                    }
                }
            }
        }
        .home-content-body {
            width: 100%;
            position: relative;
            padding: 0 10rem;
            z-index: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 3.67rem;

            &-section {
                &:last-child {
                    margin-bottom: 3.67rem;
                }
                .body-section {
                    width: 100%;
                    height: 100%;
                    padding: 1.33rem 0;
                }
                .hot-rank-list {
                    height: 20.89rem;
                    display: flex;
                    gap: 2rem;
                    align-items: center;
                    justify-content: space-between;
                    .list-item {
                        width: 100%;
                        height: 100%;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        &-title {
                            display: flex;
                            align-items: center;
                            margin-left: 0.89rem;
                            margin-bottom: 1rem;
                            .title-icon {
                                width: 1.11rem;
                                height: 2.17rem;
                                margin-right: 0.89rem;
                                position: relative;

                                &::before,
                                &::after {
                                    content: '';
                                    position: absolute;
                                    // left: 20px;
                                    bottom: 0.22rem;
                                    transform: skewX(-30deg);
                                    width: 0.39rem;
                                    height: 1.39rem;
                                    border-radius: 0.17rem;
                                }

                                &::before {
                                    background: linear-gradient(180deg, #76dfff 0%, #10a2ff 100%);
                                    z-index: 2;
                                }

                                &::after {
                                    background: linear-gradient(180deg, #76dfff 0%, #10a2ff 100%);
                                    opacity: 0.7;
                                    left: 0.5rem;
                                    z-index: 1;
                                }
                            }
                            span {
                                font-family: HONORSansCN, HONORSansCN;
                                font-weight: 800;
                                font-size: 1.67rem;
                                color: #ffffff;
                                line-height: 2.17rem;
                                position: relative;

                                &::before {
                                    content: '';
                                    position: absolute;
                                    left: -1.39rem;
                                    bottom: 0.28rem;
                                    transform: skewX(-20deg);
                                    width: 0.56rem;
                                    height: 1.17rem;
                                    background: linear-gradient(180deg, #76dfff 0%, #10a2ff 100%);
                                    filter: blur(0.39rem);
                                }
                            }
                        }
                        &-rank {
                            width: 100%;
                            height: 0;
                            flex: 1;
                            padding: 1.33rem 2rem;
                            display: flex;
                            flex-direction: column;
                            align-items: flex-start;
                            justify-content: space-between;
                            background-image: url('../../../../img/homePage/card-bg.png');
                            background-size: 100% 100%;
                            background-repeat: no-repeat;
                            background-position: center center;
                            .rank-list {
                                display: flex;
                                align-items: center;
                                gap: 0.44rem;
                                width: 100%;
                                .rank-index {
                                    width: 1.78rem;
                                    height: 1.78rem;
                                    border-radius: 0.17rem;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    font-family: HONORSansCN, HONORSansCN;
                                    font-weight: 800;
                                    font-size: 1.11rem;
                                    color: #ffffff;
                                    line-height: 1.44rem;
                                }
                                .yellow-index {
                                    background: rgba(212, 197, 0, 0.4);
                                    box-shadow: inset 0 0 0.33rem 0 #f8e71c;
                                    border: 0.06rem solid #f8e71c;
                                }
                                .blue-index {
                                    background: rgba(0, 155, 212, 0.4);
                                    box-shadow: inset 0 0 0.33rem 0 #009bd4;
                                    border: 0.06rem solid #009bd4;
                                }
                                .rank-item {
                                    height: 100%;
                                    width: 0;
                                    flex: 1;
                                    padding-left: 1.33rem;
                                    font-family: MicrosoftYaHei;
                                    font-size: 1rem;
                                    color: #ffffff;
                                    line-height: 1rem;
                                    letter-spacing: 0.06rem;
                                    display: flex;
                                    align-items: center;
                                    position: relative;
                                    &::before {
                                        content: '';
                                        position: absolute;
                                        left: 0;
                                        bottom: 0;
                                        width: calc(100% - 0.44rem);
                                        height: 0.06rem;
                                        background: rgba(255, 255, 255, 0.15);
                                    }
                                    &::after {
                                        content: '';
                                        position: absolute;
                                        right: 0;
                                        bottom: 0;
                                        width: 0;
                                        height: 0;
                                        border-right: 0.33rem solid #01e6fe;
                                        border-top: 0.33rem solid transparent;
                                    }
                                }
                            }
                        }
                    }
                }
                .excellent-ability-list {
                    height: 25rem;
                    position: relative;

                    .ability-carousel {
                        width: 100%;
                        height: 100%;

                        /deep/ .el-carousel__container {
                            height: 100%;
                        }

                        /deep/ .el-carousel__item {
                            height: 100%;
                        }

                        .ability-carousel-content {
                            width: 100%;
                            height: 100%;
                            display: grid;
                            padding: 0 2rem;
                            gap: 2rem;
                            grid-template-columns: repeat(3, 1fr);

                            .ability-item {
                                width: 100%;
                                height: 100%;

                                .ability-card {
                                    position: relative;
                                    width: 100%;
                                    height: 100%;
                                    padding: 2.56rem 1.72rem 1.44rem;
                                    background-image: url('../../../../img/homePage/card-bg.png');
                                    background-size: 100% 100%;
                                    background-repeat: no-repeat;
                                    background-position: center center;

                                    .ability-header {
                                        width: 100%;
                                        height: 1.72rem;
                                        gap: 0.67rem;

                                        .ability-icon {
                                            box-shadow: 0 0 0.22rem 0 rgba(70, 161, 235, 0.71);
                                            background-color: rgba(42, 184, 255, 1);
                                            border-radius: 0.11rem;
                                            width: 0.33rem;
                                            height: 1.11rem;
                                            margin-top: 0.33rem;
                                        }

                                        .ability-title {
                                            height: 1.72rem;
                                            font-family: HONORSansCN, HONORSansCN;
                                            font-weight: 800;
                                            font-size: 1.33rem;
                                            color: #ffffff;
                                            line-height: 1.72rem;
                                        }
                                    }

                                    .ability-desc {
                                        margin-top: 1.11rem;
                                        width: 100%;
                                        height: 14.17rem;
                                        text-indent: 2em;
                                        font-family: PingFangSC, PingFang SC;
                                        font-weight: 600;
                                        font-size: 1rem;
                                        color: #d1e4ff;
                                        line-height: 2.22rem;
                                        text-align: justify;
                                        overflow-y: auto;
                                        &::-webkit-scrollbar {
                                            width: 0.22rem;
                                            height: 0.33rem;
                                        }
                                        &::-webkit-scrollbar-thumb {
                                            border-radius: 0.56rem;
                                            background: rgba(70, 161, 235, 0.71);
                                        }
                                        &::-webkit-scrollbar-track {
                                            /* 滚动条里面轨道 */
                                            border-radius: 0.56rem;
                                            background: transparent;
                                        }
                                        &::-webkit-scrollbar-corner {
                                            background: transparent;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                .application-case-list {
                    height: 33.33rem;
                    position: relative;

                    .application-carousel {
                        width: 100%;
                        height: 100%;

                        /deep/ .el-carousel__container {
                            height: 100%;
                        }

                        /deep/ .el-carousel__item {
                            height: 100%;
                        }

                        .application-carousel-content {
                            width: 100%;
                            height: 100%;
                            display: flex;
                            gap: 2rem;
                            padding: 0 2rem;
                            .application-left {
                                width: 45%;
                                height: 100%;
                                display: flex;
                                flex-direction: column;
                                &-title {
                                    font-family: HONORSansCN, HONORSansCN;
                                    font-weight: 800;
                                    font-size: 1.67rem;
                                    color: #ffffff;
                                    line-height: 2.17rem;
                                    margin-bottom: 2.33rem;
                                }
                                &-relation {
                                    display: flex;
                                    flex-direction: column;
                                    gap: 1.33rem;
                                    margin-bottom: 1.33rem;
                                    &-item {
                                        display: flex;
                                        align-items: center;
                                        justify-content: space-between;
                                        gap: 0.67rem;
                                        .item-left {
                                            width: 15.33rem;
                                            font-family: HONORSansCN, HONORSansCN;
                                            font-weight: 600;
                                            font-size: 0.94rem;
                                            color: #ffffff;
                                            line-height: 1.28rem;
                                            background-image: url('../../../../img/homePage/card-bg-yellow.png');
                                            background-size: 100% 100%;
                                            background-repeat: no-repeat;
                                            padding: 0.56rem 0.33rem;
                                        }
                                        .item-flowicon {
                                            width: 0.78rem;
                                            height: 1.56rem;
                                        }
                                        .item-right {
                                            width: 0;
                                            flex: 1;
                                            font-family: HONORSansCN, HONORSansCN;
                                            font-weight: 600;
                                            font-size: 0.89rem;
                                            color: #ffffff;
                                            line-height: 1.17rem;
                                            background-image: url('../../../../img/homePage/card-bg-bule.png');
                                            background-size: 100% 100%;
                                            background-repeat: no-repeat;
                                            padding: 0.56rem 0.33rem;
                                        }
                                    }
                                }
                                &-desc {
                                    height: 0;
                                    flex: 1;
                                    display: flex;
                                    flex-direction: column;
                                    &-title {
                                        font-family: HONORSansCN, HONORSansCN;
                                        font-weight: 800;
                                        font-size: 1.33rem;
                                        color: #ffffff;
                                        line-height: 1.72rem;
                                        margin-bottom: 0.56rem;
                                    }
                                    &-content {
                                        font-family: HONORSansCN, HONORSansCN;
                                        font-weight: 600;
                                        font-size: 1rem;
                                        color: #d1e4ff;
                                        line-height: 2.22rem;
                                        text-indent: 2em;
                                        overflow-y: auto;
                                        &::-webkit-scrollbar {
                                            width: 0.22rem;
                                            height: 0.33rem;
                                        }
                                        &::-webkit-scrollbar-thumb {
                                            border-radius: 0.56rem;
                                            background: rgba(70, 161, 235, 0.71);
                                        }
                                        &::-webkit-scrollbar-track {
                                            /* 滚动条里面轨道 */
                                            border-radius: 0.56rem;
                                            background: transparent;
                                        }
                                        &::-webkit-scrollbar-corner {
                                            background: transparent;
                                        }
                                    }
                                }
                            }
                            .application-right {
                                width: 55%;
                                height: 100%;
                                &-card {
                                    width: 100%;
                                    height: 100%;
                                    padding: 1.67rem;
                                    background: rgba(0, 80, 160, 0.3);
                                    &-img {
                                        width: 100%;
                                        height: 100%;
                                    }
                                }
                            }
                        }
                    }
                }

                .carousel-btn {
                    position: absolute;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 3.33rem;
                    height: 3.33rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    z-index: 2;

                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: contain;
                        background-color: rgba(13, 45, 93, 0.9);
                        border-radius: 50%;
                    }
                    &-left {
                        left: -1.83rem;
                    }
                    &-right {
                        right: -1.83rem;
                    }
                    &-disabled {
                        opacity: 0.4;
                        cursor: not-allowed;
                    }
                }
            }
        }
        .bottom-bg {
            position: relative;
            &::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                width: 100%;
                background-image: url('../../../../img/homePage/home-bg-bottom.png');
                background-size: cover;
                background-repeat: no-repeat;
                background-position: bottom center;
                clip-path: inset(60% 0 0 0); /* 保证背景过渡自然 */
                padding-bottom: 73.4%; /* 保持宽高比 (1409*60%)/1920=0.42 */
                height: 0;
                z-index: 0;
            }
        }
    }
}

h3 {
    margin-top: 1.11rem;
}

.flex-col {
    display: flex;
    flex-direction: column;
}

.flex-row {
    display: flex;
    flex-direction: row;
}

.justify-between {
    justify-content: space-between;
}
</style>
