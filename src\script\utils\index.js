let nowMessage = null;
//单次弹窗  vm:vue实例
export function messageOnly(vm, message, type = 'info') {
    if (nowMessage) {
        try {
            nowMessage.close();
        } catch (e) {
            console.log('e: ', e);
        }
    }
    nowMessage = vm.$message[type](message);
}

// 判断数据类型
export function getDataType(data) {
    const str = Object.prototype.toString.call(data);
    return str.slice(8, str.length - 1);
}
export function isObj(data) {
    return getDataType(data) === 'Object';
}
export function isArr(data) {
    return getDataType(data) === 'Array';
}
export function isStr(data) {
    return getDataType(data) === 'String';
}
export function isNum(data) {
    return getDataType(data) === 'Number';
}
export function isBoo(data) {
    return getDataType(data) === 'Boolean';
}
export function isFun(data) {
    return getDataType(data) === 'Function';
}
export function isNull(data) {
    return getDataType(data) === 'Null';
}
// 深拷贝
export function deepCopy(data) {
    if (!isObj(data) && !isArr(data)) {
        return data;
    }
    let newData = isObj(data) ? {} : [];
    function doCopy(cData, cNewData) {
        for (let key in cData) {
            if (isObj(cData[key])) {
                cNewData[key] = {};
                doCopy(cData[key], cNewData[key]);
            } else if (isArr(cData[key])) {
                cNewData[key] = [];
                doCopy(cData[key], cNewData[key]);
            } else {
                cNewData[key] = cData[key];
            }
        }
    }
    doCopy(data, newData);
    return newData;
}

// 筛选 data 返回新数据 数据(最好已拷贝过的原数据),filterOps {key:[checkKey]} 筛选设置,needCopy 进行深拷贝
export function filterData(data, filterOps, needCopy = false) {
    let copyData,
        newData = [];
    if (needCopy) {
        copyData = deepCopy(data);
    } else {
        copyData = data;
    }
    copyData.forEach((item) => {
        let flag = true;
        for (let key in filterOps) {
            if (filterOps[key].length && !filterOps[key].includes(item[key])) {
                flag = false;
                break;
            }
        }
        if (flag) {
            newData.push(item);
        }
    });
    return newData;
}
// 排序 data 改变原数据  default aec  dec
export function sortData(data, key, type = '') {
    if (!type || type === 'default') {
        return void 0;
    } else if (type === 'asc') {
        data.sort(function (a, b) {
            return (a[key] + '').localeCompare(b[key] + '');
        });
    } else if (type === 'dec') {
        data.sort(function (a, b) {
            return (b[key] + '').localeCompare(a[key] + '');
        });
    }
}

//  time 小时
export function getDate(time = 0, format = 'yyyy-MM-dd HH:mm:ss') {
    const date = new Date();
    const otherDate = date.getTime() + 60 * 60 * 1000 * time;
    return [date.format(format), new Date(otherDate).format(format)];
}

// 数值取整 位数
export function intNum(num, place = 1) {
    let newNum = 0;
    if (typeof num !== 'number') {
        newNum = Number(num);
    } else {
        newNum = num;
    }
    if (isNaN(newNum)) {
        newNum = 0;
    }
    newNum = (newNum / place).toFixed(0) * place;
    return newNum;
}

function tranMore(data, condition, inner = true) {
    let tranStr = '';
    if (inner) {
        data.forEach((item, index) => {
            let itemStr = '';
            switch (index) {
                case 0:
                    itemStr = `${item}`;
                    break;
                case data.length - 1:
                    itemStr = ` ${condition} ${item}`;
                    break;
                default:
                    itemStr = ` ${condition} ${item} `;
                    break;
            }
            tranStr = tranStr + itemStr;
        });
    } else {
        data.forEach((item, index) => {
            let itemStr = '';
            switch (index) {
                case 0:
                    itemStr = `(${item})`;
                    break;
                case data.length - 1:
                    itemStr = ` ${condition} (${item})`;
                    break;
                default:
                    itemStr = ` ${condition} (${item}) `;
                    break;
            }
            tranStr = tranStr + itemStr;
        });
    }
    return tranStr;
}
function tranChild(child) {
    let childStr = '';
    let type = child.type;
    if (child.formula) {
        type = child.formula;
    }
    if (child.relation === ':') {
        childStr = `${type}=$$${child.value}:${child.value1}$$`;
    } else if (child.relation === '>') {
        childStr = `${type}=$$${child.value}:${child.max}$$`;
    } else if (child.relation === '<') {
        childStr = `${type}=$$${child.min}:${child.value}$$`;
    } else {
        // 多选情况
        let valueListStr = '';
        let len = child.value.length - 1;
        child.value.forEach((item, index) => {
            let str = '';
            if (index < len) {
                str = `${type}${child.relation}$$${item}$$ OR `;
            } else {
                str = `${type}${child.relation}$$${item}$$ `;
            }
            valueListStr = valueListStr + str;
        });
        if (child.value.length > 1) {
            valueListStr = '(' + valueListStr + ')';
        }
        childStr = valueListStr;
    }

    return childStr;
}

// 页面显示 字段
function tranShowChild(child) {
    let childStr = '';
    if (child.relation === ':') {
        childStr = `(${child.type} > ${child.value} AND ${child.type} < ${child.value1})`;
    } else if (child.relation === '>') {
        childStr = `${child.type} > ${child.value}`;
    } else if (child.relation === '<') {
        childStr = `${child.type} < ${child.value}`;
    } else {
        childStr = `${child.type} ${child.relation} "${child.valueLabel}"`;
    }
    return childStr;
}

// sql str 显示
export function toShowSqlJSON(data, conditionOut) {
    const newData = deepCopy(data);
    let str = '';
    let list = [];
    newData.forEach((item, index) => {
        let childList = [];
        item.list.forEach((child, index) => {
            childList.push(tranShowChild(child));
        });
        let itemStr = tranMore(childList, item.condition);
        list.push(itemStr);
    });
    str = tranMore(list, conditionOut, false);
    return str;
}

// sql str 底层
export function toSqlJSON(data, conditionOut) {
    const newData = deepCopy(data);
    let str = '';
    let list = [];
    newData.forEach((item, index) => {
        let childList = [];
        item.list.forEach((child, index) => {
            childList.push(tranChild(child));
        });
        let itemStr = tranMore(childList, item.condition);
        list.push(itemStr);
    });
    str = tranMore(list, conditionOut, false);
    return str;
}

// 下载文件
export function handleDownloadFile(res, callback) {
    if (res.data.type === 'application/json') {
        const errBlob = new Blob([res.data], {
            type: 'application/json',
        });
        let reader = new FileReader();
        reader.readAsText(errBlob, 'utf-8');
        reader.addEventListener('loadend', function () {
            //
            let text = JSON.parse(reader.result);
            callback(text.returnMsg || '下载失败');
        });
        return;
    }
    const blob = new Blob([res.data], {
        type: 'application/vnd.ms-excel',
    });
    const fileName = decodeURI(res.headers['content-disposition'].split('filename=')[1]);
    const linkNode = document.createElement('a');
    linkNode.download = fileName;
    linkNode.style.display = 'none';
    linkNode.href = URL.createObjectURL(blob);
    document.body.appendChild(linkNode);
    linkNode.click();
    URL.revokeObjectURL(linkNode.href);
    document.body.removeChild(linkNode);
}

// 1024转换
export function binaryTran(bytes) {
    if (bytes >= 1125899906842624) {
        // EiB
        bytes = (bytes / 1073741824).toFixed(2) + ' EiB';
    } else if (bytes >= 1073741824) {
        // PiB
        bytes = (bytes / 1073741824).toFixed(2) + ' PiB';
    } else if (bytes >= 1048576) {
        // TiB
        bytes = (bytes / 1048576).toFixed(2) + ' TiB';
    } else if (bytes >= 1024) {
        // GiB
        bytes = (bytes / 1024).toFixed(2) + ' GiB';
    } else {
        // MiB
        bytes = bytes + ' MiB';
    }
    return bytes;
}

// 数字千分位处理
export function formatNum(value) {
    if (!value && value !== 0) return 0;
    let str = value.toString();
    let reg = str.indexOf('.') > -1 ? /(\d)(?=(\d{3})+\.)/g : /(\d)(?=(?:\d{3})+$)/g;
    return str.replace(reg, '$1,');
}

// 数字千分位处理 2

export function parseNumber(num) {
    try {
        let newNum = parseInt(num);
        return newNum.toLocaleString();
    } catch (error) {
        return 0;
    }
}

export function setLengendLength(chartData) {
    let str = '';
    for (let i in chartData.lineData) {
        const name = chartData.lineData[i].name;
        str += name + 'O' + (chartData.legend[name] || '');
    }
    str += ' | ';
    return (str.length * 12) / 2;
}

function handleDeepObj(keyList, obj) {
    const newObj = obj[keyList[0]];
    if (keyList.length === 1) {
        return newObj;
    } else if (!newObj) {
        return undefined;
    }
    const newKeyList = keyList;
    newKeyList.splice(0, 1);
    return handleDeepObj(newKeyList, newObj);
}
/**
 *
 *
 * @export
 * @param {*} key
 * @param {*} obj
 */
export function getDeepValue(key, obj) {
    const keyList = key.split('.');

    return handleDeepObj(keyList, obj);
}
