:root {
    --normal-bg-border: #061c3d;
    --normal-bg: #002a5c66;
    --section-card-bg: #00326666;
    --section-card-border: #409bff4d;
    --dark-bg: #002a5c;
    --light-blue: #00ceff;
    --primary-blue: #0095ff;
    --dark-blue: #093e79;
    --light-text: #ffffff73;
    --secondary-text: #c9dfff;
    --white-text: #fff;
    --border-blue: #128bcf99;
    --button-bg: #00326bcc;
    --button-border: #0095ff99;
    --button-hover: #00326b33;
    --success-color: #36da54;
    --warning-color: #ff3030;
}

.mtex-shandong-devops-dark-theme {
    // table-dark相关dark
    .table-dark {
        width: 100%;
        height: 100%;
        color: #c9dfff;
        background-color: transparent !important;
        // 表格底部横线颜色
        &::before {
            background-color: var(--section-card-border) !important;
        }
        // 表头字体颜色、背景、底部横线
        ::v-deep th {
            color: #c9dfff;
            background: var(--dark-blue);
            border: none !important;
            box-shadow: inset 0px -1px 0px 0px var(--section-card-border);
        }
        // 表行单元格底部横线颜色
        ::v-deep td {
            border: none !important;
            box-shadow: inset 0px -1px 0px 0px var(--section-card-border);
        }
        // 表行背景、hover颜色
        ::v-deep tr {
            background-color: transparent;
            &:hover {
                background-color: var(--primary-blue) !important;
            }
            &.current-row {
                background-color: var(--primary-blue) !important;
            }
        }
        // 表行单元格(区分斑马纹)
        ::v-deep .el-table__row td {
            background-color: transparent !important;
        }
        ::v-deep .el-table__row--striped td {
            background-color: rgba(9, 62, 121, 0.3) !important;
        }
    }

    // 表格滚动条
    .el-table__body-wrapper {
        &::-webkit-scrollbar {
            width: 8px;
            height: 6px;
        }
        &::-webkit-scrollbar-thumb {
            border-radius: 10px;
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
            background: #5c6f92;
        }
        &::-webkit-scrollbar-track {
            /* 滚动条里面轨道 */
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            background: transparent;
        }
        &::-webkit-scrollbar-corner {
            background: rgba(0, 0, 0, 0);
        }
    }

    // 分页
    .pagination-dark {
        display: flex;
        justify-content: flex-start;
        padding: 2px 15px;
        .el-pagination {
            &__total {
                color: var(--light-blue);
                margin-left: auto;
            }
            &__sizes {
                color: var(--light-blue);
            }
            &__jump {
                color: var(--light-blue);
            }
        }
        .el-pager li {
            color: var(--light-blue) !important;
            background-color: transparent !important;
            &.active {
                box-shadow: inset 0px 0px 18px 0px rgba(18, 174, 237, 0.8);
                border-radius: 2px;
                border: 1px solid #009eff;
                color: var(--white-text) !important;
            }
            &:hover {
                color: var(--white-text) !important;
            }
        }
        .el-input__inner {
            color: var(--light-blue) !important;
        }
        button {
            color: var(--light-blue) !important;
            background-color: transparent !important;
            &:disabled {
                color: #c0c4cc !important;
            }
            &.btn-next {
                &:hover {
                    color: #fff !important;
                }
            }
        }
    }

    .el-radio-group {
        .el-radio-button {
            &__inner {
                color: var(--white-text) !important;
                background-color: transparent !important;
                border: 1px solid var(--border-blue) !important;
                border-radius: 0 !important;

                &:hover {
                    color: var(--primary-blue) !important;
                }
            }
            &.is-active {
                .el-radio-button__inner {
                    background: var(--primary-blue) !important;
                    &:hover {
                        color: var(--white-text) !important;
                    }
                }
            }
            &:first-child {
                .el-radio-button__inner {
                    border-top-left-radius: 2px !important;
                    border-bottom-left-radius: 2px !important;
                }
            }
            &:last-child {
                .el-radio-button__inner {
                    border-top-right-radius: 2px !important;
                    border-bottom-right-radius: 2px !important;
                }
            }
        }
    }

    .el-range-input {
        background-color: transparent !important;
        color: var(--white-text) !important;
        &::placeholder {
            color: rgba(255, 255, 255, 0.45);
        }
    }

    // 暗色时滚动条调成透明
    .el-scrollbar__wrap {
        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        &::-webkit-scrollbar-track {
            box-shadow: inset 0 0 5px transparent;
            border-radius: 10px;
            background: transparent;
        }
    }

    .el-checkbox {
        &__inner {
            background: transparent !important;
            border: 1px solid #fff6 !important;
        }
        &__input.is-checked .el-checkbox__inner {
            background-color: var(--primary-blue) !important;
        }
    }
    .el-input__count {
        background-color: transparent !important;
        .el-input__count-inner {
            background-color: transparent !important;
        }
    }
    .el-input__inner {
        background-color: transparent !important;
        border: 1px solid var(--border-blue) !important;
        color: var(--white-text) !important;
        &::placeholder {
            color: rgba(255, 255, 255, 0.45);
        }
        .el-range-separator {
            color: var(--white-text) !important;
        }
    }
    .el-textarea__inner {
        background-color: transparent !important;
        border: 1px solid var(--border-blue) !important;
        color: var(--white-text) !important;
        &::placeholder {
            color: rgba(255, 255, 255, 0.45);
        }
    }

    .el-radio-group {
        .el-radio-button {
            &__inner {
                min-width: 68px;
                color: var(--white-text) !important;
                background-color: transparent !important;
                border: 1px solid var(--border-blue) !important;
                border-radius: 0 !important;

                &:hover {
                    color: var(--primary-blue) !important;
                }
            }
            &.is-active {
                .el-radio-button__inner {
                    background: var(--primary-blue) !important;
                    &:hover {
                        color: var(--white-text) !important;
                    }
                }
            }
            &:first-child {
                .el-radio-button__inner {
                    border-top-left-radius: 2px !important;
                    border-bottom-left-radius: 2px !important;
                }
            }
            &:last-child {
                .el-radio-button__inner {
                    border-top-right-radius: 2px !important;
                    border-bottom-right-radius: 2px !important;
                }
            }
        }
    }

    .el-range-input {
        background-color: transparent !important;
        color: var(--white-text) !important;
        &::placeholder {
            color: rgba(255, 255, 255, 0.45);
        }
    }

    // 按钮样式
    .reset-btn {
        background-color: var(--button-bg);
        border: 1px solid var(--button-border);
        color: var(--white-text);
        &:hover {
            background-color: var(--button-hover);
        }
        &:focus {
            background-color: var(--button-bg);
            border: 1px solid var(--button-border);
            color: var(--white-text);
        }
    }
    .stop-btn {
        background-color: var(--button-bg);
        border: 1px solid var(--button-border);
        color: #01e6fe;
        &:hover {
            background-color: var(--button-hover);
        }
    }

    // 状态指示点和颜色
    .round-point {
        display: inline-block;
        width: 6px;
        height: 6px;
        vertical-align: middle;
        border-radius: 3px;
        margin-right: 2px;
    }
    .success {
        color: var(--success-color);
        i {
            background: var(--success-color);
        }
    }
    .normal {
        color: var(--light-blue);
        i {
            background: var(--light-blue);
        }
    }
    .warning {
        color: var(--warning-color);
        i {
            background: var(--warning-color);
        }
    }
}

// 这些元素存在body中
.date-picker-dark {
    color: var(--white-text) !important;
    background: var(--dark-bg) !important;
    border: 1px solid #ebeef522 !important;
    .el-button {
        &:not(.el-button--text) {
            background-color: var(--button-bg);
            border: 1px solid var(--button-border);
            color: var(--white-text);
        }
        &--text {
            display: none !important;
        }
        &.is-disabled.is-plain {
            background-color: transparent;
        }
        &:hover {
            background-color: var(--button-hover) !important;
        }
    }
    .el-input__inner {
        background-color: transparent !important;
        border: 1px solid var(--border-blue) !important;
        color: var(--white-text) !important;
        &::placeholder {
            color: rgba(255, 255, 255, 0.45);
        }
        .el-range-separator {
            color: var(--white-text) !important;
        }
    }
    .el-picker-panel {
        color: #fff;
        &__shortcut {
            color: var(--white-text);
            &:hover {
                color: var(--light-blue);
            }
        }
        &__icon-btn {
            color: var(--white-text);
        }
        &__sidebar {
            background-color: transparent;
            border-right: 1px solid var(--section-card-border);
        }
        &__footer {
            background-color: transparent;
            border-top: 1px solid var(--section-card-border);
        }
    }
    .el-date-range-picker {
        &__time-header {
            border-bottom: 1px solid var(--section-card-border);
        }
        &__content.is-left {
            border-right: 1px solid var(--section-card-border);
        }
    }
    .el-date-table {
        th {
            border-bottom: 1px solid var(--section-card-border);
        }
        td {
            &.in-range div {
                background: var(--dark-blue);
                &:hover {
                    background: var(--section-card-border);
                }
            }
            &.disabled div {
                background-color: var(--dark-blue);
                opacity: 0.5;
            }
        }
    }
    .el-time-panel {
        background: var(--dark-bg) !important;
        border: 1px solid #ebeef522 !important;
        &[x-placement^='top'] .popper__arrow {
            border-top-color: #ebeef522 !important;
            &::after {
                border-top-color: var(--dark-bg) !important;
            }
        }
        &[x-placement^='bottom'] .popper__arrow {
            border-bottom-color: #ebeef522 !important;
            &::after {
                border-bottom-color: var(--dark-bg) !important;
            }
        }
        &[x-placement^='left'] .popper__arrow {
            border-left-color: #ebeef522 !important;
            &::after {
                border-left-color: var(--dark-bg) !important;
            }
        }
        &[x-placement^='right'] .popper__arrow {
            border-right-color: #ebeef522 !important;
            &::after {
                border-right-color: var(--dark-bg) !important;
            }
        }
        &__content {
            &::before {
                border-top: 1px solid var(--border-blue);
                border-bottom: 1px solid var(--border-blue);
            }
            &::after {
                border-top: 1px solid var(--border-blue);
                border-bottom: 1px solid var(--border-blue);
            }
        }
        &__footer {
            border-top: 1px solid var(--border-blue);
        }
        &__btn {
            color: var(--white-text);
        }
        .el-time-spinner__item {
            color: var(--white-text);
            &.active {
                color: var(--light-blue);
            }
            &:hover {
                background-color: var(--button-hover) !important;
            }
        }
        .el-scrollbar__wrap {
            overflow-x: hidden; //将横向滚动条隐藏
        }
    }
    .el-date-picker__header-label {
        color: #fff;
    }
    .el-year-table td .cell {
        color: #fff;
    }
    .el-month-table td .cell {
        color: #fff;
    }
    .el-date-table th {
        color: #999;
        text-align: center;
    }
    .time-select-item {
        &.selected:not(.disabled) {
            color: var(--light-blue) !important;
        }
        &:hover {
            background: var(--section-card-border) !important;
            font-weight: normal !important;
        }
    }
    // 暗色时滚动条调成透明
    .el-scrollbar__wrap {
        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        &::-webkit-scrollbar-track {
            box-shadow: inset 0 0 5px transparent;
            border-radius: 10px;
            background: transparent;
        }
    }
}
.popover-dark {
    background: var(--dark-bg) !important;
    border: 1px solid #ebeef522 !important;
    &[x-placement^='top'] .popper__arrow {
        border-top-color: #ebeef522 !important;
        &::after {
            border-top-color: var(--dark-bg) !important;
        }
    }
    &[x-placement^='bottom'] .popper__arrow {
        border-bottom-color: #ebeef522 !important;
        &::after {
            border-bottom-color: var(--dark-bg) !important;
        }
    }
    &[x-placement^='left'] .popper__arrow {
        border-left-color: #ebeef522 !important;
        &::after {
            border-left-color: var(--dark-bg) !important;
        }
    }
    &[x-placement^='right'] .popper__arrow {
        border-right-color: #ebeef522 !important;
        &::after {
            border-right-color: var(--dark-bg) !important;
        }
    }
    .el-checkbox {
        &__inner {
            background: transparent !important;
            border: 1px solid #fff6 !important;
        }
        &__input.is-checked .el-checkbox__inner {
            background-color: var(--primary-blue) !important;
        }
    }
    .el-input__inner {
        background-color: transparent !important;
        border: 1px solid var(--border-blue) !important;
        color: var(--white-text) !important;
        &::placeholder {
            color: rgba(255, 255, 255, 0.45);
        }
        .el-range-separator {
            color: var(--white-text) !important;
        }
    }
    // 暗色时滚动条调成透明
    .el-scrollbar__wrap {
        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        &::-webkit-scrollbar-track {
            box-shadow: inset 0 0 5px transparent;
            border-radius: 10px;
            background: transparent;
        }
    }
}
.tooltip-dark {
    background: var(--dark-bg) !important;
    border: 1px solid #ebeef522 !important;
    &[x-placement^='top'] .popper__arrow {
        border-top-color: #ebeef522 !important;
        &::after {
            border-top-color: var(--dark-bg) !important;
        }
    }
    &[x-placement^='bottom'] .popper__arrow {
        border-bottom-color: #ebeef522 !important;
        &::after {
            border-bottom-color: var(--dark-bg) !important;
        }
    }
    &[x-placement^='left'] .popper__arrow {
        border-left-color: #ebeef522 !important;
        &::after {
            border-left-color: var(--dark-bg) !important;
        }
    }
    &[x-placement^='right'] .popper__arrow {
        border-right-color: #ebeef522 !important;
        &::after {
            border-right-color: var(--dark-bg) !important;
        }
    }
    &.popper__arrow {
        display: none !important;
    }
}
.pagination-size-dark {
    .el-select-dropdown__item {
        color: var(--white-text) !important;
        &.selected:not(.is-disabled) {
            color: var(--light-blue) !important;
        }
        &.hover {
            background-color: var(--normal-bg) !important;
        }
        &:hover {
            background-color: var(--dark-blue) !important;
        }
    }
}
.select-dropdown-dark {
    .el-select-dropdown__item {
        color: var(--white-text) !important;
        &:last-child {
            margin-bottom: 10px;
        }
        &.selected:not(.is-disabled) {
            color: var(--light-blue) !important;
            background-color: var(--dark-blue) !important;
        }
        &.hover {
            background-color: var(--normal-bg) !important;
        }
        &:hover {
            background-color: var(--dark-blue) !important;
        }
    }
}
.search-bar-dark {
    .el-select__tags {
        .el-tag.el-tag--info {
            background-color: var(--dark-blue) !important;
            border-color: var(--dark-blue) !important;
            color: var(--white-text) !important;
        }
    }
}
.cascader-menu-dark {
    .el-cascader-menu {
        color: var(--white-text) !important;
        border-right: solid 1px var(--section-card-border) !important;
        .el-cascader-node {
            &:hover {
                background-color: var(--dark-blue) !important;
            }
            &:focus {
                background-color: transparent;
            }
            &.in-active-path {
                background-color: var(--dark-blue) !important;
            }
        }
    }
}
.dropdown-menu-dark {
    background-color: var(--dark-bg) !important;
    border: 1px solid var(--section-card-border) !important;
    .el-dropdown-menu__item {
        color: var(--white-text) !important;
        &:hover {
            background-color: var(--dark-blue) !important;
        }
        &:focus {
            background-color: transparent !important;
        }
    }
}
.el-table-filter {
    background: var(--dark-bg) !important;
    border: 1px solid #ebeef522 !important;
    .el-checkbox {
        &__input {
            .el-checkbox {
                &__inner {
                    background: transparent !important;
                    border: 1px solid #fff6 !important;
                }
            }
        }
        &__label {
            color: var(--secondary-text) !important;
        }
        &.is-checked {
            .el-checkbox__inner {
                background-color: var(--primary-blue) !important;
            }
            .el-checkbox__label {
                color: var(--primary-blue) !important;
            }
        }
    }
    &__bottom {
        border-top: 1px solid var(--section-card-border) !important;
        button {
            color: #c0c4cc !important;
            &:hover {
                color: #409eff !important;
            }
            &.is-disabled {
                color: #606266 !important;
            }
        }
    }
    // 暗色时滚动条调成透明
    .el-scrollbar__wrap {
        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        &::-webkit-scrollbar-track {
            box-shadow: inset 0 0 5px transparent;
            border-radius: 10px;
            background: transparent;
        }
    }
}

//
.custom-scrollbar {
    --scrollbar-width: 8px;
    --scrollbar-height: 6px;
    --scrollbar-thumb-color: #5c6f92;
    &::-webkit-scrollbar {
        width: var(--scrollbar-width);
        height: var(--scrollbar-height);
    }
    &::-webkit-scrollbar-thumb {
        border-radius: 10px;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
        background: var(--scrollbar-thumb-color);
    }
    &::-webkit-scrollbar-track {
        /* 滚动条里面轨道 */
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
        border-radius: 10px;
        background: transparent;
    }
    &::-webkit-scrollbar-corner {
        background: rgba(0, 0, 0, 0);
    }
}
.text-ellipsis {
    --line-clamp: 1;
    overflow: hidden;
    display: -webkit-box;
    line-clamp: var(--line-clamp);
    -webkit-line-clamp: var(--line-clamp);
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
}
