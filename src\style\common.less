.sd-devops {
    &-overflow-y--black {
        overflow-y: auto;
        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        &::-webkit-scrollbar-thumb {
            border-radius: 10px;
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
            background: #555555;
        }
        &::-webkit-scrollbar-track {
            /* 滚动条里面轨道 */
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            background: transparent;
        }
        &::-webkit-scrollbar-corner {
            background: rgba(0, 0, 0, 0);
        }
    }
    &-overflow-y--grey {
        overflow-y: auto;
        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        &::-webkit-scrollbar-thumb {
            border-radius: 10px;
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
            background: #5c6f92;
        }
        &::-webkit-scrollbar-track {
            /* 滚动条里面轨道 */
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            background: transparent;
        }
        &::-webkit-scrollbar-corner {
            background: rgba(0, 0, 0, 0);
        }
    }
    &-btn--small {
        width: 4.17rem;
        height: 32px;
        border-radius: 0.11rem;
        &:hover {
            opacity: 0.7;
        }
    }
    &-btn--success {
        &.el-button--success {
            background: #47ce99;
        }
    }
}
// 页面rem缩放问题
html {
    font-size: calc(100vw * 18 / 1920) !important;
}
@font-face {
    font-family: 'YouSheBiaoTiHei';
    src: url('./font/YouSheBiaoTiHei-2.ttf');
    font-weight: normal;
    font-style: normal;
}
