<template>
    <el-menu
        class="recursive-el-menu"
        :default-active="activeIndex"
        background-color="transparent"
        text-color="#ffffff"
        active-text-color="#ffffff"
        :router="false"
        :collapse="false"
        @select="handleSelect"
    >
        <template v-for="item in items">
            <el-submenu v-if="hasChildren(item)" :index="itemKey(item)" :key="itemKey(item)">
                <template slot="title">
                    <span>{{ item.label }}</span>
                </template>
                <menu-recursive-node
                    v-for="child in item.children"
                    :key="itemKey(child)"
                    :item="child"
                />
            </el-submenu>
            <el-menu-item v-else :index="item.value" :key="itemKey(item)">
                {{ item.label }}
            </el-menu-item>
        </template>
    </el-menu>
</template>

<script>
// 递归子节点组件
const MenuRecursiveNode = {
    name: 'MenuRecursiveNode',
    props: {
        item: { type: Object, required: true }
    },
    methods: {
        hasChildren(node) {
            return Array.isArray(node.children) && node.children.length > 0;
        },
        item<PERSON>ey(node) {
            return node.value || node.name;
        }
    },
    render(h) {
        const node = this.item;
        if (this.hasChildren(node)) {
            return h('el-submenu', { attrs: { index: this.itemKey(node) } }, [
                h('span', { slot: 'title' }, node.label),
                node.children.map((child) =>
                    h('menu-recursive-node', { props: { item: child }, key: this.itemKey(child) })
                )
            ]);
        }
        return h('el-menu-item', { attrs: { index: node.value } }, node.label);
    }
};

export default {
    name: 'MenuRecursive',
    props: {
        items: { type: Array, required: true, default: () => [] }
    },
    components: {
        MenuRecursiveNode
    },
    data() {
        return { activeIndex: '' };
    },
    methods: {
        handleSelect(index) {
            this.activeIndex = index;
            this.$emit('command', index);
        },
        hasChildren(node) {
            return Array.isArray(node.children) && node.children.length > 0;
        },
        itemKey(node) {
            return node.value || node.name;
        }
    }
};
</script>

<style lang="less" scoped>
.recursive-el-menu {
    border: none;
    background: transparent;
    min-width: 200px;
    /deep/ .el-menu-item,
    /deep/ .el-submenu__title {
        height: 40px;
        line-height: 40px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        span {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
        }

        &:hover,
        &:focus {
            background-color: #0061b7 !important;
        }
    }
}
</style>
