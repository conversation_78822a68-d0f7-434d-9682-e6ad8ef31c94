<template>
    <el-table
        class="table-dark"
        :data="nowList"
        stripe
        ref="customTable"
        size="small"
        :height="height"
        v-bind="$attrs"
        style="width: 100%"
        @selection-change="handleSelectionChange"
    >
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column prop="taskId" label="任务ID" width="180"> </el-table-column>
        <el-table-column prop="taskName" label="任务名称" width="180" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="statusCn" label="处理状态">
            <template slot="header">
                <filter-com
                    :comName="'处理状态'"
                    :data="[
                        { label: '正常', value: '正常' },
                        { label: '异常', value: '异常' }
                    ]"
                    :ops="{ hasFitler: true, hasSort: false }"
                    :filterType="'statusCn'"
                    ref="statusCn"
                    @fitler-click="filterFun"
                ></filter-com>
            </template>
            <template slot-scope="scope">
                <span :class="[scope.row.statusCn === '正常' ? 'success' : 'warning']">
                    <i class="round-point"></i>
                    {{ scope.row.statusCn }}</span
                >
            </template>
        </el-table-column>
        <el-table-column prop="startTime" label="生效开始时间">
            <template slot="header">
                <filter-com
                    :comName="'生效开始时间'"
                    :data="statusFilter"
                    :filterType="'startTime'"
                    :ops="{ hasFitler: false, hasSort: true }"
                    ref="startTime"
                    @fitler-click="filterFun"
                    @sort-click="sortFun"
                ></filter-com>
            </template>
        </el-table-column>
        <el-table-column prop="endTime" label="生效结束时间">
            <template slot="header">
                <filter-com
                    :comName="'生效结束时间'"
                    :data="statusFilter"
                    :filterType="'endTime'"
                    :ops="{ hasFitler: false, hasSort: true }"
                    ref="endTime"
                    @fitler-click="filterFun"
                    @sort-click="sortFun"
                ></filter-com>
            </template>
        </el-table-column>
        <el-table-column prop="isValidCn" label="生效状态">
            <template slot="header">
                <filter-com
                    :comName="'生效状态'"
                    :data="[
                        { label: '生效', value: '生效' },
                        { label: '失效', value: '失效' }
                    ]"
                    :filterType="'isValidCn'"
                    :ops="{ hasFitler: true, hasSort: false }"
                    ref="isValidCn"
                    @fitler-click="filterFun"
                ></filter-com>
            </template>
            <template slot-scope="scope">
                <span :class="[scope.row.isValidCn === '生效' ? 'normal' : 'warning']">
                    <i class="round-point"></i>
                    {{ scope.row.isValidCn }}</span
                >
            </template>
        </el-table-column>

        <el-table-column prop="comment" label="备注" show-overflow-tooltip> </el-table-column>
        <el-table-column label="操作">
            <template slot-scope="scope">
                <el-button
                    type="text"
                    size="small"
                    style="color: #50f8f2"
                    @click="rowBtnClick('查看', scope.row, scope.$index)"
                    >查看</el-button
                >
                <el-button
                    type="text"
                    size="small"
                    style="color: red"
                    @click="rowBtnClick('失效', scope.row, scope.$index)"
                    >失效</el-button
                >
            </template>
        </el-table-column>
    </el-table>
</template>
<script>
import filterCom from './filterCom.vue';
import { filterData, sortData } from '../utils/index';
export default {
    name: 'customTable',
    components: {
        filterCom
    },
    props: {
        height: { type: String, default: '100%' },
        tableColumnOps: {
            type: Array,
            default: () => [
                { label: '任务ID', name: '任务ID' },
                { label: '任务名称', name: 'taskName' },
                { label: '状态', name: 'statusCn', slot: 'custom' },
                { label: '生效开始时间', name: 'startTime', slot: 'custom' },
                { label: '生效结束时间', name: 'endTime', slot: 'custom' },
                { label: '生效状态', name: 'isValidCn', slot: 'custom' },
                { label: '备注', name: 'comment' },
                {
                    label: '操作',
                    name: 'handler',
                    slot: 'slotBtn',
                    btnList: [{ name: '查看' }, { name: '失效' }]
                }
            ]
        },
        tableData: {
            type: Array,
            default: () => [
                {
                    taskId: '11111',
                    taskName: '11111',
                    statusCn: '11111',
                    startTime: '11111',
                    endTime: '11111',
                    endTime: '11111',
                    isValidCn: '生效',
                    createUser: '11111',
                    mark: '11111'
                }
            ]
        }
    },
    data() {
        return {
            conditionList: ['statusCn', 'startTime', 'endTime', 'isValidCn'],
            statusFilter: [
                { label: '已处理', value: '已处理' },
                { label: '处理中', value: '处理中' },
                { label: '待处理', value: '待处理' }
                // { text: '关闭', value: '关闭' },
            ],
            nowList: JSON.parse(JSON.stringify(this.tableData)),
            listOld: JSON.parse(JSON.stringify(this.tableData)), //查询到的原始数据
            filterOps: {
                //筛选条件 key + 存在的值
                isValidCn: [],
                statusCn: []
            }
        };
    },
    watch: {
        tableData: {
            handler(val) {
                this.nowList = JSON.parse(JSON.stringify(val));
                this.listOld = JSON.parse(JSON.stringify(val));
                this.initFilterOps();
            },
            deep: true
        }
    },
    methods: {
        sortFun(filterType, type) {
            if (type) {
                sortData(this.nowList, filterType, type);
            }
        },
        filterFun(checkeds, filterType) {
            this.filterOps[filterType] = [...checkeds];
            this.nowList = filterData(this.listOld, this.filterOps);
        },
        initFilterOps() {
            for (let key in this.filterOps) {
                this.filterOps[key] = [];
            }
            this.conditionList.forEach((i) => {
                try {
                    this.$refs[i].init();
                } catch (e) {}
            });
        },
        rowBtnClick(name, row, $index) {
            this.$emit('row-click', name, row, $index);
            console.log('name,row,$index: ', name, row, $index);
        },
        handleSelectionChange(val) {
            console.log('val: ', val);
            this.$emit('selection-change', val);
        }
    }
};
</script>
<style lang="less" scoped>
@import url('../../style/custom.less');
</style>
