
<template>
    <div class="road-step">
        <template v-if="data.length">
        <div v-for="(item,index) in data" :key="index" class="step">
            <div class="step-item">
                <template v-if="!item.isRoad">
                    <div class="point-item">
                        <div class="point_info_pointCnt">{{item.pointCnt}}</div>
                        <div class="bar">
                            <span class="bar-title" v-html="item.label"></span>
                            <span @click="openClosePointClick(item)"  
										:style="{'background-image': 'url('+obodeImg+')'}" class="img"></span>
                        </div>
                    </div>
                    <div :class="['point_info_div',{'isHightLight':clickPoint === item.pointCnt}]" 
						v-show="item.showPointInfo"
                        @click="moveToPoint($event,item)"
						>
						<div class="point_info_text">{{getLngLat(item)}}</div>
						<div class="point_info_text">{{getEciInfo(item)}}</div>
					</div>
                </template>
                <template v-else>
                    <div class="point-item">
                        <div class="point_info_cicle"></div>
                        <div class="bar">
                            <span class="bar-title" v-html="item.label"></span>
                            <span @click="openCloseClick($event)" :style="{'background-image': 'url('+roadImg+')'}" class="img"></span>
                        </div>
                    </div>
                    <div class="road-item-div road-open">
						<el-tree highlight-current  :check-on-click-node="true" ref="key" node-key="key"
								:data="item.children" :props="defaultProps" @node-click="roadClick"
								@node-collapse="roadExpand" @node-expand="roadExpand" >
								<span class="custom-tree-node" slot-scope="{ node, data }">
									<span class="is-leaf el-tree-node__expand-icon el-icon-caret-right tree-icon" v-if="data.url"
										:style="{'background-image': 'url('+data.url+')','background-size': '100% 100%'}"></span>
									<span class="el-tree-node__label">{{ node.label }}</span>
								</span>	
							</el-tree>
					</div>
                </template>
            </div>
        </div>
        </template>
        <empty v-else></empty>
    </div>
</template>
<script>
import obodeImg from '../../../../img/icon/point.png';
import roadImg from '../../../../img/icon/road.png';
import empty from '_com/empty/index.vue';
export default {
    name:'roadStep',
    components:{
        empty,
    },
    props:{
        data:{
            type:Array,
            default:() => []
        }
    },
    data(){
        return{
            obodeImg,
            roadImg,
            defaultProps: {
				children: 'children',
				label: 'label'
			},
            clickPoint:null,
        };
    },
    mounted(){
        
    },
    methods:{
        getLngLat(item){
			//如果返回的是0点，就变化避免报错
			let latitude=Number(item.loc.lat)||0.000001;
			let longitude=Number(item.loc.lng)||0.000001;
			return `驻点经纬度：${longitude.toFixed(7)},${latitude.toFixed(7)}`
		},
		getEciInfo(item){
			if(!item.ECI)return 
			// let {eciid=''}=item.eci;
			return `eciID：${item.ECI}`;
		},
        openClosePointClick(item){
			item.showPointInfo=!item.showPointInfo;
		},
        openCloseClick(e) {
			let dom = $(e.target).parent().parent().parent().find('.road-item-div');
			if(dom.hasClass('road-open')) {
				dom.removeClass('road-open');
				dom.addClass('road-close');
			} else {
				dom.removeClass('road-close');
				dom.addClass('road-open');
			}
		},
        roadClick(i , node , dom){
            if(!node.childNodes.length){
                this.$emit('roadClick',i.index);
            }
        },
        /**
		 * 
		 * @method 节点展开 
		 */
		roadExpand(){
			$('.bgGreen').removeClass('bgGreen');
		},
        moveToPoint(e,item){
            this.clickPoint = item.pointCnt;
            this.$emit('moveToPoint',e,item);
        }
    }
};
</script>

<style lang="less" scoped>
.road-step{
    width:100%;
    border-left:1px solid rgba(46, 117, 253, 0.3);
    .step-item{
        transform:translate(-12px,3px);
    }
    .step:first-child{
        transform:translateY(-6px);
    }
    .step+.step{
        padding-top:10px;
    }
    .point-item{
        display:flex;
        align-items:center;
    }
    .point_info_pointCnt{
        border: 2px solid #D4E9FF;
        border-radius: 100px;
        width: 22px;
        height: 22px;
        font-size: 12px;
        display: inline-block;
        text-align: center;
        vertical-align: bottom;
        color: white;
        background: linear-gradient(90deg, #2A93FF 0%, #409EFF 100%);
        box-shadow: 0px 2px 5px 1px rgba(11,94,255,0.3);
        margin-right:10px;
    }
    .bar{
        flex:1;
        background: #F5F6F8;
        border-radius: 2px;
        display:flex;
        justify-content:space-between;
        height: 28px;
        padding:0 10px;
        align-items: center;
    }
    .bar-title{
        font-size: 12px;
        font-weight: 600;
        color: #333333;
    }
    .img{
        width: 14px;
        height: 14px;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        cursor:pointer;
    }
    .point_info_div{
        margin: 10px 0 0 34px;
        cursor: pointer;
        padding: 5px;
        &.isHightLight{
            background:rgba(218, 237, 255, 1);
            border-radius: 2px;
        }
    }
    .point_info_text{
        font-size:12px;
        color: rgba(0,0,0,0.85);
    }
    .point_info_cicle{
        width:15px;
        height:15px;
        border-radius: 100px;
        border: 2px solid #D4E9FF;
        background:#409EFF;
        margin-right:14px;
        margin-left:4px;
    }
    .road-item-div{
        padding: 10px 0  0 34px;
        &.road-open{
           display:block;
        }
        &.road-close{
            display:none;
        }
    }
}
/deep/.el-tree-node__label{
    font-size:12px;
}
</style>