<template>
    <el-form
        ref="searchBarRef"
        class="search-bar"
        :style="cssVars"
        :model="form"
        label-width="76px"
        size="small"
    >
        <el-row v-for="(cols, inx) in formCols" :key="inx" :gutter="10">
            <template
                v-for="(
                    { isShow, prop, label, span, type, isDisabled, rules, labelWidth, attrs, opts },
                    i
                ) in Array.isArray(cols) ? cols : [cols]"
            >
                <el-col v-if="isShow" :key="`${prop}${inx}`" :span="span">
                    <el-form-item
                        :class="{
                            noBackground: type === 'tool',
                            isDisabled: isDisabled,
                            'mb-0': inx === formCols.length - 1 && i === cols.length - 1
                        }"
                        :label="label"
                        :prop="prop"
                        :rules="rules"
                        :label-width="labelWidth"
                    >
                        <template #error="{ error }">
                            <span class="custom-error">
                                {{ error }}
                            </span>
                        </template>
                        <el-select
                            v-if="type === 'select'"
                            v-model="form[prop]"
                            class="w-full"
                            v-bind="attrs"
                        >
                            <el-option v-for="it in opts" v-bind="it" :key="it.value" />
                        </el-select>
                        <template v-else-if="type === 'tool'">
                            <slot name="search"></slot>
                            <slot name="reset"></slot>
                        </template>
                        <component v-else :is="type" v-model="form[prop]" v-bind="attrs" />
                    </el-form-item>
                </el-col>
            </template>
        </el-row>
    </el-form>
</template>

<script>
import datePicker from '_com/datePicker.vue';
export default {
    name: 'searchBar',
    components: {
        datePicker
    },
    props: {
        formCols: {
            type: Array,
            default: () => []
        },
        form: {
            type: Object,
            default: () => ({})
        },
        isTransparentBg: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        cssVars() {
            // 设置form-item的部分样式变量
            if (this.isTransparentBg) {
                return {
                    '--theme-color-white': 'transparent',
                    '--font-color': 'rgba(255, 255, 255, 0.65)',
                    '--font-weight': 400
                };
            }
            return {
                '--theme-color-white': '#fff',
                '--font-color': 'inherit',
                '--font-weight': 700
            };
        }
    },
    methods: {
        async validForm() {
            try {
                return await this.$refs.searchBarRef.validate();
            } catch (err) {
                return err;
            }
        }
    }
};
</script>

<style lang="less" scoped>
.search-bar {
    width: 100%;
    .el-row {
        .el-col:last-of-type .el-form-item {
            margin-right: 0;
        }
    }
    .el-form-item {
        margin-bottom: 0.67rem;
        background-color: var(--theme-color-white);
        .custom-error {
            position: absolute;
            right: 0;
            top: -1.44rem;
            padding: 0 0.33rem;
            height: 1.11rem;
            line-height: 1.11rem;
            border-radius: 0.11rem;
            font-size: 0.67rem;
            color: #fff;
            background-color: #f56c6c;
            z-index: 999;
            &::before {
                content: '';
                position: absolute;
                margin-top: -0.28rem;
                width: 0.56rem;
                height: 0.44rem;
                right: 50%;
                bottom: -0.17rem;
                transform: translateX(50%) rotate(45deg);
                background-color: #f56c6c;
            }
        }
        &.noBackground {
            border: none;
            background-color: transparent;
        }
        /deep/ .el-form-item__label:before {
            margin-right: 0.11rem !important;
        }
        &.isDisabled {
            /deep/ .el-form-item__label {
                background-color: #f5f7fa;
            }
        }
        /deep/ .el-form-item__label {
            position: relative;
            margin-bottom: 0;
            padding-right: 0.56rem;
            color: var(--font-color);
            font-weight: var(--font-weight);
        }
        /deep/ .el-form-item__content {
            position: relative;
        }
    }
}
.mb-0 {
    margin-bottom: 0 !important;
}
.w-full {
    width: 100%;
}
/deep/.el-date-editor.el-input {
    width: 100%;
}
/deep/.el-range-editor.el-input__inner {
    width: 100%;
}
</style>
