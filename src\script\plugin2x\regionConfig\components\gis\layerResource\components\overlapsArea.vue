<template>
  <div class="overlaps-area">
    <el-table
      :data="overlapAreas"
      :max-height="500"
      :header-cell-style="{
        backgroundColor: '#F6F7FA',
        fontWeight: 'bold',
        color: '#4a4a4a',
      }"
      size="small"
    >
      <el-table-column width="150" property="regionId" label="资源ID"></el-table-column>
      <el-table-column width="150" property="regionName" label="资源名称"></el-table-column>
      <el-table-column width="70" property="repetition" label="重叠度">
        <template v-slot="{ row }">
          <span>{{ row.repetition.toFixed(2) }}%</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'overlaps-area',
  components: {},
  props: {
    overlapAreas: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {};
  },
};
</script>

<style lang="less" scoped>
.overlaps-area {
  width: 370px;
}
</style>
