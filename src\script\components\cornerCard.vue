<template>
    <div class="corner-icons-container">
        <div class="icon-top-left"></div>
        <div class="icon-top-right"></div>
        <div class="icon-bottom-left"></div>
        <div class="icon-bottom-right"></div>
        <slot></slot>
    </div>
</template>
<script>
export default {
    name: 'cornerCard',
    data() {
        return {};
    },
    methods: {}
};
</script>
<style lang="less">
.corner-icons-container {
    position: relative;

    // 四个角的图标（使用伪元素）
    .icon-top-left,
    .icon-top-right,
    .icon-bottom-left,
    .icon-bottom-right {
        z-index: inherit;
        position: absolute;
        width: 0.5rem;
        height: 0.5rem;
        background-size: contain;
        background-repeat: no-repeat;
    }

    // 左上角
    .icon-top-left {
        content: '';
        top: 0;
        left: 0;
        background-image: url('../../img/main/left-top.png');
    }

    // 右上角
    .icon-top-right {
        content: '';
        top: 0;
        right: 0;
        background-image: url('../../img/main/right-top.png');
    }

    // 左下角
    .icon-bottom-left {
        bottom: 0;
        left: 0;
        background-image: url('../../img/main/left-bottom.png');
    }

    // 右下角
    .icon-bottom-right {
        bottom: 0;
        right: 0;
        background-image: url('../../img/main/right-bottom.png');
    }
}
</style>
