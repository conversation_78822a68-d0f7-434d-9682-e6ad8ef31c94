<template>
  <h1 class="com-title-devops">
    <div class="title">
        {{ title }}
        <slot name="title-tips"></slot>
        <div class="search-panel">
            <el-select class="mini-select canmove" popper-class="sd-devops-select" v-show="selectList.isShowCity" size="mini" v-model="searchParams.province"
                @click.native="closePanel"
            >
                <el-option
                    v-for="item in citys"
                    :key="item.enumValue"
                    :label="item.enumName"
                    :value="item.enumValue">
                </el-option>
            </el-select>
            <el-select class="longer-select canmove" popper-class="sd-devops-select" v-show="selectList.isShowindex" size="mini" v-model="searchParams.dataType"
                @click.native="closePanel"
            >
                <el-option
                    v-for="item in indexs"
                    :key="item.enumValue"
                    :label="item.enumName"
                    :value="item.enumValue">
                </el-option>
            </el-select>
            <el-select class="mini-select canmove" popper-class="sd-devops-select" size="mini" v-show="selectList.isShowNetType" v-model="searchParams.netType"
                @click.native="closePanel"
            >
                <el-option
                    v-for="item in netTypeList"
                    :key="item.enumValue"
                    :label="item.enumName"
                    :value="item.enumValue">
                </el-option>
            </el-select>
            <time-pick-panel
                ref="panel"
                v-show="selectList.isShowtimePick"
                :isSingleTime="isSingleTime"
                :widgetParams="widgetParams"
                :timeList.sync="timeList"
            ></time-pick-panel>
            <el-select class="long-select canmove" popper-class="sd-devops-select" v-show="selectList.isShowtypes" size="mini" v-model="searchParams.type"
                @click.native="closePanel"
            >
                <el-option
                    v-for="item in types"
                    :key="item.enumValue"
                    :label="item.enumName"
                    :value="item.enumValue">
                </el-option>
            </el-select>
            <el-select class="mini-select canmove" popper-class="sd-devops-select" size="mini" v-show="selectList.isShowGranularity" v-model="searchParams.granularity"
                @click.native="closePanel"
            >
                <el-option
                    v-for="item in granularityList"
                    :key="item.enumValue"
                    :label="item.enumName"
                    :value="item.enumValue">
                </el-option>
            </el-select>
        </div>
    </div>
    <div class="right-solt canmove">
      <slot name="right"></slot>
      <div class="icon" @click.stop="$emit('iconClick')">
          <i class="el-icon-more more"></i>
      </div>
    </div>
  </h1>
</template>
<script>
import timePickPanel from './timePickPanel.vue';
const FIXED_CITY = ['GD','HA','HN','JS','ZJ'];
export default {
  name: 'comTitled',
  components:{
    timePickPanel,
  },
  props: {
    title: {
      type: String,
      default: '',
    },
    uuid:{
        type:String,
        default:'67bfd6-e75d-4bd1-8a9f-1a1922fb5c18a'
    },
    selectList:{
        type:Object,
        default:() => ({
            isShowtimePick:true,
            isShowGranularity:true,
        }),
    },
    isSingleTime:{
        type:Boolean,
        default:false,
    },
    widgetParams:{
        type:Object,
        default:() => ({}),
    }
  },
  provide(){
        return{
            updateMutlSelect:this.updateMutlSelect,
        };
    },
  data() {
    return {
        searchParams:{
            province:'QG', 
            dataType:1,
            type:3,
            startTime:'',
            endTime:'',
            relativeTime:'',
            time:'',
            granularity:'',
            netType:1,
            alarmType:''
        },
        granularity:[
            {
                enumName:'分钟',
                enumValue:1,
                trans:1,//转化
                disabled:true,
            },
            {
                enumName:'5分钟',
                enumValue:2,
                trans:5,
                disabled:false,
            },
            {
                enumName:'小时',
                enumValue:3,
                trans:60,
                disabled:false,
            },
            {
                enumName:'天',
                enumValue:4,
                trans:1440,
                disabled:false,
            },
        ],
        timeList:{
            startTime:'',
            endTime:'',
            relativeTime:'',
            time:'',
        },
    };
  },
  computed:{
        citys(){
            // return this.$store.state.devopsEnumList.provinceList || [];
            return [];
        },
        dataTypeList(){
            //  return this.$store.state.devopsEnumList.dataTypeList || [];
             return [];
        },
        indexs(){
            // if(FIXED_CITY.includes(this.searchParams.province)){
            //     return this.dataTypeList;
            // }
            // const data = JSON.parse(JSON.stringify(this.dataTypeList));
            // return data.filter((item) => !item.enumName.includes('边缘')) || [];
            return [];
        },
        types(){
            // return this.$store.state.devopsEnumList.realOfflineList|| [];
            return [];
        },
        granularityList(){
            let data = JSON.parse(JSON.stringify(this.granularity));
            const minGranularity = this.widgetParams.minGranularity;
            if(minGranularity != null){
                const index  = data.findIndex((item) => item.enumValue === minGranularity);
                data = data.slice(index);
            }
            return data.filter((item) => !item.disabled);
            // return [];
        },
        minLimitation(){
            return this.widgetParams.minLimitation || 1000;
        },
        netTypeList(){
            // return this.$store.state.devopsEnumList.netTypeList|| [];
            return [];
        },
        alarmTypeList(){
            // return this.$store.state.devopsEnumList.alarmType|| [];
            return [];
        },
  },
  mounted(){
    this.handlerSearchParams(this.widgetParams);
  },
  watch:{
    searchParams:{
        handler(newV){
            this.$emit('update:searchParams',newV);
        },
        deep:true,
    },
    widgetParams:{
        handler(newV){
            this.handlerSearchParams(newV);
        },
        deep:true,
    },
    timeList:{
        handler(newV){
            this.handlerSearchParams(newV);
        },
        deep:true,
    },
    indexs:{
        handler(newV){
            if(newV.length){
                const index = newV.findIndex((item) => item.enumValue === this.searchParams.dataType);
                if(index <= -1){
                    this.searchParams.dataType = newV[0].enumValue;
                }
            }
        },
        deep:true,
    },
  },
  methods: {
        handlerTimeList(relativeTime){
            let granularity = this.searchParams.granularity;
            this.granularity.forEach((item) => {
                item.disabled = relativeTime/item.trans > this.minLimitation ? true:false;
                if(this.searchParams.granularity === item.enumValue && item.disabled){
                    granularity = false;
                }
            });
            return granularity;
        },
        closePanel(){
            this.$refs.panel.isShowPanel = false;
            this.$emit('closeShowMore');
        },
        handlerSearchParams(newV){
            if(JSON.stringify(newV) === '{}'){
                return;
            }
            if(this.isSingleTime){
                Object.assign(this.searchParams,newV);
                return;
            }
            const {startTime,endTime,relativeTime,alarmType} = newV;
            let granularity = this.searchParams.granularity;
            if(relativeTime){
                granularity =this.handlerTimeList(relativeTime);
            }else{
                const start = new Date(startTime).getTime();
                const end = new Date(endTime).getTime();
                const relative = (end-start)/60000;
                granularity =this.handlerTimeList(relative);
            }
            if(!granularity){
                granularity = this.granularityList[0].enumValue;
            }
            Object.assign(this.searchParams,newV,{granularity:granularity},{alarmType:alarmType?alarmType:this.alarmTypeList.map((item) => item.kpiId).join(',')});
        },
        updateMutlSelect(val){
            this.searchParams.alarmType = val.map((item) => item.kpiId).join(',');
        },
  },
};
</script>
<style lang="less" scoped>
.com-title-devops {
  font-size: 16px;
  padding: 10px 16px 0 20px;
  width: 100%;
  height: 46px;
  font-weight: 600;
  position: relative;
  margin: 0;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  .title {
    padding-left: 10px;
    position: relative;
    color:#fff;
    font-size: 16px;
    display: flex;
    align-items: center;
    &:before {
      position: absolute;
      top: 5px;
      left: 0px;
      content: '';
      color: #23B4F9;
      background: #23B4F9;
      width: 2px;
      height: 17px;
      z-index: 1;
    }
  }
 /deep/ .canmove{
    cursor: pointer;
  }
}
.search-panel{
    padding-left:15px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    >:not(:last-child){
        margin-right:10px;
    }
}
.icon{
    width: 24px;
    height: 24px;
    line-height: 24px;
    padding-left: 4px;
    background: rgba(255,255,255,0.2);
    border-radius: 12px 12px 12px 12px;
    opacity: 1;
    cursor: pointer;
    .more{
        color:#fff;
    }
}
.mini-select{
    width:70px;
}
/deep/.el-input__suffix {
	right: 0px;
}
.long-select{
    width:140px;
}
.longer-select{
    width:160px;
}
/deep/
	.el-input__inner {
		background: #000000;
        color:#999999;
        border-radius: 2px;
        border: 1px solid #999;
        padding: 0 10px ;
	}
.right-solt{
    flex:1;
    display: flex;
    justify-content: flex-end;
}
/deep/.mult-select-pick{
    background: #000000;
    color: #999999;
    border-radius: 2px;
    border: 1px solid #999;
    padding: 0 10px;
    width:250px;
    height:28px;
    &:hover{
        border-color:#C0C4CC;
    }
    .value{
        font-size: 13px;
        color:#999;
    }
    .arrow{
        top:6px;
    }
}
</style>
<style lang="less">
.sd-devops-select{
    &.el-select-dropdown{
        background: rgba(48,48,48,1);
        box-shadow: inset 0px 0px 18px 0px rgb(2 132 227 / 30%);
        border-radius: 2px;
        border: 1px solid rgba(48,48,48,1);
        z-index:10;
        /deep/.el-popper{
            .popper__arrow::after{
                border-bottom-color: #000000;
            }
        }
    }
    .el-select-dropdown__item{
        color:#999999;
    }
    .el-select-dropdown__item.hover, .el-select-dropdown__item:hover{
        background-color:#404446;
        color:#fff;
    }
    &.el-popper .popper__arrow{
        display: none;
    }
}
</style>
