<template>
    <div class="section-title-two">
        <span class="title">
            {{ title }}
        </span>
        <div class="slot-container">
            <slot></slot>
        </div>
    </div>
</template>
<script>
export default {
    name: 'sectionTitleTwo',
    props: {
        title: {
            type: String,
            default: ''
        }
    },
    data() {
        return {};
    },
    methods: {}
};
</script>
<style lang="less" scoped>
.section-title-two {
    height: 30px;
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 10px;
    background-image: url('../../../img/main/section-title-bg.png');
    background-repeat: no-repeat;
    background-size: 132px 100%;
    .title {
        margin-left: 36px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 14px;
        color: #fff;
        font-style: normal;
        line-height: 18px;
    }
    .slot-container {
        width: 0;
        flex: 1;
        height: 100%;
        display: flex;
    }
}
</style>
