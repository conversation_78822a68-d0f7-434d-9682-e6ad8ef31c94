<template>
    <div class="pie-echart">
        <el-empty
            v-if="JSON.stringify(data) === '{}'"
            description="暂无数据"
        ></el-empty>
        <div v-else :id="name" class="pie-row-echart"></div>
        <!-- <ul class="lengend">
            <li
                class="lengend-item"
                v-for="(item, index) in lengendList"
                :key="index"
            >
                <div class="circle"></div>
                <span class="text border">{{ item.provinceName }}</span>
                <span class="text">{{ item.percent }}%</span>
            </li>
        </ul> -->
    </div>
</template>

<script>
import * as echarts from 'echarts';
const colorList = ['#1CB93D', '#F3F134', '#F39935', '#FC3E43', '#ff3cff'];
const initEcharts = (myChart, options) => {
    const option = {
        title: {
            left: 20,
            top: 0,
            text: options.title,
            textStyle: {
                color: '#ffffff',
                fontSize: 14,
            },
        },
        tooltip: {
            trigger: 'item',
            formatter: function (params) {
                let res = '';
                if (options.timeTips && options.timeTips.length === 2) {
                    res += `<div>从&nbsp;${options.timeTips[0]}</br>至&nbsp;${options.timeTips[1]}</div>`;
                }
                res += `<div>${params.marker}${params.name}&nbsp;&nbsp;&nbsp;${params.value}%</div>`;
                return res;
            },
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            textStyle: {
                color: '#ccc',
            },
        },
        legend: {
            top: 50,
            left: '49%',
            align: 'left',
            orient: 'vertical',
            itemWidth: 8,
            itemHeight: 8,
            icon: 'circle',
            formatter: function (name) {
                return (
                    '{a|' +
                    name +
                    '}{b|' +
                    '|    ' +
                    options.percent[name] +
                    '}'
                );
            },
            textStyle: {
                rich: {
                    a: {
                        width: 75,
                        color: 'RGBA(255, 255, 255, 0.8)',
                    },
                    b: {
                        color: 'RGBA(255, 255, 255, 0.8)',
                    },
                },
            },
        },
        grid: {
            top: 'center',
            left: '5%',
            right: '5%',
            bottom: 10,
        },
        series: [
            {
                type: 'pie',
                name: options.title,
                top: 20,
                left: 20,
                right: '58%',
                radius: ['0%', '88%'],
                avoidLabelOverlap: false,
                label: {
                    show: false,
                    position: 'center',
                },
                // emphasis: {
                //     label: {
                //         show: true,
                //         fontSize: 18,
                //         fontWeight: 'bolder',
                //     }
                // },
                labelLine: {
                    show: false,
                },
                data: options.pieData,
            },
        ],
    };
    option.series[0].data = options.pieData.map((item, index) => {
        return {
            ...item,
            itemStyle: {
                color: colorList[index],
            },
        };
    });
    myChart.clear();
    myChart.setOption(option);
};
export default {
    name: 'pieRowEchart',
    props: {
        data: {
            type: Object,
            default: () => ({}),
        },
        name: {
            type: String,
            default: 'pie',
        },
        lengendList: {
            type: Array,
            default: () => [
                {
                    provinceName: '浙江',
                    percent: '0',
                },
                {
                    provinceName: '新疆',
                    percent: '0',
                },
                {
                    provinceName: '辽宁',
                    percent: '0',
                },
            ],
        },
    },
    data() {
        return {
            myChart: '',
        };
    },
    watch: {
        data: {
            handler(newV) {
                if (newV.pieData) {
                    this.initEchart();
                }
            },
            deep: true,
        },
    },
    mounted() {
        if (this.data && this.data.pieData) {
            this.initEchart();
        }
    },
    methods: {
        initEchart() {
            this.$nextTick(() => {
                if(!this.myChart){
                    this.myChart = echarts.init(document.getElementById(this.name));
                }
                initEcharts(this.myChart, this.data);
            });
        },
        resize() {
            if (this.myChart) {
                this.myChart.resize();
            }
        },
    },
};
</script>

<style lang="less" scoped>
.pie-echart {
    width: 100%;
    height: 100%;
    position: relative;
}
.pie-row-echart {
    width: 100%;
    height: 100%;
}
.lengend {
    position: absolute;
    top: 62%;
    left: 48%;
    padding-left: 15px;
    padding-top: 5px;
    .lengend-item {
        position: relative;
        border-left: 1px solid #3d546e;
        padding: 7px 0 0;
        list-style-type: none;
        display: flex;
        align-items: center;
    }
    li::before {
        position: relative;
        top: -1px;
        width: 15px;
        border-bottom: 1px solid #3d546e;
        content: '';
        display: inline-block;
    }
    li:last-child {
        height: 0px;
        padding-top: 13px;
    }
    .circle {
        border-radius: 50%;
        width: 7px;
        height: 7px;
        background: #ff3cff;
    }
    .text {
        padding: 0 10px;
        color: RGBA(255, 255, 255, 0.8);
        position: relative;
        &.border::after {
            content: '|';
            position: absolute;
            right: 0px;
            top: 0px;
        }
    }
    // .border{
    //     border-right: 1px solid RGBA(255, 255, 255, 0.8);
    // }
}
</style>
