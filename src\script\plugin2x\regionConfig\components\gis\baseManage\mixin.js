export default {
    data() {
        return {
            tableData: [],
            paginationData: {
                curPage: 1,
                pageSize: 10,
                totalCount: 0,
            },
            isArea: null,
            cellBoundary: null,
        }
    },
    computed:{
        isSelectAll(){
            return this.selectList.length === this.baseStations.length;
        }
    },
    watch: {
        baseStations: {
            handler(newStations) {
                this.paginationData.totalCount = newStations.length;
                this.search({ curPage: 1 });
            },
            immediate: true,
        },
        isArea() {
            this.setTableData(this.baseStations, this.paginationData);
        },
        cellBoundary() {
            this.setTableData(this.baseStations, this.paginationData);
        },
    },
    created() {
        this.search();
    },
    methods: {
        search(paginationData = {}) {
            Object.assign(this.paginationData, paginationData);
            this.setTableData(this.baseStations, this.paginationData);
        },
        getBoundary(cellBoundary) {
            if (Number.isFinite(cellBoundary)) {
                return (cellBoundary / 1000).toFixed(2);
            }
            return '-';
        },
        renderHeader(h, { column, menu }) {
            const prop = column.property;
            return h(
                'el-select',
                {
                    class: 'custom-select', // 添加的class类名
                    props: {
                        value: this[prop], // 这里是你的变量
                        size: 'mini',
                    },
                    on: {
                        change: (command) => {
                            this.handleCommand(command, prop);
                            this[prop] = command; // 更新你的变量
                        },
                    },
                },
                menu.map((it) =>
                    h('el-option', { props: { value: it.value, label: it.label }, key: it.value }, it.label)
                )
            );
        },
        handleSizeChange(pageSize) {
            Object.assign(this.paginationData, {
                pageSize,
                curPage: 1,
            });
            this.setTableData(this.baseStations, this.paginationData);
        },
        handleCurrentChange(curPage) {
            this.paginationData.curPage = curPage;
            this.setTableData(this.baseStations, this.paginationData);
        },
        setTableData(allTableData, paginationData) {
            allTableData = allTableData.filter(item => {
                const isArea = this.isArea ? item.isArea === this.isArea : true;
                const cellBoundary = this.cellBoundary ? Number(this.getBoundary(item.cellBoundary)) : true;
                let isCellBoundary;
                if (!this.cellBoundary) {
                    isCellBoundary = true;
                } else if (this.cellBoundary === 10) {
                    isCellBoundary = cellBoundary > this.cellBoundary;
                } else {
                    isCellBoundary = cellBoundary < this.cellBoundary;
                }
                return isArea && isCellBoundary;
            })
            const { curPage, pageSize, totalCount } = paginationData;
            let startIndex = Math.max(0, (curPage - 1) * pageSize);
            let endIndex = Math.min(totalCount, curPage * pageSize);
            this.tableData = allTableData.slice(startIndex, endIndex);
            this.$nextTick(() => {
                if(this.isSelectAll){
                    this.tableData.forEach((item )=>{
                        this.$refs.dataTable.$refs.commonTable.toggleRowSelection(item,true);
                    });
                }
            });
        },
    }
}