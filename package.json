{"name": "mtex-static-shandong-devops-plugin", "version": "4.0.0", "description": "山东移动运维管理中心", "author": "author", "license": "ISC", "scripts": {"dev": "webpack-dev-server --mode development --hot --config build/webpack.dev.config.js", "dist": "webpack --mode development --progress --colors --config build/webpack.dist.config.js", "prod": "webpack --mode production --progress --colors --config build/webpack.dist.config.js"}, "dependencies": {"axios": "^0.19.0", "browser-md5-file": "^1.1.1", "clean-webpack-plugin": "^3.0.0", "crypto-js": "^4.0.0", "dayjs": "^1.11.13", "echarts": "^5.4.2", "element-ui": "2.15.14", "eslint-config-standard": "12.0.0", "eslint-friendly-formatter": "^4.0.1", "lodash": "^4.17.15", "mtex-rams-core": "^1.0.23", "pinyin-match": "^1.1.1", "proj4": "^2.9.2", "vue-codemirror": "^4.0.6", "vue-draggable-resizable-gorkys": "^2.4.4", "vue-element-extends": "^1.2.24", "vue-grid-layout": "^2.3.8", "vue-seamless-scroll": "^1.1.23", "vuedraggable": "^2.23.2", "vxe-table": "^2.5.22"}, "devDependencies": {"babel-core": "^6.26.3", "babel-eslint": "^7.1.1", "babel-loader": "^7.1.5", "babel-plugin-import": "^1.12.2", "babel-plugin-istanbul": "^4.1.3", "babel-plugin-syntax-dynamic-import": "^6.18.0", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-vue-jsx": "^3.7.0", "babel-plugin-transform-amd-to-commonjs": "^1.4.0", "babel-plugin-transform-runtime": "^6.23.0", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.7.0", "babel-preset-stage-2": "^6.24.1", "babel-preset-stage-3": "^6.24.1", "babel-runtime": "^6.26.0", "css-loader": "^1.0.1", "eslint": "5.16.0", "eslint-loader": "2.1.1", "eslint-plugin-html": "5.0.0", "eslint-plugin-import": "2.14.0", "eslint-plugin-json": "1.3.2", "eslint-plugin-node": "8.0.1", "eslint-plugin-promise": "4.0.1", "eslint-plugin-standard": "4.0.0", "eslint-plugin-vue": "5.1.0", "expose-loader": "^0.7.5", "file-loader": "^2.0.0", "highlight.js": "^9.18.1", "html-loader": "^0.5.5", "html-withimg-loader": "^0.1.16", "less": "^3.13.1", "less-loader": "^5.0.0", "marked": "^1.1.0", "node-sass": "^4.12.0", "sass-loader": "^7.1.0", "style-loader": "^0.22.1", "style-resources-loader": "^1.4.1", "stylus": "^0.54.5", "stylus-loader": "^3.0.2", "svg-sprite-loader": "^5.0.0", "url-loader": "^1.1.2", "vue": "^2.6.10", "vue-cli": "^2.9.6", "vue-html-loader": "^1.2.4", "vue-infinite-scroll": "^2.0.2", "vue-loader": "^15.7.0", "vue-markdown": "^2.2.4", "vue-resource": "^1.5.1", "vue-router": "^3.0.6", "vue-style-loader": "^4.1.2", "vue-template-compiler": "^2.6.10", "vue-virtual-scroller": "^1.0.10", "vuex": "^3.1.1", "webpack": "^4.31.0", "webpack-cli": "^3.3.2", "webpack-dev-server": "^3.4.1", "webpack-merge": "^4.2.2", "worker-loader": "^2.0.0"}}