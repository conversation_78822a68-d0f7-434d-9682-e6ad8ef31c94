<template>
    <el-drawer
        class="base-manage"
        :modal="false"
        :visible.sync="drawer"
        size="40%"
        custom-class="custom-drawer"
        :before-close="handleClose"
    >
        <!-- 标题 -->
        <template #title>
            <div class="custom-title">
                <span class="text">基站管理</span>
            </div>
        </template>
        <!-- 主体 -->
        <div class="base-manage__tabs">
            <el-tabs v-model="activeName">
                <el-tab-pane v-for="(tab, inx) in tabList" v-bind="tab" :key="inx">
                    <keep-alive>
                        <component
                            v-if="tab.name === activeName"
                            :is="tab.name"
                            :ref="tab.name"
                            v-bind="tab.attrs"
                            v-on="tab.listeners"
                        />
                    </keep-alive>
                </el-tab-pane>
            </el-tabs>
        </div>
    </el-drawer>
</template>
<script>
import customTip from './components/customTip.vue';
import allInfo from './components/allInfo.vue';
import { removeDuplicateByCell } from '../common/method';
export default {
    name: 'base-manage',
    components: {
        customTip,
        allInfo
    },
    props: {
        drawer: {
            type: Boolean,
            default: false
        },
        row: {
            type: Object,
            default: () => ({})
        },
        baseStations: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            activeName: 'allInfo',
            allBaseStations: [],
            selectData: {
                allInfo: []
            }
        };
    },
    computed: {
        tabList() {
            return [
                {
                    label: '全部',
                    name: 'allInfo',
                    attrs: {
                        baseStations: this.allBaseStations
                    },
                    listeners: {
                        setSelectList: this.setSelectList.bind(this)
                    }
                }
            ];
        },
        userInfo() {
            return frameService.getUser();
        }
    },
    created() {
        this.allBaseStations = removeDuplicateByCell(
            this.baseStations.filter((item) => item.onceAdded)
        );
    },
    methods: {
        setSelectList(selectList, type) {
            this.selectData[type] = selectList;
        },
        handleClose() {
            this.$emit('update:drawer', false);
            this.$emit('handleClose');
        }
    }
};
</script>
<style lang="less" scoped>
.base-manage {
    position: absolute;
    .custom-title {
        display: flex;
        padding-left: 12px;
        align-items: center;
        height: 48px;
        font-weight: bold;
        border-radius: 6px 6px 0px 0px;
        box-shadow: inset 0px -1px 0px 0px rgba(0, 0, 0, 0.07);
        background: linear-gradient(290deg, rgba(235, 246, 255, 0.67) 0%, #d4ebff 100%);
        // background-color: rgba(0, 50, 107, 0.8);
        .text {
            font-size: 16px;
            color: rgba(0, 0, 0, 0.85);
            // color: #fff;
        }
    }
    &__tabs {
        flex: 1;
        padding: 0 0 16px 16px;
        overflow: hidden;
        .el-tabs {
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        /deep/ .el-tabs__content {
            flex: 1;
            height: 0;
            overflow: unset;
            .el-tab-pane {
                height: 100%;
                display: flex;
                flex-direction: column;
            }
        }
    }
    /deep/ .el-drawer__header {
        margin-bottom: 0;
        padding: 0;
        .el-drawer__close-btn {
            position: absolute;
            right: 16px;
        }
    }
    /deep/ .el-drawer__body {
        display: flex;
        flex-direction: column;
    }
}
/deep/ .custom-drawer {
    max-width: 510px;
    // background-color: rgba(0, 42, 92, 0.9);
}
</style>
