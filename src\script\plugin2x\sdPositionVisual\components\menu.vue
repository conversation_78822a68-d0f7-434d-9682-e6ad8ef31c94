<template>
    <el-menu
        :default-openeds="['1','2','3']"
        :default-active="activeItem"
        @select="selectMenu"
    >
        <el-submenu index="1">
            <span slot="title" class="title-level1">用户位置定位</span>
            <el-menu-item index="realLocation">
                <img :src="require(`../../../../img/icon/position_${activeItem === 'realLocation'?'blue':'grey'}.png`)" alt=""/>
                <span slot="title">实时位置定位</span>
            </el-menu-item>
        </el-submenu>
        <el-submenu index="2">
            <span slot="title" class="title-level1">用户轨迹查询</span>
            <el-menu-item index="baseStation">
                <img :src="require(`../../../../img/icon/baseStation_${activeItem === 'baseStation'?'blue':'grey'}.png`)" alt=""/>
                <span slot="title">历史轨迹查询（基站级）</span>
            </el-menu-item>
            <el-menu-item index="roadNetworkFit">
                <img :src="require(`../../../../img/icon/fit_${activeItem === 'roadNetworkFit'?'blue':'grey'}.png`)" alt=""/>
                <span slot="title">历史轨迹查询（路网拟合）</span>
            </el-menu-item>
        </el-submenu>
        <el-submenu index="3">
            <span slot="title" class="title-level1">用户驻留分析</span>
            <el-menu-item index="Location">
                <img :src="require(`../../../../img/icon/location_${activeItem === 'Location'?'blue':'grey'}.png`)" alt=""/>
                <span slot="title">常驻地查询</span>
            </el-menu-item>
        </el-submenu>
    </el-menu>
</template>

<script>
export default {
    name:'visualMenu',
    data(){
        return {
            activeItem:'realLocation',
        };
    },
    methods:{
        selectMenu(value) {
            this.activeItem = String(value);
            this.$emit('jumpRouter', value);
        },
    }
};
</script>

<style lang="less" scoped>
.el-menu{
    border-right:none;
}
/deep/.el-menu {
    
    .el-menu-item {
        min-width:100%;
        span{
            font-size: 14px;
        }
        &.is-active{
            color: #409EFF;
            background: #ECF5FF;
            border-left:.17rem solid #409EFF;
        }
    }
}
.title-level1{
    font-size: 14px;
    font-weight: 600;
    color: #303133;
}
</style>