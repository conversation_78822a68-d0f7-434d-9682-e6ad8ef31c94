import axios from 'axios';
const token = localStorage.getItem('token');
axios.interceptors.request.use((config) => {
    config.headers.token = token;
    return config;
});
// import { reqUtil } from 'mtex-rams-core';
// const { decodeResult, tryEncodeParam } = reqUtil;
// import { decodeResultUser } from '../utils/resUtils';

// post 请求
export const mtexFun = function (url, params = {}, config = undefined) {
    let newParams = params;
    // newParams = tryEncodeParam(newParams);
    return new Promise((resolve, reject) => {
        // axios
        //     .post(systemUtil.rootPath + '/' + url, newParams)
        axios({
            method: 'post',
            url: systemUtil.rootPath + '/' + url,
            data: newParams,
            ...config
        })
            .then((res) => {
                let newRes = res.data;
                // if (res.data.encodeResp) {
                //     newRes = decodeResult(res.data);
                // }
                let result = newRes;
                if (result.returnCode === '000001') {
                    resolve(result.data);
                    return;
                }
                if (res.headers && res.headers['content-type'] === 'application/force-download') {
                    resolve(result);
                    return;
                }
                if (res.headers && res.headers['content-type'] ===
                    'application/vnd.ms-excel;charset=utf-8') {
                    resolve(res);
                    return;
                }
                reject({
                    success: result.returnCode,
                    errorMessage: result.returnMsg,
                    result: result,
                });
                return;
            })
            .catch((err) => {
                reject({
                    success: 111,
                    errorMessage: err.message,
                });
            });
    });
};
// get 请求
export const mtexFunGet = function (url, params = {}, headers = undefined) {
    let newParams = params;
    // newParams = tryEncodeParam(newParams);
    if (headers) {
        for (let key in headers) {
            const value = headers[key];
            axios.defaults.headers.get[key] = value;
        }
    }
    return new Promise((resolve, reject) => {
        axios
            .get(url, newParams)
            .then((res) => {
                let newRes = res.data;
                if (res.data.encodeResp) {
                    newRes = decodeResultUser(res.data);
                }
                resolve(newRes);
            })
            .catch((err) => {
                reject('');
            });
    });
};
