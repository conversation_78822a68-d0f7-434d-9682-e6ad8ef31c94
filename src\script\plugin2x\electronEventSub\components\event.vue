<template>
    <div class="event">
        <el-tooltip
            v-for="(item, index) in eventData"
            :key="index"
            class="item2"
            effect="light"
            :content="item.content || '暂无'"
            placement="top"
            popper-class="tooltip-name tooltip-dark popover-dark"
        >
            <div
                class="event-item border-radius4"
                :class="['normal', item.check ? 'active' : '']"
                @click="checkedChange(item, $event)"
            >
                <el-checkbox v-if="item.name !== '驻留'" v-model="item.check"
                    >{{ item.name }}
                </el-checkbox>
                <div class="lingering" v-else>
                    <el-checkbox style="padding: 10px 0px; margin-bottom: 0px" v-model="item.check"
                        >{{ item.name }}
                    </el-checkbox>
                    <div class="step-content-box">
                        <div class="item">
                            <label class="item-label no-required">最小时长：</label>
                            <div class="item-input">
                                <el-input-number
                                    size="small"
                                    v-model="item.eventCondition.minTime"
                                    :min="0"
                                ></el-input-number>
                                <span style="font-size: 14px; margin-left: 5px">s</span>
                            </div>
                        </div>
                        <div class="item">
                            <el-checkbox
                                class="need-reset"
                                style="margin-bottom: 0px"
                                v-model="item.eventCondition.needReset"
                                >重置驻留时长
                            </el-checkbox>
                        </div>
                    </div>
                </div>
                <!--  -->
            </div>
        </el-tooltip>
    </div>
</template>
<script>
export default {
    name: 'event',
    props: {},
    data() {
        return {
            eventData: [
                {
                    name: '进入',
                    id: 1,
                    check: false,
                    content:
                        '根据输入区域，基于基站拉链，识别用户从区域外部进入区域内部，获取进入内部的首个基站信息，触发生成事件。'
                },
                {
                    name: '离开',
                    id: 2,
                    check: false,
                    content:
                        '根据输入区域，基于基站拉链，识别用户从区域内部进入区域外部，获取进入外部的首个基站信息，触发生成事件。'
                },
                {
                    name: '驻留',
                    id: 3,
                    check: false,
                    eventCondition: {
                        minTime: 0,
                        needReset: false
                    },
                    content:
                        '根据输入区域，驻留时长条件，检测区域内用户驻留时长，从任务启动时间起持续累计，如达到驻留时长门限，及生成驻留事件。用户中途离开区域，累计时长会清零。可设置重复触发'
                }
            ],
            eventList: []
        };
    },
    watch: {
        // eventData: {
        //     handler(val) {
        //         // this.$emit(this.checkedChange,val)
        //     },
        //     deep: true,
        // },
    },
    methods: {
        inforClick(e) {
            e.stopPropagation();
        },

        initData() {
            this.eventData.forEach((item) => {
                item.check = false;
            });
        },
        checkedChange(item, e) {
            let eClassList = [...e.target.classList];
            this.$nextTick(() => {
                if (
                    eClassList.includes('event-item') ||
                    eClassList.includes('lingering') ||
                    eClassList.includes('step-content-box') ||
                    eClassList.includes('item')
                ) {
                    item.check = !item.check;
                }
            });
        },
        getCheckedList() {
            let list = this.eventData.filter((item) => item.check);
            if (!list.length) {
                return list;
            }
            list = list.map((item) => {
                let eventCondition = null;
                if (item.id === 3) {
                    eventCondition = item.eventCondition;
                }
                return {
                    eventType: item.id,
                    eventCondition: eventCondition
                };
            });
            return list;
        }
    }
};
</script>
<style lang="less" scoped>
.border-radius4 {
    border-radius: 4px;
}
.event {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
    .event-item {
        border: 1px solid rgba(0, 149, 255, 0.5);
        width: calc(20% - 16px);
        height: 40px;
        line-height: 40px;
        text-indent: 6px;
        margin-right: 20px;
        margin-bottom: 20px;
        cursor: pointer;
        user-select: none;

        /deep/.el-checkbox__label {
            color: #c9dfff;
        }
        &:nth-child(5n) {
            margin-right: 0px;
        }
        &:last-child {
            // color: blue;
            width: calc(40% - 12px);
            height: 90px;
            line-height: normal;
            margin-bottom: 10px;
            min-width: 510px;
        }
    }
    .active {
        // border: 1px solid #409eff;
        background: rgba(0, 149, 255, 0.6);
        /deep/.el-checkbox__label {
            color: #fff;
        }
    }
    .step-content-box {
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        justify-content: flex-start;
        .item {
            width: 50%;
            height: 40px;
            line-height: 40px;
            &-label {
                color: #c9dfff;
                font-weight: 400;
                font-size: 14px;
            }
            .is-required:before {
                content: '*';
                color: #f56c6c;
                margin-right: 4px;
            }
            .no-required:before {
                content: '';
                margin-right: 10px;
            }
            &-input {
                display: inline-block;
                text-indent: 0;
                /deep/.el-input-number__decrease,
                /deep/.el-input-number__increase {
                    background-color: rgb(1, 61, 130);
                    border: none;
                    i {
                        color: #fff;
                        background: transparent;
                    }
                }
            }
        }
    }
}
</style>
<style lang="less">
.tooltip-name {
    // border: none !important;
    // background: #fff;
    min-width: 150px;
    max-width: 300px;
    border-radius: 4px;
    border: 1px solid rgba(0, 149, 255, 0.5) !important;
    padding: 12px;
    z-index: 2000;
    color: #fff;
    line-height: 1.4;
    text-align: justify;
    font-size: 14px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    word-break: break-all;
    position: relative;
}
</style>
