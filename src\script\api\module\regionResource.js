const { getFullPath } = require('../index.js');
let url = 'mtexapi';
const regionService = {
    searchRegion: getFullPath(url + '/region-service/regionMng/searchRegion'), //区域查询接口
    userPoint: getFullPath(url + '/location-service/userLoc/userPoint'), //用户实时位置查询接口
    userPointUnencrypted: getFullPath(url + '/location-service/userLoc/userPoint/unencrypted'), //用户实时位置查询接口（明文）
    cityHotMap: getFullPath(url + '/region-service/heat/city'), //地市职住热力信息查询接口
    jobsHousing: getFullPath(url + '/region-service/jobsHousing/user'), //单用户职住信息查询接口
    regionHotMap: getFullPath(url + '/region-service/heat/cityRegion'), //地市区域热力查询接口
    getCityDistrict: getFullPath(url + '/region-service/regionMng/getCityDistrict'), //地市区县获取接口
    getLayer: getFullPath(url + '/region-service/regionMng/getLayer'), // 区域类型查询接口
    addRegionShape: getFullPath(url + '/region-service/regionMng/addRegionShape'), // 区域添加接口
    getCenter: getFullPath(url + '/region-service/regionMng/getCenter'), //
    downloadExcelTemplate: getFullPath(url + '/region-service/regionMng/downloadExcelTemplate'),
    addRegionShapeByExcel: getFullPath(url + '/regionMng/addRegionShapeByExcel'),

    //处理指标统计及可视化
    addList: getFullPath(url + '/region-service/largeScreen/addList'), //添加组件
    getconfigList: getFullPath(url + '/region-service/largeScreen/list'), //首页大屏-查询组件列表
    updateList: getFullPath(url + '/region-service/largeScreen/updateList'), //首页-配置更新接口
    delList: getFullPath(url + '/region-service/largeScreen/delList'), //首页-配置更新接口
    delayChart: getFullPath(url + '/region-service/largeScreen/delayChart'), //时延饼图查询
    trendChart: getFullPath(url + '/region-service/largeScreen/trendChart'), //数据量趋势图

    // 用户轨迹
    getUserLocation: getFullPath(url + '/location-service/userLoc/userLocation'), //用户轨迹查询
    getRoadLingerLocation: getFullPath(url + '/location-service/userLoc/roadLingerLocation'), //路网拟合轨迹查询

    //事件订阅
    addEvent: getFullPath(url + '/region-service/event/addEvent'), // 添加事件
    searchEvent: getFullPath(url + '/region-service/event/searchEvent'), //事件任务查询
    delEvent: getFullPath(url + '/region-service/event/delEvent'), // 删除事件
    regionEventRecordCount: getFullPath(url + '/region-service/event/regionEventRecordCount'), //订购任务数、区域数、数据量总数查询接口
    eventTrendChart: getFullPath(url + '/region-service/event/trendChart'), // 电子围栏趋势图查询
    eventDelayChart: getFullPath(url + '/region-service/event/delayChart'), //电子围栏时延饼图查询
    downloadEventList: getFullPath(url + '/region-service/event/downloadEventList'), //下载任务清单
    regionHotMapBySelect: getFullPath(url + '/region-service/heat/selection/cityRegion'), //电子围栏热力圈框选

    // 系统维护
    loginTrend: getFullPath(url + '/region-service/logs/call/login/trend'), //系统成效-应用支撑频次(登录趋势)
    webAppRank: getFullPath(url + '/region-service/logs/web/app/rank'), //系统成效-热门功能板块(功能点击排行)
    webAppDetail: getFullPath(url + '/region-service/logs/web/app/trend'), //系统成效-功能点击次数趋势(功能点击详情)
    softPurchaseDataRank: getFullPath(url + '/region-service/logs/soft/collection/rank'), //系统成效-软采数据排名
    softPurchaseDataTrend: getFullPath(url + '/region-service/logs/soft/collection/trend/compare'), //系统成效-软采数据趋势
    realTimeLocationDataFlow: getFullPath(url + '/region-service/logs/call/monitor/trend/compare'), //系统成效-实时精准定位数据流量监控
    apiCallRank: getFullPath(url + '/region-service/logs/call/rank'), //系统成效-热门API板块(接口调用排行)
    apiCallDetail: getFullPath(url + '/region-service/logs/call/detail/compare'), //系统成效-API调用次数趋势(接口调用详情)

    // 门户首页
    capabilityCount: getFullPath(url + '/region-service/energy/panel/capability/count'), // 能效展板-能力数量、能力使用查询接口
    alarmMonitor: getFullPath(url + '/region-service/energy/panel/alarm/monitor'), // 能效展板-告警监控板查询接口
    valueSummary: getFullPath(url + '/region-service/energy/panel/value/summary'), // 能效展板-累计价值实现/对内支撑需求/对外价值体现查询接口
    createContract: getFullPath(url + '/region-service/energy/panel/createContract'), // 能效展板-添加合同
    updateContract: getFullPath(url + '/region-service/energy/panel/updateContract'), // 能效展板-修改合同
    deleteContract: getFullPath(url + '/region-service/energy/panel/deleteContract'), // 能效展板-删除合同
    queryContract: getFullPath(url + '/region-service/energy/panel/queryContract'), // 能效展板-查询合同
    residentPositionLac: getFullPath(url + '/region-service/energy/panel/resident/position/lac'), // 能效展板-常驻、定位输出流量、4/5G基站个数查询
    fingerprintTotal: getFullPath(url + '/region-service/energy/panel/fingerprint/total'), // 能效展板-总体指纹精度查询
    mrData: getFullPath(url + '/region-service/energy/panel/mr/data'), // 能效展板-MR采样数量查询
    fingerprintCityRank: getFullPath(url + '/region-service/energy/panel/fingerprint/city/rank'), // 能效展板-地市指纹精度排行查询
    softCollectionRank: getFullPath(url + '/region-service/energy/panel/soft/collection/rank'), // 能效展板-实时软采流量查询
    approveInnerQuery: getFullPath(url + '/region-service/approveInner/query') // 能效展板-对内支撑需求查询
};
// 金库审批接口对应场景id、资源id配置
const goldApprovalApis = {
    userPointUnencrypted: {
        assetId: '1910268316132462592',
        sceneId: '1436187919349059584'
    }
};

module.exports = regionService;
module.exports.goldApprovalApis = goldApprovalApis;
