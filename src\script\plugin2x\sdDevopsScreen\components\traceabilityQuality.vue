<template>
    <div class="real-time">
        <com-titled :title="name" :searchParams.sync="searchParams" @iconClick="isShowMore = !isShowMore" 
        :widgetParams="widgetParams"
        @closeShowMore="closeShowMore"
        ></com-titled>
        <div class="charts" @click="closeShowMore">
           <template v-for="(list,listIndex) in configure">
                <component
                    class="charts-item"
                    :ref="uuid+listIndex"
                    :key="listIndex"
                    :is="list.comp"
                    :name="uuid+listIndex"
                    :data="list.data"
                    :type="list.type"
                    :lengendList="lengendList[listIndex] || []"
                />
            </template>
            <el-tooltip class="item" effect="dark"  placement="right">
                <div slot="content"><span class="hight-text">包含：</span><br/>
                    <span class="normal-text">信令采集时延、输出时延</span><br/>
                    <span class="hight-text">不包含：</span><br/>
                    <span class="normal-text">UCX推送时延</span>
                </div>
                <i class="el-icon-warning-outline tips"></i>
            </el-tooltip>
        </div>
        <more-tips-bar
            :isShow.sync="isShowMore"
            :widgetParams="widgetParams"
            :refresh.sync ="refresh"
            @close="closeCard(widgetId)"
            @replace="replaceCard(uuid)"
            @refresh="init"
        ></more-tips-bar>
    </div>
</template>

<script>
import comTitled from './comTitled.vue';
import lineEchart from '@/script/components/charts/lineEchart.vue';
import moreTipsBar from './moreTipsBar.vue';
import screenCommon from '../mixins/commonMixin.js';
import pieRowEchart from '@/script/components/charts/pieRowEchart.vue';
export default {
    name:'traceabilityQuality',
    components:{
        comTitled,
        pieRowEchart,
        lineEchart,
        moreTipsBar
    },
    inject: ['closeCard','replaceCard'],
    props:{
        name:{
            type:String,
            default:'源端质量',
        },
        uuid:{
            type:String,
            default:'67bfd6-e75d-4bd1-8a9f-1a1922fb5c16a'
        },
        widgetParams:{
            type:Object,
            default:() => ({}),
        },
        widgetId:{
            type:String,
            default:'',
        }
    },
    inject: ['closeCard','replaceCard','updateComList'],
    mixins:[screenCommon],
    data(){
        return{
            configure:[
                {
                    type:'singleLineArea',
                    comp:'lineEchart',
                    data:{
                        title:'实时信令',
                        xAxisData:[],
                        yAxis:['数量(亿)）'],
                        unit:['亿'],
                        isNull:true,
                        granularity:[''],
                        minValue:[''],
                        minInterval:[''],
                        lineData:[
                            {
                                name:'实时信令',
                                data:[],
                                color:'RGBA(35, 153, 216, 1)',
                                areaStyle:{
                                    color:{
                                        type: 'linear',
                                        x: 0,
                                        y: 0,
                                        x2: 0,
                                        y2: 1,
                                        colorStops: [{
                                            offset: 0, color: 'RGBA(34, 179, 250, 0.8)' // 0% 处的颜色
                                        }, {
                                            offset: 1, color: 'RGBA(36, 75, 117, 0.3)' // 100% 处的颜色
                                        }],
                                        global: false // 缺省为 false
                                    }
                                },
                            },
                        ]
                    }
                },
                {
                    type:'singleLineArea',
                    comp:'lineEchart',
                    data:{
                        title:'4G UEMR',
                        xAxisData:[],
                        yAxis:['数量(亿)）'],
                        unit:['亿'],
                        isNull:true,
                        granularity:[''],
                        minValue:[''],
                        minInterval:[''],
                        lineData:[
                            {
                                name:'4G UEMR',
                                data:[],
                                color:'RGBA(35, 153, 216, 1)',
                                areaStyle:{
                                    color:{
                                        type: 'linear',
                                        x: 0,
                                        y: 0,
                                        x2: 0,
                                        y2: 1,
                                        colorStops: [{
                                            offset: 0, color: 'RGBA(34, 179, 250, 0.8)' // 0% 处的颜色
                                        }, {
                                            offset: 1, color: 'RGBA(36, 75, 117, 0.3)' // 100% 处的颜色
                                        }],
                                        global: false // 缺省为 false
                                    }
                                },
                            },
                        ]
                    }
                },
                {
                    type:'singleLineArea',
                    comp:'lineEchart',
                    data:{
                        title:'5G UEMR',
                        xAxisData:[],
                        yAxis:['数量(亿)）'],
                        unit:['亿'],
                        isNull:true,
                        granularity:[''],
                        minValue:[''],
                        minInterval:[''],
                        lineData:[
                            {
                                name:'5G UEMR',
                                data:[],
                                color:'RGBA(35, 153, 216, 1)',
                                areaStyle:{
                                    color:{
                                        type: 'linear',
                                        x: 0,
                                        y: 0,
                                        x2: 0,
                                        y2: 1,
                                        colorStops: [{
                                            offset: 0, color: 'RGBA(34, 179, 250, 0.8)' // 0% 处的颜色
                                        }, {
                                            offset: 1, color: 'RGBA(36, 75, 117, 0.3)' // 100% 处的颜色
                                        }],
                                        global: false // 缺省为 false
                                    }
                                },
                            },
                        ]
                    }
                },
                {
                    type:'pie',
                    comp:'pieRowEchart',
                    data:{
                        pieData: [
                            { value: 0, name: '<60 s' },
                            { value: 0, name: '60-120 s' },
                            { value: 0, name: '120-300 s' },
                            { value: 0, name: '300-1200 s' },
                            { value: 0, name: '>1200 s' },
                        ],
                        percent: {
                            '<60 s': '0.0%',
                            '60-120 s': '0.0%',
                            '120-300 s': '0.0%',
                            '300-1200 s': '0.0%',
                            '>1200 s':'0.0%'
                        },
                        title: '时延',
                    }
                },
                {
                    type:'pie',
                    comp:'pieRowEchart',
                    data:{
                        pieData: [
                            { value: 0, name: '<60 s' },
                            { value: 0, name: '60-120 s' },
                            { value: 0, name: '120-300 s' },
                            { value: 0, name: '300-1200 s' },
                            { value: 0, name: '>1200 s' },
                        ],
                        percent: {
                            '<60 s': '0.0%',
                            '60-120 s': '0.0%',
                            '120-300 s': '0.0%',
                            '300-1200 s': '0.0%',
                            '>1200 s':'0.0%'
                        },
                    }
                },
                {
                    type:'pie',
                    comp:'pieRowEchart',
                    data:{
                        pieData: [
                            { value: 0, name: '<60 s' },
                            { value: 0, name: '60-120 s' },
                            { value: 0, name: '120-300 s' },
                            { value: 0, name: '300-1200 s' },
                            { value: 0, name: '>1200 s' },
                        ],
                        percent: {
                            '<60 s': '0.0%',
                            '60-120 s': '0.0%',
                            '120-300 s': '0.0%',
                            '300-1200 s': '0.0%',
                            '>1200 s':'0.0%'
                        },
                    }
                },
            ],
            isShowMore:false,
            searchParams:{},
            searchType:[1,2,3],//1为xdr  2为4guemr  3为5guemr
            reRefreshTimer:null,
        };
    },
    computed:{
        postParams(){
            let startDate = new Date();
            let endDate = new Date().format('yyyy-MM-dd HH:mm:00');
            if(this.searchParams.relativeTime){
                startDate = new Date(startDate.setTime(startDate.getTime() - 60 * 1000 * this.searchParams.relativeTime)).format('yyyy-MM-dd HH:mm:00');
            }else{
                startDate = this.searchParams.startTime;
                endDate = this.searchParams.endTime;
            }
            return {
                startTime:startDate || this.defaultStartTime,
                endTime:endDate || this.defaultNewDate,
                granularity:this.searchParams.granularity,
            };
        }
    },
    watch:{
        searchParams:{
            handler(newV,oldV){
                this.init();
                if(JSON.stringify(oldV) !=='{}'){
                    const params = Object.assign({},this.widgetParams,newV);
                    this.updateComList(2,{
                        uuid:this.uuid,
                        widgetParams:{
                            ...params,
                        }
                    });
                }
            },
            deep:true,
        },
        refresh:{
            handler(newV,oldV){
                this.setIntervalTimer();
                if(JSON.stringify(oldV) !=='{}'){
                    const params = Object.assign({},this.widgetParams,{refresh:newV});
                    this.updateComList(2,{
                        uuid:this.uuid,
                        widgetParams:{
                            ...params,
                        }
                    });
                }
            }
        }
    },
    beforeDestroy() {
        this.clearRefreshTimer();
    },
    methods:{
        init(){
            const promies = [];
            this.searchType.forEach((item) => {
                promies.push(this.getDelayChart(item));
                promies.push(this.getTrendChart(item));
            });
            Promise.all(promies);
        },
        resize(){
            this.configure.forEach((ele,index) => {
                const name = this.uuid+index;
                this.$refs[name][0].resize();
            });
        },
        getDelayChart(searchType){
            this.getPost('delayChart',{...this.postParams,dataType:searchType},'时延饼图查询',(data) => {
                this.handlerPieData(data,searchType+2);
            });
        },
        getTrendChart(searchType){
            this.getPost('trendChart',{...this.postParams,dataType:searchType},'数据量趋势图',(data) => {
                this.handlerLineData(data,searchType-1);
            });
        },
        setIntervalTimer(){
            this.clearRefreshTimer();
            const that = this;
            this.reRefreshTimer = setInterval(() => {
                 that.init();
            },that.refresh * 60000);
        },
        clearRefreshTimer(){
            this.reRefreshTimer && clearInterval(this.reRefreshTimer);
        },
    }
};
</script>

<style lang="less" scoped>
.real-time{
    width:calc(50% - 16px);
    height: 452px;
    background: linear-gradient(116deg, #252C51 0%, #24272F 100%);
    border-radius: 12px 12px 12px 12px;
    position: relative;
}
.charts{
    width: 100%;
    height:calc(100% - 56px);
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    position: relative;
    .charts-item{
        width:33%;
        height:50%;
    }
    .tips{
        position: absolute;
        bottom: calc(50% - 19px);
        left: 60px;
        color:RGBA(255, 255, 255, 0.8);
        font-size: 14px;
    }
}
</style>