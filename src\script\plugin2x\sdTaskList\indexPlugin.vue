<template>
    <div class="task-list mtex-shandong-devops-dark-theme" v-loading="detailLoading">
        <comNavTitle :navList="navList"> </comNavTitle>
        <cornerCard class="task-list-con">
            <div class="search-con">
                <el-form class="search-left" :model="form" label-width="80px" size="small" inline>
                    <el-form-item label="任务ID:" prop="taskId">
                        <el-input
                            v-model="form.taskId"
                            class="form-input"
                            placeholder="请输入任务ID"
                        ></el-input>
                    </el-form-item>

                    <el-form-item label="任务名称:" prop="taskName">
                        <el-input
                            v-model="form.taskName"
                            class="form-input"
                            placeholder="请输入任务名称"
                        ></el-input>
                    </el-form-item>

                    <el-form-item label="创建时间：" label-width="85px">
                        <date-picker
                            v-model="form.time"
                            :is-init-time="true"
                            init-time-type="recent"
                            clearable
                            :recent-days="30"
                            @change="onTimeChange"
                        ></date-picker>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" size="small" @click="getTaskList">查询</el-button>
                        <el-button type="primary" size="small" @click="reset" class="reset-btn"
                            >重置</el-button
                        >
                    </el-form-item>
                </el-form>

                <div class="search-right">
                    <el-button
                        icon="el-icon-circle-close"
                        type="primary"
                        size="small"
                        @click="toNoWork"
                        >批量失效</el-button
                    >
                </div>
            </div>
            <div
                class="table-con"
                v-loading="taskListLoading"
                element-loading-background="rgba(0, 0, 0, 0.4)"
            >
                <div class="table-box" v-show="tableData.length">
                    <custom-table
                        :tableData="tableData"
                        @row-click="rowClick"
                        @selection-change="selectionChange"
                    ></custom-table>
                </div>
                <div class="paging" v-show="tableData.length">
                    <el-pagination
                        class="pagination-dark"
                        popper-class="pagination-size-dark popover-dark"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page.sync="pageOption.page"
                        :page-sizes="[10, 50, 200, 300, 400]"
                        :page-size.sync="pageOption.pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="pageOption.total"
                    >
                    </el-pagination>
                    <el-button
                        v-loading="downloadBtnLoading"
                        class="download-btn"
                        type="primary"
                        icon="el-icon-download"
                        size="small"
                        @click="download"
                        >下载</el-button
                    >
                </div>
                <el-empty
                    v-if="!tableData.length"
                    :image="require('../../../img/noDataMon.png')"
                ></el-empty>
            </div>
        </cornerCard>
        <detail-dialog
            v-show="detailDialogVisible"
            :detailData="detailData"
            @change-dialog="changeDialog"
        ></detail-dialog>
    </div>
</template>
<script>
import cornerCard from '_com/cornerCard.vue';
import comNavTitle from '_com/comNavTitle.vue';
import customTable from '_com/customTable.vue';
import datePicker from '_com/datePicker.vue';
import { handleDownloadFile } from '@/script/utils/index';
const detailDialog = () => import('./detailDialog.vue');
import commonMixins from '@/script/mixins/commonMixins.js';
import { getFileRequst } from '@/script/utils/request.js';

const statusOps = {
    1: '正常',
    0: '异常'
};
const isValidOps = {
    0: '失效',
    1: '生效'
};
const eventData = {
    1: {
        eventTypeName: '进入',
        eventType: 1,
        describe:
            '根据输入区域，基于基站拉链，识别用户从区域外部进入区域内部，获取进入内部的首个基站信息，触发生成事件。'
    },
    2: {
        eventTypeName: '离开',
        eventType: 2,
        describe:
            '根据输入区域，基于基站拉链，识别用户从区域内部进入区域外部，获取进入外部的首个基站信息，触发生成事件。'
    },
    3: {
        eventTypeName: '驻留',
        eventType: 3,
        describe:
            '根据输入区域，驻留时长条件，检测区域内用户驻留时长，从任务启动时间起持续累计，如达到驻留时长门限，及生成驻留事件。用户中途离开区域，累计时长会清零。可设置重复触发'
    }
};
export default {
    name: 'taskList',
    mixins: [commonMixins],
    props: {
        outsideParam: {
            type: Array,
            default: () => []
        }
    },
    components: {
        cornerCard,
        comNavTitle,
        customTable,
        detailDialog,
        datePicker
    },
    data() {
        return {
            navList: this.outsideParam,
            detailLoading: false,
            form: {
                time: [],
                aeraType: '',
                taskId: '',
                taskName: ''
            },
            tableOps: [
                { label: '任务ID', name: 'taskId' },
                { label: '任务名称', name: 'taskName' },
                { label: '全部状态', name: 'status', slot: 'custom' },
                { label: '生效开始时间', name: 'startTime', slot: 'custom' },
                { label: '生效结束时间', name: 'endTime', slot: 'custom' },
                { label: '生效状态', name: 'isTime', slot: 'custom' },
                { label: '备注', name: 'comment' },
                { label: '操作', name: 'handler', slot: 'slot' }
            ],
            // 1、自定义区域 2、省份 3、地市  4、区县
            aeraTypeList: [
                { label: '全部', value: '全部' },
                { label: '自定义区域', value: 1 },
                { label: '省份', value: 2 },
                { label: '地市', value: 3 },
                { label: '区县', value: 4 }
            ],
            tableData: [],
            pageOption: {
                page: 1,
                pageSize: 10,
                total: 0
            },
            tableSelectList: [],
            detailDialogVisible: false,
            timer: null,
            downloadBtnLoading: false,
            taskListLoading: false,
            detailData: {},
            eventObj: {}
        };
    },
    watch: {
        'form.taskId'(val) {
            if (isNaN(val * 1)) {
                this.form.taskId = '';
            }
        }
    },
    methods: {
        init() {},
        initParams(hasPage = true) {
            const params = {
                startTime: '', //指任务创建时间大于该时间
                endTime: '', //指任务创建时间小于该时间
                taskId: this.form.taskId * 1 || null,
                taskName: this.form.taskName,
                residentType: null,
                isValid: 1
            };
            if (Array.isArray(this.form.time) && this.form.time.length === 2) {
                params.startTime = this.form.time[0];
                params.endTime = this.form.time[1];
            }
            if (!params.userId) {
                params.userId = null;
            }
            if (hasPage) {
                params.pageIndex = this.pageOption.page; //默认第一页
                params.rowsPage = this.pageOption.pageSize; //默认1000行
            }

            if (params.areaType === '全部') {
                params.areaType = null;
            }

            return params;
        },
        getTaskList(topSearch = false) {
            if (this.timer) {
                clearTimeout(this.timer);
                this.timer = null;
            }
            this.timer = setTimeout(() => {
                this.tableData = [];
                if (topSearch) {
                    this.pageOption.page = 1;
                    this.pageOption.pageSize = 10;
                }
                const params = this.initParams();

                this.taskListLoading = true;
                this.getPost(
                    'post',
                    'searchEvent',
                    params,
                    '事件任务查询',
                    (res) => {
                        this.taskListLoading = false;
                        if (res && res.taskList.length) {
                            let list = res.taskList.map((item) => {
                                return {
                                    ...item,
                                    statusCn: statusOps[item.status],
                                    isValidCn: isValidOps[item.isValid]
                                };
                            });

                            this.tableData = list;
                            this.pageOption.total = res.totalNumber;
                        }
                    },
                    () => {
                        this.taskListLoading = false;
                    }
                );
            }, 200);
        },
        reset() {
            this.form = this.$options.data().form;
            this.getTaskList();
        },
        toNoWork() {
            if (!this.tableSelectList.length) {
                this.$message.warning('请先选中数据');
                return;
            }
            const taskIds = this.tableSelectList.map((item) => item.taskId);
            this.batchSetInvalid(taskIds);
        },
        download() {
            const params = this.initParams(false);
            this.downloadBtnLoading = true;
            getFileRequst('post', 'downloadEventList', params, {
                responseType: 'blob'
            })
                .then((res) => {
                    handleDownloadFile(res, (err) => {
                        this.$message(err);
                    });
                    this.downloadBtnLoading = false;
                })
                .catch((e) => {
                    this.downloadBtnLoading = false;
                });
        },
        handleSizeChange() {
            this.pageOption.page = 1;
            this.getTaskList();
        },
        handleCurrentChange() {
            this.getTaskList();
        },
        rowClick(name, row, $index) {
            if (name === '查看') this.goDetail(row, $index);
            if (name === '失效') this.doWorking(row, $index);
        },
        selectionChange(val) {
            this.tableSelectList = val;
        },
        goDetail(row, $index) {
            this.detailData = {};
            const data = row;
            const eventObj = this.eventObj;
            const events = data.events.map((event) => {
                return {
                    ...event,
                    describe: eventObj[event.eventType]
                };
            });

            let userP = '暂无';
            this.detailData = {
                taskName: data.taskName,
                taskId: data.taskId,
                time: `${data.startTime} 至 ${data.endTime}`,
                comment: data.comment,
                areaType: '自定义区域',
                area: data.areas.areaNames,
                peopleType: '暂无',
                userP: userP,
                events: events.map((item) => Object.assign(item, eventData[item.eventType])),
                output: [{}]
            };
            this.changeDialog(true);
        },
        // 设置是否生效
        doWorking(row, $index) {
            this.batchSetInvalid([row.taskId]);
        },
        batchSetInvalid(taskIds) {
            this.$confirm('是否确定将此任务设置为不生效？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    this.taskListLoading = true;
                    this.getPost(
                        'post',
                        'delEvent',
                        { taskIds: taskIds },
                        '事件任务删除',
                        (res) => {
                            this.taskListLoading = false;
                            this.$message({
                                type: 'success',
                                message: '设置成功!'
                            });
                            this.getTaskList();
                        },
                        () => {
                            this.taskListLoading = false;
                        }
                    );
                })
                .catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消'
                    });
                });
        },
        changeDialog(val) {
            this.detailDialogVisible = val;
        },
        onTimeChange() {
            this.getTaskList(true);
        }
    },
    async mounted() {
        this.$nextTick(() => {
            this.getTaskList();
        });
    }
};
</script>
<style lang="less" scoped>
@import url('../../../style/custom.less');
.task-list {
    height: 100%;
    // display: flex;
    // flex-direction: column;
    .task-list-con {
        height: calc(100% - 64px);
        border: 16px solid var(--normal-bg-border);
        padding: 16px;
        background: var(--normal-bg);
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        .search-con {
            width: 100%;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            .search-left {
                display: flex;
                flex-direction: row;
                justify-content: flex-start;
                flex-wrap: wrap;
                .form-input {
                    width: 160px;
                }
                /deep/.el-form-item {
                    margin-bottom: 10px;
                    margin-right: 10px;
                }
            }
            .search-right {
                margin-left: auto;
            }
            /deep/.el-form-item__label {
                color: #ffffffa6;
                font-weight: 400;
            }
        }
        .table-con {
            width: 100%;
            height: calc(100% - 46px);
        }
        .el-empty {
            height: 100%;
        }
        .table-box {
            width: 100%;
            height: calc(100% - 50px);
        }
        .paging {
            width: 100%;
            text-align: right;
            margin-top: 16px;
            display: flex;
            flex-direction: row;
            align-items: self-end;
            justify-content: flex-end;
            .download-btn {
                margin-left: 16px;
            }
        }
    }
}
</style>
