<template>
    <div class="location mtex-shandong-devops-dark-theme">
        <comNavTitle :navList="navList" :isShowInfo="true" :infoText="'说明：常驻地查询'">
            <searchBar
                class="searchBar"
                ref="formList"
                :formCols="formCols"
                :form="form"
                :isTransparentBg="true"
            >
                <template #search>
                    <el-button type="primary" size="small" @click="search">查询</el-button>
                </template>
                <template #reset>
                    <el-button class="reset-btn" size="small" @click="reset">重置</el-button>
                </template>
            </searchBar>
        </comNavTitle>
        <mtv-gis
            class="gis"
            ref="LocationGisMap"
            :totaloptions="gistotalOptions"
            @onLoad="gisOnLoad"
        ></mtv-gis>
    </div>
</template>

<script>
import searchBar from '_com/searchBar/searchBar.vue';
import comNavTitle from '_com/comNavTitle.vue';
import commonMixins from '@/script/mixins/commonMixins.js';
import { gistotalOptions, getMayType, changeGisColor } from '@/script/constant/gis.js';
export default {
    name: 'Location',
    components: {
        searchBar,
        comNavTitle
    },
    props: {
        outsideParam: {
            type: Array,
            default: () => []
        }
    },
    mixins: [commonMixins],
    data() {
        return {
            navList: this.outsideParam,
            formCols: [
                [
                    {
                        type: 'el-input',
                        prop: 'msisdn',
                        label: '用户号码：',
                        labelWidth: '85px',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true,
                            class: 'displayPass'
                        },
                        rules: [
                            // { required: true, message: '必填', trigger: 'submit' }
                        ],
                        span: 16,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'tool',
                        prop: 'tool',
                        span: 8,
                        isShow: true,
                        labelWidth: '20px'
                    }
                ]
            ],
            form: {
                msisdn: ''
            },
            gistotalOptions: gistotalOptions,
            gisLoaded: false,
            imgLayer: null
        };
    },
    methods: {
        search() {
            if (!this.gisLoaded) {
                this.$message('地图尚未初始化完成，请稍候重试');
                return;
            }
            if (!this.form.msisdn) {
                this.$message.error('请先输入用户号码！');
                return;
            }
            this.clearGisData();
            this.$exloading1x();
            this.getPost(
                'post',
                'jobsHousing',
                {
                    ...this.form
                },
                '用户实时位置查询',
                (data) => {
                    this.$exloaded1x();
                    if (!data) {
                        this.$message('该查询条件暂无数据，请重试！');
                        return;
                    }
                    let list = {};
                    if (data.workLongitude && data.workLatitude) {
                        list['居住地'] = {
                            lng: data.workLongitude,
                            lat: data.workLatitude,
                            data: '居住地',
                            width: 56,
                            ht: 0.2
                        };
                    }
                    if (data.liveLongitude && data.liveLatitude) {
                        list['工作地'] = {
                            lng: data.liveLongitude,
                            lat: data.liveLatitude,
                            data: '工作地',
                            width: 56,
                            ht: 0.2
                        };
                    }
                    this.drawLocationImg(list);
                }
            );
        },
        reset() {
            this.form.msisdn = '';
            if (this.gisLoaded) {
                this.clearGisData();
            }
        },
        clearGisData() {
            if (this.imgLayer) {
                this.imgLayer.removeAll();
            }
            const GIS = this.$refs['LocationGisMap'].getEntity();
            GIS.layerList.divLayer.removeAll();
        },
        gisOnLoad() {
            const gis = this.$refs['LocationGisMap'].getEntity();
            // 设置底图
            if (getMayType() === 'default') {
                gis.tileLayerList['底图图层'].visible = false;
                //重写getUrl方法
                gis.tileLayerList['高德底图'].getUrl = (x, y, z) => {
                    return `${location.origin}/MapUrl/api/appmaptile?x=${x}&y=${y}&z=${z}`;
                };
            } else {
                gis.tileLayerList['高德底图'] && (gis.tileLayerList['高德底图'].visible = false);
            }

            changeGisColor(gis);

            gis.downLoad.onDownloadFinish.addOneEvent(() => {
                this.gisLoaded = true;
            });
        },
        drawLocationImg(list) {
            if (!list['居住地'] && !list['工作地']) {
                return;
            }
            let data = Object.keys(list).map((item) => {
                return list[item];
            });
            const GIS = this.$refs['LocationGisMap'].getEntity();
            this.imgLayer = new GIS.layer();
            this.imgLayer.name = 'imgTest';
            GIS.gis.scene.add(this.imgLayer.Group);
            this.imgLayer.visible = true;
            //传入图片url获取材质
            let material1 = GIS.meshList.img.getMaterial({
                url: require('../../../img/gis/workPlaceNew.png'),
                opacity: 1
            });
            let material2 = GIS.meshList.img.getMaterial({
                url: require('../../../img/gis/habitationNew.png'),
                opacity: 1
            });
            const habitationData = [list['居住地']];
            const workPlaceData = [list['工作地']];
            habitationData.autoScale = true;
            workPlaceData.autoScale = true;
            //生成模型
            if (list['居住地']) {
                let imgMesh1 = GIS.meshList.img.create(habitationData, material1);
                this.imgLayer.add(imgMesh1);
            }
            if (list['工作地']) {
                let imgMesh2 = GIS.meshList.img.create(workPlaceData, material2);
                this.imgLayer.add(imgMesh2);
            }
            //模型添加进图层
            this.drawingTips(data);
            //更新GIS
            GIS.gis.needUpdate = true;
            GIS.cameraControl.move(data[0]);
            GIS.cameraControl.zoom = 17;
            GIS.cameraControl.zoomByPoints(data, 4);
        },
        drawingTips(data) {
            const GIS = this.$refs['LocationGisMap'].getEntity();
            if (!data.length) {
                return;
            }
            data.forEach((item, index) => {
                const data = {
                    dom: `<div class="tips-wrapper">
                            ${item.data}
                        </div>`,
                    point: {
                        lat: item.lat,
                        lng: item.lng
                    }
                };
                data.autoScale = false;
                GIS.layerList.divLayer.addDiv(data);
            });
        }
    }
};
</script>

<style lang="less" scoped>
@import url('../../../style/custom.less');
.location {
    width: 100%;
    height: 100%;
    .gis {
        width: 100%;
        height: calc(100% - 80px);
    }
}
/deep/.tips-wrapper {
    transform: translate(-50%, -50px);
    min-width: 48px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: #375e8f;
    border-radius: 2px;
    border: 1px solid rgba(104, 146, 191, 0.5);
    font-size: 12px;
    color: #ffffffd9;
    text-align: center;
}
.searchBar {
    width: 510px;
    margin-left: auto;
    /deep/.displayPass {
        .el-input__inner {
            -webkit-text-security: disc !important;
            background-color: transparent;
            border: 1px solid rgba(18, 139, 207, 0.6);
            color: #fff;
        }
    }
    /deep/.el-date-editor {
        background-color: transparent;
        .el-input__inner {
            background-color: transparent;
            border: 1px solid rgba(18, 139, 207, 0.6);
            color: #fff;
        }
    }
}
</style>
