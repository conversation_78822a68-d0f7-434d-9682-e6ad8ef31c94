<template>
    <div class="common-table">
        <el-table
            class="table-dark"
            size="small"
            :data="tableData"
            style="width: 100%"
            :height="height"
            ref="table"
            v-if="!needRefresh"
            :border="border"
            :stripe="stripe"
            highlight-current-row
            @expand-change="expandChange"
            @selection-change="selectionChange"
            @cell-click="cellClick"
        >
            <el-table-column
                ref="column"
                v-for="(column, columnIndex) in columns"
                :label="column.label"
                :prop="column.prop"
                :key="column.columnId || columnIndex"
                :type="column.type"
                :width="column.width"
                :minWidth="column.minWidth"
                :sortable="column.sortable"
                :align="column.align"
                :fixed="column.fixed"
            >
                <template
                    v-for="slotName in column.slots || []"
                    :slot="slotName"
                    slot-scope="scope"
                >
                    <slot :name="`column-${slotName}`" :scope="scope">
                        <template>
                            {{ scope.row[column.prop] }}
                        </template>
                    </slot>
                </template>
            </el-table-column>
        </el-table>
        <div class="paging">
            <el-pagination
                class="pagination-dark"
                popper-class="pagination-size-dark popover-dark"
                v-if="isPage"
                background
                :layout="layout"
                :total="total"
                :current-page="currentPage"
                :page-sizes="pageSizes"
                :page-size="pageSize"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            >
            </el-pagination>
        </div>
    </div>
</template>

<script>
export default {
    name: 'commonTable',
    props: {
        columns: {
            type: Array,
            default: () => []
        },
        tableData: {
            type: Array,
            default: () => []
        },
        layout: {
            type: String,
            default: 'prev, pager, next, sizes, jumper, total'
        },
        total: {
            type: Number,
            default: 0
        },
        currentPage: {
            type: Number,
            default: 1
        },
        pageSizes: { type: Array, default: () => [10, 20, 30, 50] },
        pageSize: {
            type: Number,
            default: 10
        },
        isPage: {
            type: Boolean,
            default: true
        },
        height: {
            type: [String, Number],
            default: '100%'
        },
        border: {
            type: Boolean,
            default: false
        },
        stripe: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            needRefresh: false
        };
    },
    mounted() {
        this._table = this.$refs.table;
        const targetComp = this.$refs.table.$children.find(
            (item) => item.$el.className === 'el-table__body'
        );
        targetComp.onColumnsChange = (layout) => {
            const cols = targetComp.$el.querySelectorAll('colgroup > col');
            if (!cols.length) return;
            const flattenColumns = layout.getFlattenColumns();
            const columnsMap = {};
            flattenColumns.forEach((column) => {
                columnsMap[column.id] = column;
            });
            for (let i = 0, j = cols.length; i < j; i++) {
                const col = cols[i];
                const name = col.getAttribute('name');
                const column = columnsMap[name];
                if (column) {
                    col.setAttribute('width', column.realWidth || column.width);
                }
            }
            const targetIndex = this.columns.findIndex((item) => item.type === 'expand');

            this.$nextTick(() => {
                this.$el.querySelectorAll('colgroup').forEach((item) => {
                    const targetEl = item.querySelector(
                        `col[name^="${this._table.tableId}_"]:nth-child(${targetIndex + 1})`
                    );
                    targetEl && targetEl.setAttribute('width', 0);
                });
            });
        };
    },

    methods: {
        handleSizeChange(val) {
            this.$emit('size-change', val);
        },
        handleCurrentChange(val) {
            this.$emit('current-change', val);
        },
        expandChange() {
            this.$emit('expand-change', { ...arguments });
        },
        selectionChange(selection) {
            this.$emit('selection-change', selection);
        },
        cellClick(val) {
            this.$emit('cell-click', val);
        }
    }
};
</script>
<style lang="less" scoped>
@import url('../../style/custom.less');
.common-table {
    display: flex;
    flex-flow: column;
    flex: 1;

    /deep/ .el-table {
        flex: 1;

        .el-table__expand-column {
            overflow: hidden;
        }

        // th {
        //     background: rgba(246, 247, 250, 1);
        // }

        .el-table__expanded-cell[class*='cell'] {
            padding: 0;
            background: rgba(246, 247, 250, 1);
        }
    }
    .paging {
        margin-top: 16px;
    }
}
</style>
