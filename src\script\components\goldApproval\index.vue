<template>
    <div class="gold-approval-mask" v-if="dialogVisible" @click.self="null">
        <div
            class="gold-approval-container"
            :class="{ 'gold-approval-container-2x': currentStep === 1 }"
        >
            <header class="gold-approval-header">
                <span class="gold-approval-title">{{ title }}</span>
            </header>
            <i class="el-icon-close gold-approval-close" @click="handleClose"></i>
            <main class="gold-approval-main">
                <div class="gold-approval-main-content">
                    <div class="gold-approval-steps">
                        <div
                            v-for="(item, index) in approvalSteps"
                            :key="index"
                            class="gold-approval-step-wrapper"
                        >
                            <div
                                class="gold-approval-step"
                                :class="{
                                    process: item.status === 'process',
                                    finish: item.status === 'finish'
                                }"
                            ></div>
                            <div
                                v-if="index < approvalSteps.length - 1"
                                class="gold-approval-step-line"
                                :class="{
                                    process: item.status === 'process',
                                    finish: item.status === 'finish'
                                }"
                            ></div>
                        </div>
                    </div>
                    <!-- 步骤1：审批人选择和理由 -->
                    <template v-if="currentStep === 1">
                        <div class="gold-approval-main-layout">
                            <div class="gold-approval-left-panel">
                                <el-select
                                    v-model="approverValue"
                                    placeholder="审批人选择"
                                    class="gold-approval-approver-select"
                                    popper-class="gold-approval-select-popper"
                                >
                                    <template slot="prefix">
                                        <img
                                            src="@/img/goldApproval/user-icon.png"
                                            alt="user-icon"
                                            class="gold-approval-user-icon"
                                        />
                                    </template>
                                    <el-option
                                        v-for="item in approverOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                </el-select>
                                <div class="gold-approval-apply-reason">
                                    <div class="apply-reason-sentence">
                                        <span class="apply-reason-text">因</span>
                                        <el-autocomplete
                                            v-model="reasonValue"
                                            :fetch-suggestions="queryReasonSuggestions"
                                            placeholder="请输入申请原因"
                                            class="apply-reason-select"
                                            popper-class="gold-approval-autocomplete-popper"
                                            clearable
                                            @select="handleReasonSelect"
                                        ></el-autocomplete>
                                        <span class="apply-reason-text">原因</span>
                                        <span class="apply-reason-text reason-text-tip"
                                            >（下拉选择或输入）
                                        </span>
                                        <div class="apply-reason-sentence">
                                            <span class="apply-reason-text">需操作</span>
                                            <el-input
                                                placeholder="请输入系统名称或文件名称"
                                                class="apply-reason-input system-input"
                                                v-model.trim="operationSystem"
                                            >
                                            </el-input>
                                            <span class="apply-reason-text">系统或文件，</span>
                                            <span class="apply-reason-text">涉及</span>
                                            <el-input
                                                placeholder="数据级别，只能输入数字，如3"
                                                class="apply-reason-input level-input"
                                                v-model.trim="dataLevel"
                                                @change="handleDataLevelChange"
                                            >
                                            </el-input>
                                            <span class="apply-reason-text">级数据或文件， </span>
                                        </div>
                                        <div class="apply-reason-sentence">
                                            <span class="apply-reason-text">需要进行 </span>
                                            <el-input
                                                placeholder="操作类型，如查询、下载等"
                                                class="apply-reason-input operation-input"
                                                v-model.trim="requiredOperation"
                                            >
                                            </el-input>
                                            <span class="apply-reason-text">操作，</span>
                                        </div>
                                        <div class="apply-reason-sentence need-single-line">
                                            <span class="apply-reason-text"
                                                >特此进行金库申请，请审批
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="gold-approval-right-panel">
                                <div class="gold-approval-instructions">
                                    <h3 class="instructions-title">填写说明</h3>
                                    <div class="instructions-content">
                                        <p>
                                            1、请详细说明申请原因（例如写明因版本割接、重要操作、需求来源、文件名、文件记录条数、包含的字段、是否涉及客户敏感信息、是否二次转发他人使用、特殊场景的变更申请工单号或审批单等信息），操作的具体系统或文件名，操作库、表、文件的具体级别，操作的具体动作（例如查询、删除），申请原因字段的总字数（包含预置的文字）不超过450个中文字符。
                                        </p>
                                        <p>
                                            2、金库审批短信和金库申请日志的“申请理由”中“数据级别”字段取值说明，如是数据库资源且触发的金库是精确匹配时，数据级别字段优先取该资源该库表在细粒度对应资源对应库表的数据级别，不取用户填写的数据级别；非精确匹配或非数据库资源触发金库则直接取用户填写的数据级别；
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>

                    <!-- 步骤2：验证码 -->
                    <template v-if="currentStep === 2">
                        <el-input
                            placeholder="输入验证码"
                            class="gold-approval-verify-code"
                            v-model.trim="verifyCode"
                        >
                            <template slot="prefix">
                                <img
                                    src="@/img/goldApproval/safe-icon.png"
                                    alt="safe-icon"
                                    class="gold-approval-safe-icon"
                                />
                            </template>
                            <template slot="suffix">
                                <span
                                    class="gold-approval-code__msg"
                                    :class="{ resend: isResend }"
                                    @click="handleResend"
                                >
                                    {{ isResend ? '重新发送' : '金库验证码已发送' }}
                                </span>
                            </template>
                        </el-input>
                        <div
                            class="gold-approval-verify-result"
                            v-if="verifySubmitted && verifyResultReceived"
                        >
                            <i
                                class="gold-approval-verify-result-icon"
                                :class="isVerifySuccess ? 'el-icon-success' : 'el-icon-warning'"
                            ></i>
                            <span class="gold-approval-verify-result-text"
                                >{{ isVerifySuccess ? '验证成功' : '验证失败' }}
                            </span>
                        </div>
                    </template>

                    <el-button
                        type="primary"
                        class="gold-approval-submit-btn"
                        @click="handleSubmit"
                        :disabled="isSubmitDisabled"
                        :loading="loading"
                    >
                        {{ submitBtnText }}
                    </el-button>
                </div>
            </main>
        </div>
    </div>
</template>

<script>
/**
 * 金库审批弹窗
 * 1、在src\script\api\module\regionResource.js中添加需要审批的接口配置
 * 2、将请求接口原本的getPost方法改为getPostGoldApproval方法，用法一致
 */
import { request } from '@/script/utils/request.js';
import goldApprovalService from '@/script/api/module/goldApproval.js';

export default {
    name: 'GoldApproval',
    props: {
        // 弹窗标题
        title: {
            type: String,
            default: '金库审批'
        },
        ids: {
            type: Object,
            default: () => ({
                assetId: '',
                sceneId: ''
            })
        }
    },
    data() {
        return {
            dialogVisible: true,
            currentStep: 1,
            loading: false, // 按钮loading状态
            approvalSteps: [
                {
                    status: 'process'
                },
                {
                    status: 'wait'
                }
            ],
            // 验证码相关
            verifyCode: '',
            isResend: true,
            verifySubmitted: false,
            verifyResultReceived: false,
            // 审批人选择
            approverValue: '',
            approverOptions: [],
            // 申请原因相关
            reasonValue: '',
            reasonOptions: [
                { label: '查询数据', value: '查询数据' },
                { label: '业务工作需要', value: '业务工作需要' },
                { label: '日志查看', value: '日志查看' },
                { label: '漏洞修复', value: '漏洞修复' },
                { label: '处理工单', value: '处理工单' },
                { label: '其他', value: '其他' }
            ],
            operationSystem: '',
            dataLevel: '',
            requiredOperation: '',
            // 接口返回数据存储
            pantheonSessionId: '', // 第一步接口返回
            pantheonSerialNum: '', // 第二步接口返回
            isVerifySuccess: false
        };
    },
    watch: {
        dialogVisible: {
            handler(val) {
                if (val) {
                    this.getGoldSceneStatus();
                }
            },
            immediate: true
        }
    },
    computed: {
        // 按钮文本
        submitBtnText() {
            if (this.currentStep === 2) {
                return '确定';
            }
            return '下一步';
        },
        // 是否禁用提交按钮
        isSubmitDisabled() {
            if (this.currentStep === 1) {
                return (
                    !this.approverValue ||
                    !this.reasonValue ||
                    !this.operationSystem ||
                    !this.dataLevel ||
                    !this.requiredOperation
                );
            } else if (this.currentStep === 2) {
                return !this.verifyCode || (this.verifySubmitted && this.isVerifySuccess);
            }
            return false;
        },
        userEnName() {
            return frameService.getUser().name; //英文名
        }
    },
    methods: {
        // 只能输入数字（正整数）
        handleDataLevelChange(val) {
            if (!/^\d+$/.test(val)) {
                this.dataLevel = '';
            }
        },
        // 查询申请原因建议
        queryReasonSuggestions(queryString, callback) {
            let suggestions;
            if (queryString) {
                suggestions = this.reasonOptions.filter((item) => {
                    return item.label.toLowerCase().includes(queryString.toLowerCase());
                });
            } else {
                suggestions = this.reasonOptions;
            }
            callback(suggestions);
        },
        // 选择申请原因
        handleReasonSelect(item) {
            this.reasonValue = item.value;
        },
        // 关闭弹窗
        handleClose() {
            this.dialogVisible = false;
            this.$emit('close');
        },
        // 重新发送验证码
        handleResend() {
            if (this.isResend) {
                this.sendVerifyCode();
                // 清除验证结果
                this.verifySubmitted = false;
                this.verifyResultReceived = false;
                this.verifyCode = '';
            }
        },
        // 发送验证码
        sendVerifyCode() {
            this.isResend = false;
            this.applyGoldSceneRemote();
            setTimeout(() => {
                this.isResend = true;
            }, 10000); // 10秒后可重新发送
        },
        // 提交按钮点击事件
        async handleSubmit() {
            this.loading = true;
            try {
                if (this.currentStep === 1) {
                    // 第二步：申请金库场景远程授权
                    await this.applyGoldSceneRemote();
                } else if (this.currentStep === 2) {
                    // 第三步：金库场景授权二次短信认证
                    await this.submitVerifyCode();
                }
            } catch (error) {
                console.error('请求出错：', error);
            } finally {
                this.loading = false;
            }
        },
        // 切换步骤,更新步骤状态
        goToStep(step) {
            this.currentStep = step;
            this.approvalSteps = this.approvalSteps.map((item, index) => {
                if (index + 1 < step) {
                    return { status: 'finish' };
                } else if (index + 1 === step) {
                    return { status: 'process' };
                }
                return { status: 'wait' };
            });
        },
        async getGoldSceneStatus() {
            const params = {
                sceneId: this.ids.sceneId,
                assetId: this.ids.assetId,
                loginUser: this.userEnName
            };
            await this.getPost(
                'post',
                goldApprovalService.goldSceneStatus,
                params,
                '金库场景状态查询',
                (data) => {
                    // 处理审批人列表
                    this.approverOptions = data.approveUserList.map((item) => ({
                        label: item.approveUserAccount,
                        value: item.approveUserAccount
                    }));
                    this.pantheonSessionId = data.pantheonSessionId;
                    this.goToStep(1);
                }
            );
        },
        // 1、申请金库场景远程授权
        async applyGoldSceneRemote() {
            // 组合申请原因
            const fullReason = `因${this.reasonValue}原因, 需操作${this.operationSystem}系统或文件，涉及${this.dataLevel}级数据或文件， 需要进行${this.requiredOperation}操作，特此进行金库申请，请审批`;

            const params = {
                sceneId: this.ids.sceneId,
                assetId: this.ids.assetId,
                pantheonSessionId: this.pantheonSessionId,
                loginUser: this.userEnName,
                account: this.approverValue,
                approveUserAccount: this.userEnName,
                applyReason: fullReason,
                authMaxTime: '1'
            };
            await this.getPost(
                'post',
                goldApprovalService.applyGoldSceneRemote,
                params,
                '申请金库场景远程授权',
                (data) => {
                    this.pantheonSerialNum = data.pantheonSerialNum;
                    this.goToStep(2);
                }
            );
        },
        // 2、提交验证码
        async submitVerifyCode() {
            this.verifySubmitted = true;
            this.verifyResultReceived = false;

            const params = {
                authCode: this.verifyCode,
                pantheonSerialNum: this.pantheonSerialNum
            };

            await this.getPost(
                'post',
                goldApprovalService.secondCodeVerify,
                params,
                '金库场景授权二次短信认证',
                (data) => {
                    this.verifyResultReceived = true;
                    this.isVerifySuccess = data.resultCode === '1';

                    if (this.isVerifySuccess) {
                        this.$emit('success', {
                            sessionId: this.pantheonSessionId
                        });
                        setTimeout(() => {
                            this.handleClose();
                        }, 1000);
                    } else {
                        this.isResend = true;
                    }
                }
            );
        },
        /**
         * 接口请求封装
         * @param method 请求方式
         * @param postName 接口名
         * @param _param 入参
         * @param errContent 错误提示内容
         * @param callBack 成功逻辑业务代码callBack
         * @param catchCallback catch时回调
         */
        async getPost(method, postName, _param, errContent, callBack, catchCallback) {
            await request(method, postName, _param)
                .then((rcvData) => {
                    this.$exloaded1x();
                    //此处成功逻辑业务代码
                    if (typeof rcvData === 'string') {
                        this.$message(rcvData);
                        return;
                    }
                    callBack && callBack(rcvData);
                })
                .catch((err) => {
                    catchCallback && catchCallback(); //失败回调
                    this.$exloaded1x();
                    let options = {
                        title: '消息提示',
                        content: errContent + '接口请求失败！',
                        detail: `详细内容：${err.errorMessage || err}`
                    };
                    this.$popupMessageWindow(options);
                });
        }
    }
};
</script>

<style lang="less" scoped>
.gold-approval-mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}
.scrollbar-custom {
    --scrollbar-width: 4px;
    --scrollbar-height: 4px;
    --scrollbar-thumb-color: #5c6f92;
    &::-webkit-scrollbar {
        width: var(--scrollbar-width);
        height: var(--scrollbar-height);
    }
    &::-webkit-scrollbar-thumb {
        border-radius: 10px;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
        background: var(--scrollbar-thumb-color);
    }
    &::-webkit-scrollbar-track {
        /* 滚动条里面轨道 */
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
        border-radius: 10px;
        background: transparent;
    }
    &::-webkit-scrollbar-corner {
        background: rgba(0, 0, 0, 0);
    }
}

@width-1x: 520px;
@height-1x: 349px;
@bg-1x: url('~@/img/goldApproval/gold-approval-bg.png');
@bg-2x: url('~@/img/goldApproval/gold-approval-bg-2x.png');
@header-height-1x: 24px;
@close-top-1x: 45px;
@close-size-1x: 20px;
@btn-width: 360px;

.gold-approval-container {
    --width: @width-1x;
    --height: @height-1x;
    --bg: @bg-1x;
    --header-height: @header-height-1x;
    --close-top: @close-top-1x;
    --close-size: @close-size-1x;
    --btn-width: @btn-width;

    width: var(--width);
    height: var(--height);
    background-image: var(--bg);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    padding-top: 5px;
    display: flex;
    flex-direction: column;
    position: relative;
    transition: all 0.3s ease-in-out;
    &.gold-approval-container-2x {
        --width: calc(@width-1x * 1.5);
        --height: calc(@height-1x * 1.5);
        --bg: @bg-2x;
        --header-height: calc(@header-height-1x * 2);
        --close-top: calc(@close-top-1x * 1.5);
        --close-size: calc(@close-size-1x * 1.5);
        --btn-width: 100%;
    }

    .gold-approval-header {
        display: flex;
        align-items: center;
        justify-content: center;
        height: var(--header-height);
        flex: none;
        .gold-approval-title {
            font-weight: 500;
            font-size: 16px;
            color: #ffffff;
            line-height: 24px;
            letter-spacing: 1px;
        }
    }
    .gold-approval-close {
        position: absolute;
        right: var(--close-size);
        top: var(--close-top);
        font-size: var(--close-size);
        font-weight: 500;
        cursor: pointer;
        color: #acc2d3;
        &:hover {
            color: #f74041;
        }
    }
    .gold-approval-main {
        min-height: 0;
        flex: 1;
        padding: 40px var(--close-size) 50px;
        &-content {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 24px;
            padding: 0 48px;

            .gold-approval-main-layout {
                width: 100%;
                display: flex;
                gap: 20px;
                min-height: 0;
                flex: 1;

                .gold-approval-left-panel {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    gap: 15px;
                }

                .gold-approval-right-panel {
                    width: 30%;
                }
            }
            .gold-approval-steps {
                width: 44%;
                display: flex;
                align-items: center;
                margin-bottom: 10px;

                .gold-approval-step-wrapper {
                    display: flex;
                    align-items: center;
                    flex: 1;

                    &:last-child {
                        flex: 0;
                    }

                    .gold-approval-step {
                        width: 9px;
                        height: 9px;
                        border-radius: 50%;
                        background: rgba(0, 149, 255, 0.5);
                        flex-shrink: 0;

                        &.process {
                            background: rgba(1, 230, 254, 1);
                            position: relative;
                            &::before {
                                content: '';
                                position: absolute;
                                width: 21px;
                                height: 21px;
                                border-radius: 50%;
                                background: rgba(2, 244, 255, 0.2);
                                left: 50%;
                                top: 50%;
                                transform: translate(-50%, -50%);
                                animation: ripple 1.5s ease-in-out infinite;
                            }

                            @keyframes ripple {
                                0% {
                                    width: 9px;
                                    height: 9px;
                                    opacity: 1;
                                }
                                70% {
                                    width: 18px;
                                    height: 18px;
                                    opacity: 0.9;
                                }
                                100% {
                                    width: 21px;
                                    height: 21px;
                                    opacity: 0.2;
                                }
                            }
                        }
                        &.finish {
                            background: rgba(1, 230, 254, 1);
                        }
                    }

                    .gold-approval-step-line {
                        height: 1px;
                        flex: 1;
                        background: rgba(0, 149, 255, 0.2);

                        &.process {
                            background: rgba(1, 230, 254, 0.2);
                        }

                        &.finish {
                            background: rgba(1, 230, 254, 0.8);
                        }
                    }
                }
            }
            .gold-approval-approver-select {
                align-self: flex-start;
                width: 100%;
                /deep/&.el-select {
                    height: 40px;
                    background: rgba(0, 40, 85, 0.6);
                    border-radius: 4px;
                    .el-input__inner {
                        background: transparent;
                        font-weight: 500;
                        font-size: 14px;
                        color: #ffffff;
                        line-height: 16px;
                        border: 1px solid rgba(18, 139, 207, 0.6);
                        &::placeholder {
                            font-weight: 400;
                            font-size: 14px;
                            color: rgba(255, 255, 255, 0.45);
                            line-height: 16px;
                        }
                        &:focus {
                            box-shadow: 0px 4px 20px 0px #004e9e;
                        }
                    }
                    .el-input__prefix {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        left: 7px;
                        .gold-approval-user-icon,
                        .gold-approval-company-icon {
                            width: 16px;
                            height: 16px;
                        }
                    }
                    .el-input__suffix-inner {
                        .el-select__caret {
                            color: rgba(0, 146, 250, 1);
                        }
                    }
                }
            }
            .gold-approval-apply-reason {
                width: 100%;
                min-height: 0;
                flex: 1;
                background: rgba(0, 40, 85, 0.6);
                border-radius: 4px;
                font-weight: 500;
                font-size: 14px;
                color: #ffffff;
                line-height: 16px;
                border: 1px solid rgba(18, 139, 207, 0.6);
                padding: 16px;
                padding-right: 12px;
                display: flex;
                flex-direction: column;
                flex-wrap: wrap;

                .apply-reason-sentence {
                    display: flex;
                    flex-wrap: wrap;
                    align-items: center;
                    gap: 5px;
                    line-height: 32px;
                    overflow-y: auto;
                    padding-right: 4px;
                    .scrollbar-custom();

                    &.need-single-line {
                        flex-basis: 100%;
                    }

                    .apply-reason-text {
                        font-size: 14px;
                        color: #d1e4ff;
                        white-space: nowrap;
                        &.reason-text-tip {
                            font-size: 12px;
                            color: #00ceff;
                        }
                    }

                    .apply-reason-select {
                        width: 150px;
                        display: inline-flex;
                    }

                    .apply-reason-input {
                        display: inline-flex;

                        &.system-input {
                            width: 200px;
                        }

                        &.level-input {
                            width: 240px;
                        }

                        &.operation-input {
                            width: 200px;
                        }
                    }

                    .apply-reason-select,
                    .apply-reason-input {
                        /deep/&.el-autocomplete,
                        /deep/&.el-input {
                            height: 32px;
                            background: rgba(0, 40, 85, 0.6);
                            border-radius: 4px;

                            .el-input__inner {
                                height: 32px;
                                line-height: 32px;
                                background: transparent;
                                font-weight: 500;
                                font-size: 14px;
                                color: #ffffff;
                                border: 1px solid rgba(18, 139, 207, 0.6);
                                &::placeholder {
                                    font-weight: 400;
                                    font-size: 14px;
                                    color: rgba(255, 255, 255, 0.45);
                                }
                                &:focus {
                                    box-shadow: 0px 4px 20px 0px #004e9e;
                                }
                            }
                            .el-input__suffix {
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                &-inner {
                                    .el-select__caret {
                                        color: rgba(0, 146, 250, 1);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            .gold-approval-instructions {
                height: 100%;
                background: rgba(0, 40, 85, 0.6);
                border: 1px solid rgba(18, 139, 207, 0.6);
                border-radius: 4px;
                padding: 12px;
                padding-right: 8px;
                display: flex;
                flex-direction: column;

                .instructions-title {
                    font-size: 14px;
                    font-weight: 500;
                    color: #00ceff;
                    margin: 0 0 10px 0;
                }

                .instructions-content {
                    min-height: 0;
                    flex: 1;
                    overflow-y: auto;
                    padding-right: 4px;
                    p {
                        font-size: 12px;
                        color: #d1e4ff;
                        line-height: 1.5;
                        margin: 0 0 8px 0;
                        text-align: justify;
                    }
                    .scrollbar-custom();
                }
            }
            .gold-approval-verify-code {
                width: 360px;
                height: 40px;
                background: rgba(0, 40, 85, 0.6);
                border-radius: 4px;

                /deep/.el-input__inner {
                    background: transparent;
                    font-weight: 500;
                    font-size: 14px;
                    color: #ffffff;
                    line-height: 16px;
                    border: 1px solid rgba(18, 139, 207, 0.6);
                    &::placeholder {
                        font-weight: 400;
                        font-size: 14px;
                        color: rgba(255, 255, 255, 0.45);
                        line-height: 16px;
                    }
                    &:focus {
                        box-shadow: 0px 4px 20px 0px #004e9e;
                    }
                }
                /deep/&:has(.gold-approval-code__msg) .el-input__inner {
                    padding-right: 132px;
                }
                /deep/&:has(.gold-approval-code__msg.resend) .el-input__inner {
                    padding-right: 76px;
                }

                /deep/.el-input__prefix {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    left: 7px;
                    .gold-approval-edit-icon {
                        width: 16px;
                        height: 16px;
                    }
                }
                /deep/.el-input__suffix:has(.gold-approval-code__msg) {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    right: 10px;
                    .el-input__suffix-inner {
                        display: flex;
                    }
                    .gold-approval-code__msg {
                        font-weight: 400;
                        font-size: 14px;
                        color: #d1e4ff;
                        cursor: default;
                        &.resend {
                            color: #00ceff;
                            cursor: pointer;
                            &:hover {
                                opacity: 0.8;
                            }
                        }
                    }
                }
                &.select-reason-error {
                    /deep/.el-input__inner {
                        border: 1px solid #ff4949;
                    }
                    &::before {
                        content: '* 理由长度需大于8个字符';
                        position: absolute;
                        bottom: -20px;
                        left: 0;
                        font-size: 12px;
                        color: #ff4949;
                    }
                }
            }
            .gold-approval-verify-result {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 6px;
                &:has(.el-icon-success) {
                    .gold-approval-verify-result-icon,
                    .gold-approval-verify-result-text {
                        color: #36da54;
                    }
                }
                &:has(.el-icon-warning) {
                    .gold-approval-verify-result-icon,
                    .gold-approval-verify-result-text {
                        color: #ff3030;
                    }
                }
                .gold-approval-verify-result-icon,
                .gold-approval-verify-result-text {
                    font-weight: 400;
                    font-size: 14px;
                }
            }
            .gold-approval-submit-btn {
                /deep/&.el-button {
                    transition: all 0.3s ease-in-out;
                    width: var(--btn-width);
                    height: 40px;
                    border-radius: 4px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-top: auto;
                    &--primary {
                        background-color: #0095ff;
                        border-color: #0095ff;
                        &:hover,
                        &:focus {
                            background-color: darken(#0095ff, 10%);
                            border-color: darken(#0095ff, 10%);
                        }

                        &:active {
                            background-color: darken(#0095ff, 20%);
                            border-color: darken(#0095ff, 20%);
                        }

                        &.is-disabled,
                        &.is-disabled:hover,
                        &.is-disabled:focus,
                        &.is-disabled:active {
                            background-color: #0095ff;
                            border-color: #0095ff;
                            opacity: 0.4;
                        }
                    }
                }
            }
        }
    }
}
</style>
<style lang="less">
.gold-approval-select-popper {
    background: rgba(0, 10, 38, 0.98);
    box-shadow: 0px 4px 20px 0px #004e9e;
    border-radius: 4px;
    border: 1px solid rgba(18, 139, 207, 0.6);
    max-width: 720px;
    overflow: auto;
    &[x-placement^='bottom'] .popper__arrow {
        border-bottom-color: rgba(18, 139, 207, 0.6);
        &::after {
            border-bottom-color: rgba(0, 10, 38, 0.98);
        }
    }
    .el-select-dropdown__item {
        font-weight: 400;
        font-size: 14px;
        color: #d1e4ff;
        &:last-child {
            margin-bottom: 10px;
        }
        &.selected:not(.is-disabled) {
            font-weight: 400;
            font-size: 14px;
            color: #00ceff;
            background-color: rgba(0, 149, 255, 0.15);
        }
        &.hover {
            background-color: rgba(0, 149, 255, 0.15);
        }
        &:hover {
            background-color: rgba(0, 149, 255, 0.15);
        }
    }
    // 滚动条调成透明
    .el-scrollbar__wrap {
        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        &::-webkit-scrollbar-track {
            box-shadow: inset 0 0 5px transparent;
            border-radius: 10px;
            background: transparent;
        }
    }
}

.gold-approval-autocomplete-popper {
    background: rgba(0, 10, 38, 0.98);
    box-shadow: 0px 4px 20px 0px #004e9e;
    border-radius: 4px;
    border: 1px solid rgba(18, 139, 207, 0.6);
    max-width: 720px;
    overflow: auto;
    &[x-placement^='bottom'] .popper__arrow {
        border-bottom-color: rgba(18, 139, 207, 0.6);
        &::after {
            border-bottom-color: rgba(0, 10, 38, 0.98);
        }
    }
    &.el-autocomplete-suggestion li {
        font-weight: 400;
        font-size: 14px;
        color: #d1e4ff;
    }
    &.el-autocomplete-suggestion.is-loading li:hover {
        background-color: rgba(0, 149, 255, 0.15);
    }
    &.el-autocomplete-suggestion li.highlighted,
    &.el-autocomplete-suggestion li:hover {
        color: #00ceff;
        background-color: rgba(0, 149, 255, 0.15);
    }
    // 滚动条调成透明
    .el-scrollbar__wrap {
        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        &::-webkit-scrollbar-track {
            box-shadow: inset 0 0 5px transparent;
            border-radius: 10px;
            background: transparent;
        }
    }
}
</style>
