/* eslint-disable complexity */
// 组件配置

const widgetConfig =(name,vm) => {
    // const {provinceList,dataTypeList,realOfflineList,netTypeList} =vm.$store.state.devopsEnumList;
    // const granularityEnum = vm.$store.state.granularityEnum;
    const configList ={
        signalQuality:{
            widgetId:'signalQuality',
            widgetParams:{
                province:'QG',
                dataType: 1,
                type: 3,
                startTime:'',
                endTime:'',
                time:'',
                relativeTime:1440,
                refresh:5,
                granularity:2,
                minGranularity: 1,
                minLimitation: 1000 ,
            }
        },
        parameterQuality:{
            widgetId:'parameterQuality',
            widgetParams:{
                province:'QG',
                dataType: 1,
                type: 3,
                netType: 1,
                startTime:'',
                endTime:'',
                time:'',
                relativeTime:10080,
                refresh:5,
                granularity:4,
                minGranularity: 4,
                minLimitation:1000 ,
            }
        },
        traceabilityQuality:{
            widgetId:'traceabilityQuality',
            widgetParams:{
                startTime:'',
                endTime:'',
                time:'',
                relativeTime:360,
                refresh:5,
                granularity:1,
                minGranularity: 1,
                minLimitation:1000 ,
            }
        },
        zipperQuality:{
            widgetId:'zipperQuality',
            widgetParams:{
                startTime:'',
                endTime:'',
                time:'',
                relativeTime:1440,
                minRelativeTime:1440,
                refresh:5,
                granularity:2,
                minGranularity: 2,
                minLimitation:1000 ,
            }
        }
    };
    return configList[name];
};

const widgetNameList={
    signalQuality:'信令质量',
    parameterQuality:'工参质量',
    traceabilityQuality:'溯源质量',
    zipperQuality:'拉链质量',
};

export {
    widgetConfig,
    widgetNameList,
};