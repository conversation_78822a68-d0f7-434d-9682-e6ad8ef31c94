<template>
    <div class="dark-table">
        <!-- 空数据展示 -->
        <el-empty
            v-if="!tableData || tableData.length === 0"
            description="暂无数据"
            :image="require('../../img/noDataMon.png')"
        >
        </el-empty>
        <template v-else>
            <!-- 表格头部 -->
            <div class="table-header" :class="{ 'has-scrollbar': hasScrollbar }">
                <div class="header-cell rank-cell">序号</div>
                <div
                    v-for="(column, index) in columns"
                    :key="index"
                    class="header-cell"
                    :style="getColumnStyle(column)"
                >
                    {{ column.label }}
                </div>
            </div>

            <!-- 表格主体（带滚动） -->
            <div class="table-body" ref="tableBody" @scroll="checkScrollbar">
                <div
                    v-for="(row, rowIndex) in tableData"
                    :key="rowIndex"
                    class="table-row"
                    :class="{
                        'row-hover': hoveredRow === rowIndex,
                        'row-selected': selectedRow === rowIndex
                    }"
                    @mouseenter="hoveredRow = rowIndex"
                    @mouseleave="hoveredRow = null"
                    @click="handleRowClick(rowIndex)"
                >
                    <div class="table-cell rank-cell">
                        <div
                            class="rank-circle"
                            :class="rowIndex < 3 ? `rank-circle-${rowIndex}` : ''"
                        >
                            {{ getRowNumber(rowIndex) }}
                        </div>
                    </div>
                    <div
                        v-for="(column, colIndex) in columns"
                        :key="colIndex"
                        class="table-cell"
                        :title="row[column.prop]"
                        :style="getColumnStyle(column)"
                    >
                        {{ row[column.prop] }}
                    </div>
                </div>
            </div>
        </template>
    </div>
</template>

<script>
export default {
    name: 'dark-table',
    props: {
        columns: {
            type: Array,
            default: () => []
        },
        tableData: {
            type: Array,
            default: () => []
        },
        defaultSelectedRow: {
            type: [Number, Object],
            default: null
        },
        useDataIndex: {
            type: Boolean,
            default: true
        },
        scrollbarWidth: {
            type: Number,
            default: 6 // 默认滚动条宽度，与CSS匹配
        }
    },
    data() {
        return {
            hoveredRow: null,
            selectedRow: null,
            hasScrollbar: false
        };
    },
    watch: {
        // 监听默认选中行的变化
        defaultSelectedRow: {
            handler(newVal) {
                if (newVal !== null) {
                    this.selectedRow = newVal;
                }
            },
            immediate: true
        }
    },
    mounted() {
        if (this.defaultSelectedRow !== null) {
            this.selectedRow = this.defaultSelectedRow;
        }

        // 初始检查滚动条并添加调整大小观察器
        this.$nextTick(() => {
            this.checkScrollbar();

            // 添加调整大小观察器以检测表格大小变化
            if (typeof ResizeObserver !== 'undefined') {
                this.resizeObserver = new ResizeObserver(() => {
                    this.checkScrollbar();
                });
                // 确保tableBody存在才添加观察
                if (this.$refs.tableBody) {
                    this.resizeObserver.observe(this.$refs.tableBody);
                }
            }

            // 同时在窗口调整大小时检查
            window.addEventListener('resize', this.checkScrollbar);
        });
    },
    beforeDestroy() {
        // 清理监听器
        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
        }
        window.removeEventListener('resize', this.checkScrollbar);
    },
    methods: {
        handleRowClick(rowIndex) {
            this.selectedRow = rowIndex;
            this.$emit('row-click', this.tableData[rowIndex]);
        },
        getRowNumber(rowIndex) {
            if (rowIndex < 3) {
                return '';
            }
            return rowIndex + 1;
        },
        getColumnStyle(column) {
            const style = {};

            if (column.width) {
                style.width = typeof column.width === 'number' ? `${column.width}px` : column.width;
                style.flex = 'none';
            } else if (column.minWidth) {
                style.minWidth =
                    typeof column.minWidth === 'number' ? `${column.minWidth}px` : column.minWidth;
            }

            if (column.align) {
                style.textAlign = column.align;
            }

            return style;
        },
        checkScrollbar() {
            if (this.$refs.tableBody) {
                const { scrollHeight, clientHeight } = this.$refs.tableBody;
                this.hasScrollbar = scrollHeight > clientHeight;
            } else {
                this.hasScrollbar = false;
            }
        }
    }
};
</script>

<style lang="less" scoped>
.dark-table {
    width: 100%;
    height: 100%;
    color: #fff;
    display: flex;
    flex-direction: column;

    .table-header {
        display: flex;
        background: #093e79;
        border-radius: 4px 4px 0px 0px;
        line-height: 36px;
        min-height: 36px;

        &.has-scrollbar {
            padding-right: 10.4px;
        }

        .header-cell {
            flex: 1;
            padding-left: 16px;
            text-align: left;
            font-weight: bold;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-family: 'PingFangSC, PingFang SC';
        }

        .rank-cell {
            width: 60px;
            flex: none;
            padding-left: 0;
            text-align: center;
        }
    }

    .table-body {
        height: 0;
        flex: 1;
        overflow-y: auto;
        box-shadow: inset 0px -1px 0px 0px rgba(64, 155, 255, 0.3);
        scrollbar-width: thin;
        scrollbar-color: rgba(0, 149, 255, 0.6) rgba(9, 62, 121, 0.3);

        &::-webkit-scrollbar {
            width: 6px;
            background-color: rgba(9, 62, 121, 0.3);
        }

        &::-webkit-scrollbar-thumb {
            background-color: rgba(0, 149, 255, 0.6);
            border-radius: 3px;
        }

        .table-row {
            display: flex;
            line-height: 36px;
            min-height: 36px;
            position: relative;
            cursor: pointer;
            box-shadow: inset 0px -1px 0px 0px rgba(64, 155, 255, 0.3);

            &.row-hover,
            &.row-selected {
                background: rgba(0, 149, 255, 0.6);
                .rank-circle {
                    background: rgba(0, 149, 255, 1);
                }
            }

            .table-cell {
                font-family: 'PingFangSC, PingFang SC';
                flex: 1;
                padding-left: 16px;
                text-align: left;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .rank-cell {
                width: 60px;
                flex: none;
                padding-left: 0;
                display: flex;
                justify-content: center;
                align-items: center;

                .rank-circle {
                    width: 20px;
                    height: 20px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    border-radius: 50%;
                    background: rgba(0, 149, 255, 0.2);
                    background-size: 100% 100%;
                    background-repeat: no-repeat;
                    background-position: center;
                }
                .rank-circle-0 {
                    background-image: url('../../img/abilityMonitor/gold-medal.png');
                }
                .rank-circle-1 {
                    background-image: url('../../img/abilityMonitor/silver-medal.png');
                }
                .rank-circle-2 {
                    background-image: url('../../img/abilityMonitor/bronze-medal.png');
                }
            }
        }
    }

    /* 空数据样式 */
    .el-empty {
        height: 100%;
    }
}
</style>
