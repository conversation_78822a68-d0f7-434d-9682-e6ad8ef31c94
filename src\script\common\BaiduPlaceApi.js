function BaiduPlaceApi(regionParam) {
    let _region = regionParam;
    let _scope = 1;
    let _pageSize = 20;
    let _pageNumMax = 1;
    let _output = 'json';
    let _url = 'https://api.map.baidu.com/place/v2/search';
    let gisLocater = new GisLocater();
    let _akArray = [
        {ak: 'sIdIz44bHXXLGQHSnXD8yfM7'},
        {ak: 'QeSK9l7HhpfSXyazvuG9TA19OnpSaoTC'},
        {ak: '1HD0pao5EKXkaTteq499mYErWbmZv90S'},
        {ak: 'b5jhSDKt8mhc6SFMECt0PYmRIsj08IA9'},
        {ak: '8OgYmp8sXWWxGgfQUKYtCafk'},
        {ak: 'k25CijwjubGLBGY0GcUPQ345'},
        {ak: 'IBXGOOTAkWPfOt3IA7Z1OS2yhgH53QuI'},
        {ak: 'UFDa8st5v8GKGBa8qhEHPxAl'},
        {ak: 'vnuTKgGIjtOqjzyrlVsdffFC2cUikEtr'},
        {ak: 'As5aP2zX5PBSKR75WG9iq6DOW8u63SFu'},
        {ak: '6D7kpOZUVH07hfs0EOxI9sCIfgcTOnOU'},
        {ak: 's4G3GCG9EGu8aGU09FdZDrpVQS7WlUkx'},
        {ak: 'tdqfO1r2PTGHYQMjGxCmzPgo4VuGKi4e'},
        {ak: 'tF8PfNCObo16ok42d1mQoosAcC86rAQB'},
        {ak: 'nK7dR7XkkAgpQXw8615R9GXu1s3qDOlM'},
        {ak: 'MHATMMVools6PzSU7DalQRoR'},
        {ak: 'LCWIbm91dSb9ri2P0kttUu6k1g22TbQN'},
        {ak: 'MF0Ky9VIkdLQG61vGHUf8vu4kenEAhuk'},
        {ak: 'xCgRNC3RWCOcZrAziH4rZNGvRNpQOSH7'},
        {ak: 'la61o1WyXetRAvqFsGMnnFf2fpQqyj7k'},
        {ak: 'C1AMSVM4SMMeiHo5m14XH2GKZwLsGRgX'},
        {ak: 'chF96KYDQPnIrgAcOUSI9Ks1xhdP70Kk'},
        {ak: 'r1csnVTMnbe6HB6O4XhytilqGyqr6UNP'},
        {ak: 'hTxKBaLbG5lObt1o6rCZt9PvClroEG53'},
        {ak: 'X74rMzQcxPKqwnwg0m5ahSjuUYp4WHDO'},
        {ak: 'DReSjqWBhO5EW5GGZTbMOOSWq5ZajGHl'},
        {ak: 'vQFpFy74vyf3VkyPoeItDFaSfK4roPxC'}
    ];
    let corl = true;


    function getAk() {
        let ak = '';
        let i, item;
        for (i = 0; i < _akArray.length; i += 1) {
            item = _akArray[i];
            if (!item.dayOut) {
                ak = item.ak;
                break;
            }
        }
        return ak;
    }


    function setAkToDayout(ak) {
        let i, item;
        for (i = 0; i < _akArray.length; i += 1) {
            item = _akArray[i];
            if (item.ak == ak) {
                item.dayOut = true;
            }
        }
    }


    let result = new Object();
    //城市内检索
    result.searchInRegion = function (nameParam, callbackParam) {
        if (corl) {
            searchInRegionByPage(nameParam, callbackParam, 0, []);
            return true;
        } 
            return false;
        
    };
    result.changeCityName = function (name) {
        _region = name;
    };


    function searchInRegionByPage(nameParam, callbackParam, pageNum, resultDatas) {
        let ak = getAk();
        if (ak == '') {
            let resultObj = {
                status: 302,
                message: '所有AK的天配额超限，限制访问'
            };
            callbackParam(resultObj);
            return;
        }
        let ajax = $.ajax({
            url: _url,
            type: 'get',
            dataType: 'jsonp',
            jsonp: 'callback',
            jsonpCallback: 'jsonpCallback',
            data: {
                query: nameParam,
                region: _region,
                scope: _scope,
                page_size: _pageSize,
                page_num: pageNum,
                output: _output,
                ak: ak
            },
            success: function (data) {
                if (num != undefined) {
                    clearTimeout(num);
                    ajax = undefined;
                }
                //console.log(data);
                if (data.status == 0) {
                    let items = data.results;
                    let i, item, gp;//, text = "";
                    for (i = 0; i < items.length; i += 1) {
                        item = items[i];
                        if (item.location) {
                            gp = gisLocater.bd09_To_Gps84(item.location.lng, item.location.lat);
                            item.location.wgLng = gp.getWgLon();
                            item.location.wgLat = gp.getWgLat();
                            resultDatas.push(item);
                        }
                    }
                    let total = data.total;
                    if (_pageSize * (pageNum + 1) < total && pageNum + 1 < _pageNumMax) {
                        searchInRegionByPage(nameParam, callbackParam, pageNum + 1, resultDatas);
                    } else {
                        //已经搜索完
                        let resultObj = {
                            status: 0,
                            message: 'ok',
                            total: total,
                            items: resultDatas
                        };
                        callbackParam(resultObj);
                    }
                } else {
                    //该ak密匙超限
                    setAkToDayout(ak);
                    searchInRegionByPage(nameParam, callbackParam, pageNum, resultDatas);
                }
                // else {
                //     let resultObj = {
                //         status: data.status,
                //         message: data.message,
                //     };
                //     callbackParam(resultObj);
                // }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                if (XMLHttpRequest.status == 200) {
                    if (num != undefined) {
                        clearTimeout(num);
                        ajax = undefined;
                    }
                    setAkToDayout(ak);
                    searchInRegionByPage(nameParam, callbackParam, pageNum, resultDatas);
                }
            }
        });
        let num = setTimeout(function () {
            ajax.abort();
            corl = false;
            // main.searchTool.corl.txt[2] = "本地POI地址搜索";
            // let input = dom.querySelector('#selTypeBut');
            // if (input[0].value == "2") {
            // 	input.change();
            // 	main.searchTool.staticShowAddress(nameParam);
            // }
        }, 5000);
    }


    return result;
}
//百度经纬度与本地经纬度转换类
function GisLocater() {
    let pi = Math.PI;
    let a = 6378245.0;
    let ee = 0.00669342162296594323;


    //84 to 火星坐标系 (GCJ-02) World Geodetic System ==> Mars Geodetic System
    // function gps84_To_Gcj02(lon, lat) {
    // 	if (outOfChina(lon, lat)) {
    // 		return null;
    // 	}
    // 	let dLat = transformLat(lon - 105.0, lat - 35.0);
    // 	let dLon = transformLon(lon - 105.0, lat - 35.0);
    // 	let radLat = lat / 180.0 * pi;
    // 	let magic = Math.sin(radLat);
    // 	magic = 1 - ee * magic * magic;
    // 	let sqrtMagic = Math.sqrt(magic);
    // 	dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * pi);
    // 	dLon = (dLon * 180.0) / (a / sqrtMagic * Math.cos(radLat) * pi);
    // 	let mgLat = lat + dLat;
    // 	let mgLon = lon + dLon;
    // 	return new GisPos(mgLon, mgLat);
    // }


    //火星坐标系 (GCJ-02) to 84
    function gcj_To_Gps84(lon, lat) {
        let gps = transform(lon, lat);
        let lontitude = lon * 2 - gps.getWgLon();
        let latitude = lat * 2 - gps.getWgLat();
        return new GisPos(lontitude, latitude);
    }


    //火星坐标系 (GCJ-02) 与百度坐标系 (BD-09) 的转换算法 将 GCJ-02 坐标转换成 BD-09 坐标
    function gcj02_To_Bd09(gg_lon, gg_lat) {
        let x = gg_lon, y = gg_lat;
        let z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * pi);
        let theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * pi);
        let bd_lon = z * Math.cos(theta) + 0.0065;
        let bd_lat = z * Math.sin(theta) + 0.006;
        return new GisPos(bd_lon, bd_lat);
    }


    //火星坐标系 (GCJ-02) 与百度坐标系 (BD-09) 的转换算法 * * 将 BD-09 坐标转换成GCJ-02 坐标 *
    function bd09_To_Gcj02(bd_lon, bd_lat) {
        let x = bd_lon - 0.0065, y = bd_lat - 0.006;
        let z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * pi);
        let theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * pi);
        let gg_lon = z * Math.cos(theta);
        let gg_lat = z * Math.sin(theta);
        return new GisPos(gg_lon, gg_lat);
    }


    //(BD-09)-->84
    this.bd09_To_Gps84 = function (bd_lon, bd_lat) {
        if (bd_lat == 0 || bd_lon == 0) {
            return new GisPos(0, 0);
        }
        let gcj02 = bd09_To_Gcj02(bd_lon, bd_lat);
        let map84 = gcj_To_Gps84(gcj02.getWgLon(), gcj02.getWgLat());
        return map84;

    };
    //gps84->bd09
    this.gps84_To_bd09 = function (bd_lon, bd_lat) {
        // let gcj02 = gps84_To_Gcj02(bd_lon, bd_lat);
        let bd09 = gcj02_To_Bd09(bd_lon, bd_lat);
        return bd09;
    };


    function outOfChina(lon, lat) {
        if (lon < 72.004 || lon > 137.8347)
            return true;
        if (lat < 0.8293 || lat > 55.8271)
            return true;
        return false;
    }


    function transform(lon, lat) {
        if (outOfChina(lon, lat)) {
            return new GisPos(lon, lat);
        }
        let dLat = transformLat(lon - 105.0, lat - 35.0);
        let dLon = transformLon(lon - 105.0, lat - 35.0);
        let radLat = lat / 180.0 * pi;
        let magic = Math.sin(radLat);
        magic = 1 - ee * magic * magic;
        let sqrtMagic = Math.sqrt(magic);
        dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * pi);
        dLon = (dLon * 180.0) / (a / sqrtMagic * Math.cos(radLat) * pi);
        let mgLat = lat + dLat;
        let mgLon = lon + dLon;
        return new GisPos(mgLon, mgLat);
    }


    function transformLat(x, y) {
        let ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x));
        ret += (20.0 * Math.sin(6.0 * x * pi) + 20.0 * Math.sin(2.0 * x * pi)) * 2.0 / 3.0;
        ret += (20.0 * Math.sin(y * pi) + 40.0 * Math.sin(y / 3.0 * pi)) * 2.0 / 3.0;
        ret += (160.0 * Math.sin(y / 12.0 * pi) + 320 * Math.sin(y * pi / 30.0)) * 2.0 / 3.0;
        return ret;
    }


    function transformLon(x, y) {
        let ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x));
        ret += (20.0 * Math.sin(6.0 * x * pi) + 20.0 * Math.sin(2.0 * x * pi)) * 2.0 / 3.0;
        ret += (20.0 * Math.sin(x * pi) + 40.0 * Math.sin(x / 3.0 * pi)) * 2.0 / 3.0;
        ret += (150.0 * Math.sin(x / 12.0 * pi) + 300.0 * Math.sin(x / 30.0 * pi)) * 2.0 / 3.0;
        return ret;
    }
}
//本地经纬度内部类
function GisPos(wgLonParam, wgLatParam) {
    this.wgLon = wgLonParam;
    this.wgLat = wgLatParam;
}


GisPos.prototype.getWgLat = function () {
    return this.wgLat;
};

GisPos.prototype.setWgLat = function (wgLatParam) {
    this.wgLat = wgLatParam;
};

GisPos.prototype.getWgLon = function () {
    return this.wgLon;
};

GisPos.prototype.setWgLon = function (wgLonParam) {
    this.wgLon = wgLonParam;
};

function showNone(gis) {
    let listDom = gis.dom.find('.searchControl .addressSearch');
    let listBody = gis.dom.find('.searchControl .listBody');
    listBody.html('没有找到结果!');
    listDom.slideDown(250);
    setTimeout(function () {
        listDom.slideUp(500);
    }, 1000);
}
function getAddress(value,gis){
    let searchs = gis.dom.find('.searchControl');
    let searchLoadding = searchs.find('.search_loadding');
    searchLoadding.show();
    gis.tool = {
        ...gis.tool,
        onSearchAction: new gis.classEvent(),
        onSearchFinish: new gis.classEvent(),
        onSearchChange:new gis.classEvent(),
    };
    let listDom = gis.dom.find('.searchControl .addressSearch');
    let listBody = gis.dom.find('.searchControl .listBody');
    let showAddress = function (ans) {
        listBody.html();
        try {
            let html = '';
            if (ans.items.length) {
                ans.items.forEach(i => {
                    html += '<div class=\'listItem\' valueLat=\'' + i.location.wgLat + '\' valueLng=\'' + i.location.wgLng + '\' ';
                    if (i.pointShape != undefined) {
                        html += 'valueShape=\'' + i.pointShape + '\'';
                    }
                    html += '>';
                    html += '<div class=\'itemName\' title=\'' + i.name + '\'>' + i.name + '</div>';
                    html += '<div class=\'itemValue\' title=\'' + (i.address == undefined ? '' : i.address) + '\'>' + (i.address == undefined ? '' : i.address) + '</div>';
                    html += '</div>';
                });
                listBody.html(html);
            } else {
                showNone(gis);
            }
        } catch (e) {
            showNone(gis);
        }
        listDom.slideDown(500);
    };

    listBody.on('click', '.listItem', function (event) {
        let thDom = $(this);
        let lat = thDom.attr('valueLat');
        let lng = thDom.attr('valueLng');
        let name = thDom.find('.itemName').attr('title');
        let address = thDom.find('.itemValue').attr('title');
        let data = {lat: lat, lng: lng, showArrow: true, name: name, address: address};
        let shape = thDom.attr('valueShape');
        if (shape != undefined) {
            data.valueShape = JSON.parse(shape);
        }
        gis.cameraControl.move(data);
        gis.tool.onSearchAction.run(data, event);
        setTimeout(function () {
            listDom.slideUp(500);
        }, 1000);
    });
    let search = new BaiduPlaceApi(gis.cityInfo.cityName);
    search.changeCityName(gis.cityInfo.cityName);
    search.searchInRegion(value, function (ans) {
        showAddress(ans);
        searchLoadding.hide();
        gis.tool.onSearchFinish.run(ans);
    });
}

export {
    BaiduPlaceApi,
    GisLocater,
    getAddress
};