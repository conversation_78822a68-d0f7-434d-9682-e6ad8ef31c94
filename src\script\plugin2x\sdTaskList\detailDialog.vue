<template>
    <div class="task-detail">
        <div class="left-box" @click="handleClose"></div>
        <div class="con">
            <header class="con-header">
                <span style="width: calc(100% - 30px)" class="title transverse-scroll over-box"
                    >{{ detailData.taskName }}
                </span>
                <i class="el-icon-close close" @click="handleClose"></i>
            </header>
            <div class="detail-content">
                <expand :title="'基本信息'">
                    <template slot="content">
                        <div class="content-item">
                            <div class="item">
                                <label>任务名称：</label>
                                <div class="item-con transverse-scroll over-box">
                                    {{ detailData.taskName }}
                                </div>
                            </div>
                            <div class="item">
                                <label>任务ID：</label>
                                <div class="item-con">
                                    {{ detailData.taskId }}
                                </div>
                            </div>
                            <div class="item">
                                <label>生效时间：</label>
                                <div class="item-con">
                                    {{ detailData.time }}
                                </div>
                            </div>
                            <div class="item">
                                <label>任务备注：</label>
                                <div class="item-con transverse-scroll">
                                    {{ detailData.comment }}
                                </div>
                            </div>
                        </div>
                    </template>
                </expand>
                <expand :title="'区域'">
                    <template slot="content">
                        <div class="content-item">
                            <div class="item" style="auto">
                                <div class="item-box">
                                    <label>区域类型：</label>
                                    <div class="item-con">
                                        {{ detailData.areaType }}
                                    </div>
                                </div>
                                <div
                                    class="item-box"
                                    :class="
                                        detailData.areaType === '自定义区域' ? 'item-box-area' : ''
                                    "
                                >
                                    <label>{{ detailData.areaType }}：</label>
                                    <div class="item-con">
                                        <span v-if="detailData.areaType !== '自定义区域'">{{
                                            detailData.area && detailData.area.join(',')
                                        }}</span>
                                        <div v-else class="custom-area">
                                            <span class="custom-area-title">
                                                {{ detailData.area.length }}个 </span
                                            ><br />
                                            <span class="custom-area-id">
                                                {{ detailData.area && detailData.area.join(',') }}
                                            </span>
                                            <el-popover
                                                class="area-more"
                                                popper-class="popover-dark"
                                                placement="top"
                                                title="自定义区域ID"
                                                width="300"
                                                trigger="click"
                                                :content="
                                                    detailData.area && detailData.area.join(',')
                                                "
                                            >
                                                <el-button type="text" slot="reference" size="mini"
                                                    >更多</el-button
                                                >
                                            </el-popover>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </expand>
                <!-- <expand :title="'客群'">
                    <template slot="content">
                        <div class="content-item">
                            <div class="item">
                                <div class="item-box">
                                    <label>人群对象：</label>
                                    <div class="item-con">
                                        {{ detailData.peopleType }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </expand> -->

                <expand :title="'事件任务'">
                    <template slot="content">
                        <div class="content-item">
                            <div class="task-list">
                                <el-tooltip
                                    v-for="(item, index) in detailData.events"
                                    :key="index"
                                    class="item-tooltip"
                                    :content="item.describe"
                                    placement="top-start"
                                    popper-class="tooltip-name tooltip-dark"
                                >
                                    <div class="task-list-item">
                                        <div class="event-item" v-if="item.eventType !== 3">
                                            <span>
                                                {{ item.eventTypeName }}
                                            </span>
                                        </div>

                                        <div v-else class="event-item residency">
                                            <span>
                                                {{ item.eventTypeName }}
                                            </span>
                                            <br />
                                            <span>
                                                最小时长：{{ item.eventCondition.minTime }}s
                                            </span>
                                            <br />
                                            <span>
                                                是否重置驻留时长:
                                                {{ item.eventCondition.needReset ? '是' : '否' }}
                                            </span>
                                        </div>
                                    </div>
                                </el-tooltip>
                            </div>
                        </div>
                    </template>
                </expand>
                <expand :title="'输出'">
                    <template slot="content">
                        <div class="content-item">
                            <div class="item">
                                <div class="item-box">
                                    <label>任务ID：</label>
                                    <div class="item-con">
                                        {{ detailData.taskId }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </expand>
            </div>
        </div>
    </div>
</template>
<script>
import expand from '_com/expand.vue';
export default {
    components: {
        expand
    },
    props: {
        visible: {
            type: Boolean,
            default: true
        },
        detailData: {
            type: Object,
            default: () => ({
                taskName: '',
                taskId: '',
                time: '',
                comment: '',
                areaType: '',
                area: [''],
                peopleType: '',
                userTypeName: '',
                userP: '',
                events: [{ name: '', content: '' }],
                output: [{}]
            })
        }
    },
    data() {
        return {};
    },
    methods: {
        handleClose() {
            this.$emit('change-dialog', false);
        }
    }
};
</script>
<style lang="less" scoped>
.over-box{
    display: inline-block;
}
.task-detail {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    background: rgba(9, 9, 9, 0.6);
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: flex-start;
    .left-box {
        flex: 1;
        height: 100%;
    }
    .con {
        width: 677px;
        height: 100%;
        background: var(--dark-bg);
        .con-header {
            padding: 16px;
            border-bottom: 1px solid var(--border-blue);
            height: 58px;
            width: 100%;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            .title {
                font-size: 18px;
                color: var(--white-text);
                font-weight: 400;
                .end-time {
                    font-style: normal;
                    font-weight: 400;
                    color: var(--white-text);
                    font-size: 12px;
                }
            }
            .close {
                color: var(--white-text);
                text-shadow: none;
                opacity: .4;
                &:hover {
                    opacity: 1;
                }
            }
        }
        .detail-content {
            height: calc(100% - 59px);
            padding: 16px;
            .detail-header {
                padding: 0 16px;
                line-height: 40px;
                height: 40px;
                background: rgba(64, 158, 255, 0.1);
                font-size: 12px;
                border-radius: 4px 4px 4px 4px;
                margin-bottom: 16px;
                display: flex;
                flex-direction: row;
                align-items: center;
                border: 1px solid rgba(64, 158, 255, 0.2);
                justify-content: space-between;
                font-weight: 400;
                color: #409eff;
                font-size: 14px;
            }
        }
        .detail-content {
            overflow-x: hidden;
            overflow-y: auto;
            font-size: 14px;
            .content-item {
                height: auto;
                padding: 10px;
            }
            .task-list {
                display: flex;
                flex-direction: row;
                justify-content: flex-start;
                flex-wrap: wrap;
                &-item {
                  margin-bottom: 12px;
                  text-align: center;
                  margin-right:20px;
                  min-width: 100px;
                  .event-item{
                       height: 40px;
                       line-height: 40px;
                       border-radius: 4px;
                       border: 1px solid var(--border-blue);
                       color: var(--white-text);
                  }
                  .residency{
                   height: 80px;
           padding: 10px;
          text-align: left;
          line-height: normal;
                  }
                    .icon{
                            cursor: pointer;
                        }
                    .item-tooltip{
                        color: #ccc;

                    }
                    &-btn {
                        width: 100%;
                    }
                }
            }
            .item {
                display: flex;
                flex-direction: row;
                height: 32px;
                // line-height: 32px;
                &-box {
                    width: 50%;
                    height: 100%;
                    display: flex;
                    flex-direction: row;
                    justify-content: flex-start;
                    align-items: center;
                }
                &-box-area{
                    align-items: flex-start;
                }
                label {
                    color: var(--light-text);
                    font-weight: 400;
                     margin-bottom: 0px;
                }
                &-con {
                    color: var(--white-text);
                    flex:1,
                    // display: inline-block;
                }
            }
            .custom-area{
                height: 40px;
                position:relative;
                &-title{
                    display: inline-block;
                }
                &-id{
                    display: inline-block;
                    width: 160px;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
                .area-more{
                    position: absolute;
                    top: -3px;
                    left: 125px;
                }
            }
        }
    }

}
</style>
<style lang="less">
.tooltip-name {
    // border: none !important;
    border-color: #fff;
    background: #fff;
    min-width: 150px;
    max-width: 300px;
    border-radius: 4px;
    border: 1px solid #ebeef5;
    padding: 12px;
    z-index: 2000;
    color: #606266;
    line-height: 1.4;
    text-align: justify;
    font-size: 14px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    word-break: break-all;
    position: relative;
    .popper__arrow {
        display: none;
    }
    &::after {
        position: absolute;
        display: block;
        width: 0;
        height: 0;
        border-color: transparent;
        border-style: solid;
        content: ' ';
        border-width: 6px;
        margin-left: -6px;
        left: 80px;
        bottom: -5px;
        border-bottom-width: 0;
        border-top-color: #fff;
    }
}
.el-popover {
    color: var(--light-text);
    &__title {
        color: var(--white-text);
    }
}
</style>
