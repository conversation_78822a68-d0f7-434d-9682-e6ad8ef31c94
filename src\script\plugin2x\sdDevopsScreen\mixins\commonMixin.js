import { request } from '@/script/utils/request.js';
const screenCommon = {
    data(){
        return{
            lengendList:{},
            refresh:'',
        };
    },
    computed:{
        defaultStartTime(){
            const date = new Date();
            const startTime = new Date(date.setDate(date.getDate() - 1));
            return startTime.format('yyyy-MM-dd HH:mm:00');
        },
        defaultNewDate(){
            return new Date().format('yyyy-MM-dd HH:mm:00');
        }
    },
    methods:{
        closeShowMore(){
            if(this.isShowMore){
                this.isShowMore = false;
            }
        },
        async getPost(postName, _param,errContent, callBack) {
            await request('post',postName,_param)
                .then((rcvData) => {
                    this.$exloaded1x();
                    if(typeof rcvData === 'string'){
                        this.$message(rcvData);
                        return;
                    }
                    //此处成功逻辑业务代码
                    callBack && callBack(rcvData);
                })
                .catch((err) => {
                    this.$exloaded1x();
                    let options = {
                        title: '消息提示',
                        content: errContent+'接口请求失败！',
                        detail: `详细内容：${err.errorMessage || err}`,
                    };
                    this.$popupMessageWindow(options);
                });
        },
        // 处理无数据
        handlerNullLineData(dataIndex){
            const commonLineData = this.configure[dataIndex].data.lineData;
            this.configure[dataIndex].data.isNull = true;
            this.configure[dataIndex].data.xAxisData = [];
            this.configure[dataIndex].data.lineData.forEach((item,index) => {
                item.data = new Array(commonLineData[index].data).fill('-');
            });
            this.configure[dataIndex].data['granularity'] =  [''];
        },
        // 处理通用趋势图数据
        handlerLineData(data,dataIndex){
            const {dataInfo} = data;
            const commonLineData = this.configure[dataIndex].data.lineData;
            if(!dataInfo){
                this.handlerNullLineData(dataIndex);
                return;
            }
            let unit = [],lineData = [],xAxisData = [],yAxis=[],topList={},time=[],granularity=[],minValue=[],minInterval=[];
            if(data.unit){
                yAxis = ['(' + data.unit + ')'];
            }
            dataInfo.forEach((item,index) => {
                unit.push(item.unit || data.unit);
                granularity.push(item.granularity || data.granularity || '');
                minValue.push(item.minValue || data.minValue);
                minInterval.push(item.minInterval||data.minInterval);
                if(!data.unit){
                    yAxis.push(item.title+'(' + item.unit + ')');
                }
                topList[item.title]= item.dataList.map((val) => val.topList || []);
                lineData.push({
                    name:item.title,
                    data:item.dataList.length?item.dataList.map((val) => val.value):[],
                    color:commonLineData[index].color,
                    areaStyle:commonLineData[index].areaStyle || {},
                    yAxisIndex:yAxis[index]?index:0,
                });
                xAxisData = item.dataList.length?item.dataList.map((val) => val.simpleTime):[];
                time = item.dataList.length?item.dataList.map((val) => val.time):[];
            });
            if(xAxisData.length){
                this.configure[dataIndex].data.isNull = false;
            }else{
                this.handlerNullLineData(dataIndex);
            }
            this.configure[dataIndex].data.title = data.title;
            this.configure[dataIndex].data.unit = unit;
            this.configure[dataIndex].data.yAxis = yAxis;
            this.configure[dataIndex].data.lineData = lineData;
            this.configure[dataIndex].data.xAxisData = xAxisData;
            this.configure[dataIndex].data['topList'] =  topList;
            this.configure[dataIndex].data['time'] =  time;//完整时间
            this.configure[dataIndex].data['granularity'] =  granularity;//时间粒度
            this.configure[dataIndex].data['minValue'] = minValue;//y轴最小值
            this.configure[dataIndex].data['minInterval'] = minInterval;//y轴最小间隔
        },
        // 处理饼图数据
        handlerPieData(data,dataIndex){
            const {delayInfoList,title,searchStartTime,searchEndTime} = data;
            if(this.configure[dataIndex].data.title){
                this.configure[dataIndex].data.title = title ||'时延';
            }
            if(!delayInfoList || !delayInfoList.length){
                this.handlerNullPie(dataIndex);
                return;
            }
            this.configure[dataIndex].data.pieData = delayInfoList;
            const percent = {};
            delayInfoList.forEach((item) => {
                 percent[item.name] = Number(item.value).toFixed(1) + '%';
            });
            this.configure[dataIndex].data.percent = percent;
            if(searchStartTime && searchEndTime){
                this.configure[dataIndex].data['timeTips'] = [searchStartTime,searchEndTime];
            }
            // this.$set(this.lengendList,dataIndex,topThreeList);
        },
        handlerNullPie(dataIndex){
            this.configure[dataIndex].data.pieData = [
                { value: 0, name: '<60 s' },
                { value: 0, name: '60-120 s' },
                { value: 0, name: '120-300 s' },
                { value: 0, name: '300-1200 s' },
                { value: 0, name: '>1200 s' },
                ];
            this.configure[dataIndex].data.percent ={
                '<60 s': '0.0%',
                '60-120 s': '0.0%',
                '120-300 s': '0.0%',
                '300-1200 s': '0.0%',
                '>1200 s':'0.0%'
            };
            // this.$set(this.lengendList,dataIndex,[]);
        }
    }
};
export default screenCommon;