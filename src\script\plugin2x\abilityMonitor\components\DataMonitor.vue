<template>
    <cornerCard class="content-bg content-first">
        <section-title-one title="数据监控">
            <el-form class="search-bar-first">
                <el-form-item label="类型选择：" label-width="100px">
                    <el-select
                        v-model="localForm.type"
                        size="small"
                        popper-class="select-dropdown-dark popover-dark"
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="item in typeOpt"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="时间范围：" label-width="100px">
                    <date-picker
                        v-model="localForm.time"
                        :is-init-time="false"
                        :showOptionsShortcuts="false"
                        type="datetimerange"
                        format="yyyy-MM-dd HH:mm:ss"
                        value-format="yyyy-MM-dd HH:mm:ss"
                    ></date-picker>
                </el-form-item>
                <el-form-item label-width="16px">
                    <el-button type="primary" size="small" @click="search">查询</el-button>
                    <el-button type="primary" class="reset-btn" size="small" @click="reset"
                        >重置</el-button
                    >
                </el-form-item>
            </el-form>
        </section-title-one>
        <div class="content-first-wrapper">
            <div class="content-first-left">
                <section-title-two title="软采数据质量与时延趋势"></section-title-two>
                <div class="content-first-left-wrapper">
                    <div class="card-box">
                        <div
                            class="card-item"
                            v-for="(item, index) in cardList"
                            :key="index"
                            :class="{ 'card-item-active': activeCard === item.id }"
                            @click="handleCardClick(item)"
                        >
                            <div class="card-item-title-label" :style="{ background: item.bg }">
                                TOP {{ index + 1 }}
                            </div>
                            <span class="card-item-title-value">{{ item.cityName }}</span>
                            <div class="card-item-count-label">数据条数</div>
                            <span class="card-item-count-value"
                                >{{ convertToMillion(item.total) }} 百万</span
                            >
                            <div class="card-item-rate-label">回填率</div>
                            <span class="card-item-rate-value">{{
                                item.ratio ? (item.ratio * 100).toFixed(2) + '%' : ''
                            }}</span>
                        </div>
                    </div>
                    <common-echart
                        class="chart-box"
                        :targetData="hasSoftPurchaseData || true ? leftChartData : {}"
                        refName="data-quality-and-delay-trend"
                    ></common-echart>
                </div>
            </div>
            <div class="content-first-right">
                <section-title-two title="实时精准定位数据流量监控"></section-title-two>
                <common-echart
                    class="right-chart-box"
                    :targetData="hasLocationData || true ? rightChartData : {}"
                    refName="positioning-data-traffic-monitoring"
                ></common-echart>
            </div>
        </div>
    </cornerCard>
</template>
<script>
import commonComponentsMixin from './comMixin.js';
export default {
    name: 'DataMonitor',
    mixins: [commonComponentsMixin],
    props: {
        form: Object,
        typeOpt: Array
    },
    data() {
        return {
            initialForm: {}, // 保存初始数据
            localForm: {
                type: '',
                time: []
            },
            cardList: [
                {
                    id: 1,
                    cityId: '',
                    cityName: '',
                    total: '',
                    ratio: null,
                    bg: 'linear-gradient( 90deg, #D9001B 0%, rgba(217,0,27,0) 100%)'
                },
                {
                    id: 2,
                    cityId: '',
                    cityName: '',
                    total: '',
                    ratio: null,
                    bg: 'linear-gradient( 90deg, #FFA420 0%, rgba(255,164,32,0) 100%)'
                },
                {
                    id: 3,
                    cityId: '',
                    cityName: '',
                    total: '',
                    ratio: null,
                    bg: 'linear-gradient( 90deg, #36DA54 0%, rgba(54,218,84,0) 100%)'
                },
                {
                    id: 4,
                    cityId: '',
                    cityName: '',
                    total: '',
                    ratio: null,
                    bg: 'linear-gradient( 90deg, #1890FF 0%, rgba(2,147,255,0) 100%)'
                },
                {
                    id: 5,
                    cityId: '',
                    cityName: '',
                    total: '',
                    ratio: null,
                    bg: 'linear-gradient( 90deg, #1890FF 0%, rgba(2,147,255,0) 100%)'
                }
            ],
            activeCard: 1,
            leftChartData: {
                xAxisData: [],
                yAxisName: '单位:百万',
                lengendShow: true,
                isGroup: true,
                lineData: [
                    {
                        name: '2分钟以下',
                        1: {
                            name: '今日',
                            type: 'line',
                            data: [],
                            lineType: 'solid',
                            color: '#0095FF',
                            unit: ''
                        },
                        2: {
                            name: '昨日',
                            type: 'line',
                            data: [],
                            lineType: 'dashed',
                            color: '#0095FF',
                            unit: ''
                        }
                    },
                    {
                        name: '2~5分钟',
                        1: {
                            name: '今日',
                            type: 'line',
                            data: [],
                            lineType: 'solid',
                            color: '#FFDF12',
                            unit: ''
                        },
                        2: {
                            name: '昨日',
                            type: 'line',
                            data: [],
                            lineType: 'dashed',
                            color: '#FFDF12',
                            unit: ''
                        }
                    },
                    {
                        name: '5分钟以上',
                        1: {
                            name: '今日',
                            type: 'line',
                            data: [],
                            lineType: 'solid',
                            color: '#0FFF81',
                            unit: ''
                        },
                        2: {
                            name: '昨日',
                            type: 'line',
                            data: [],
                            lineType: 'dashed',
                            color: '#0FFF81',
                            unit: ''
                        }
                    }
                ]
            },
            rightChartData: {
                xAxisData: [],
                yAxisName: '单位:百万',
                lengendShow: true,
                isGroup: false,
                lineData: [
                    {
                        name: '今日',
                        type: 'line',
                        data: [],
                        lineType: 'solid',
                        color: '#01E6FE',
                        unit: ''
                    },
                    {
                        name: '昨日',
                        type: 'line',
                        data: [],
                        lineType: 'dashed',
                        color: '#FFDF12',
                        unit: ''
                    }
                ]
            },
            hasSoftPurchaseData: false, // 是否有软采数据
            hasLocationData: false // 是否有实时精准定位数据
        };
    },
    watch: {
        form: {
            handler(newVal) {
                // 当外部form变化时，更新本地表单
                this.localForm = JSON.parse(JSON.stringify(newVal));
            },
            immediate: true,
            deep: true
        }
    },
    mounted() {
        // 保存初始数据副本
        this.initialForm = JSON.parse(JSON.stringify(this.form));

        // 初始化时，如果有时间，则请求一次接口
        if (this.localForm.time && this.localForm.time.length === 2) {
            this.search();
        }
    },
    methods: {
        // 转换单位为百万
        convertToMillion(value) {
            return (value / 1000000).toFixed(2);
        },
        // 查询按钮点击事件
        search() {
            if (!this.localForm.time || this.localForm.time.length !== 2) {
                this.$message.warning('请选择时间范围');
                return;
            }

            this.getSoftPurchaseDataRank();
            this.getRealTimeLocationDataFlow();
        },

        // 重置按钮点击事件
        reset() {
            // 使用初始数据重置表单
            this.localForm = JSON.parse(JSON.stringify(this.initialForm));

            // 重置后执行一次查询
            if (this.localForm.time && this.localForm.time.length === 2) {
                this.search();
            }
        },

        // 获取软采数据排名
        getSoftPurchaseDataRank() {
            const params = {
                startTime: this.localForm.time[0],
                endTime: this.localForm.time[1]
            };

            // 如果类型不是全部，则添加类型参数
            if (this.localForm.type !== 'all') {
                params.collectionType = this.localForm.type;
            }

            this.$exloading1x();
            this.getPost('post', 'softPurchaseDataRank', params, '系统成效-软采数据排名', (res) => {
                if (res && Array.isArray(res) && res.length > 0) {
                    // 处理卡片数据
                    this.cardList = this.cardList.map((item, index) => {
                        const { id, bg, ...resRest } = res[index] || {};
                        id, bg;
                        return {
                            ...item,
                            ...resRest
                        };
                    });

                    // 默认选中第一个城市
                    if (this.cardList.length > 0 && this.cardList[0].cityId) {
                        this.activeCard = this.cardList[0].id;
                        this.getSoftPurchaseDataTrend(this.cardList[0].cityId);
                    }
                } else {
                    this.hasSoftPurchaseData = false;
                }
                this.$exloaded1x();
            });
        },

        // 获取软采数据趋势
        getSoftPurchaseDataTrend(cityId) {
            if (!cityId) {
                return;
            }
            if (!this.localForm.time || this.localForm.time.length !== 2) {
                this.$message.warning('请选择时间范围');
                return;
            }

            const params = {
                startTime: this.localForm.time[0],
                endTime: this.localForm.time[1],
                cityId: cityId
            };

            // 如果类型不是全部，则添加类型参数
            if (this.localForm.type !== 'all') {
                params.collectionType = this.localForm.type;
            }

            this.$exloading1x();
            this.getPost(
                'post',
                'softPurchaseDataTrend',
                params,
                '系统成效-软采数据趋势',
                (res) => {
                    // 默认设置为无数据
                    this.hasSoftPurchaseData = false;

                    if (res && res.today && Array.isArray(res.today)) {
                        // 处理图表数据
                        const xAxisData = [];
                        const todayData2minBelow = [];
                        const todayData2minTo5min = [];
                        const todayData5minAbove = [];
                        const yesterdayData2minBelow = [];
                        const yesterdayData2minTo5min = [];
                        const yesterdayData5minAbove = [];

                        // 处理今日数据
                        res.today.forEach((item) => {
                            xAxisData.push(item.time);
                            todayData2minBelow.push(item.count2minBelow || 0);
                            todayData2minTo5min.push(item.count2minTo5min || 0);
                            todayData5minAbove.push(item.count5minAbove || 0);
                        });

                        // 处理昨日数据
                        if (res.yesterday && Array.isArray(res.yesterday)) {
                            res.yesterday.forEach((item) => {
                                yesterdayData2minBelow.push(item.count2minBelow || 0);
                                yesterdayData2minTo5min.push(item.count2minTo5min || 0);
                                yesterdayData5minAbove.push(item.count5minAbove || 0);
                            });
                        }

                        if (xAxisData.length > 0) {
                            this.leftChartData.xAxisData = xAxisData;
                            // 更新为isGroup: true的数据结构
                            this.leftChartData.isGroup = true;
                            this.leftChartData.lineData = [
                                {
                                    name: '2分钟以下',
                                    1: {
                                        name: '今日',
                                        type: 'line',
                                        data: todayData2minBelow,
                                        lineType: 'solid',
                                        color: '#0095FF',
                                        unit: '百万'
                                    },
                                    2: {
                                        name: '昨日',
                                        type: 'line',
                                        data: yesterdayData2minBelow,
                                        lineType: 'dashed',
                                        color: '#0095FF',
                                        unit: '百万'
                                    }
                                },
                                {
                                    name: '2~5分钟',
                                    1: {
                                        name: '今日',
                                        type: 'line',
                                        data: todayData2minTo5min,
                                        lineType: 'solid',
                                        color: '#FFDF12',
                                        unit: '百万'
                                    },
                                    2: {
                                        name: '昨日',
                                        type: 'line',
                                        data: yesterdayData2minTo5min,
                                        lineType: 'dashed',
                                        color: '#FFDF12',
                                        unit: '百万'
                                    }
                                },
                                {
                                    name: '5分钟以上',
                                    1: {
                                        name: '今日',
                                        type: 'line',
                                        data: todayData5minAbove,
                                        lineType: 'solid',
                                        color: '#0FFF81',
                                        unit: '百万'
                                    },
                                    2: {
                                        name: '昨日',
                                        type: 'line',
                                        data: yesterdayData5minAbove,
                                        lineType: 'dashed',
                                        color: '#0FFF81',
                                        unit: '百万'
                                    }
                                }
                            ];

                            this.hasSoftPurchaseData = true;
                        }
                    }
                    this.$exloaded1x();
                }
            );
        },

        // 获取实时精准定位数据流量监控
        getRealTimeLocationDataFlow() {
            const params = {
                startTime: this.localForm.time[0],
                endTime: this.localForm.time[1]
            };

            this.$exloading1x();
            this.getPost(
                'post',
                'realTimeLocationDataFlow',
                params,
                '系统成效-实时精准定位数据流量监控',
                (res) => {
                    // 默认设置为无数据
                    this.hasLocationData = false;

                    if (res && res.today && Array.isArray(res.today)) {
                        // 处理图表数据
                        const xAxisData = [];
                        const todayData = [];
                        const yesterdayData = [];

                        // 处理今日数据
                        res.today.forEach((item) => {
                            xAxisData.push(item.time);
                            todayData.push(item.count || 0);
                        });

                        // 处理昨日数据
                        if (res.yesterday && Array.isArray(res.yesterday)) {
                            res.yesterday.forEach((item) => {
                                yesterdayData.push(item.count || 0);
                            });
                        }

                        if (xAxisData.length > 0) {
                            this.rightChartData.xAxisData = xAxisData;
                            this.rightChartData.isGroup = false;
                            this.rightChartData.lineData = [
                                {
                                    name: '今日',
                                    type: 'line',
                                    data: todayData,
                                    lineType: 'solid',
                                    color: '#01E6FE',
                                    unit: '百万'
                                },
                                {
                                    name: '昨日',
                                    type: 'line',
                                    data: yesterdayData,
                                    lineType: 'dashed',
                                    color: '#01E6FE',
                                    unit: '百万'
                                }
                            ];

                            this.hasLocationData = true;
                        }
                    }
                    this.$exloaded1x();
                }
            );
        },

        // 处理卡片点击事件
        handleCardClick(item) {
            this.activeCard = item.id;
            this.getSoftPurchaseDataTrend(item.cityId);
        }
    }
};
</script>

<style lang="less" scoped>
@import '../PanelCommon.less';
.content-first-wrapper {
    width: 100%;
    height: 560px;
    .content-first-left-wrapper {
        width: 100%;
        height: 0;
        flex: 1;
        padding-top: 10px;
        display: flex;
        gap: 10px;
        flex-direction: column;
        .card-box {
            width: 100%;
            height: 100px;
            display: flex;
            justify-content: space-between;
            .card-item {
                width: 19%;
                height: 100%;
                padding: 16px;
                cursor: pointer;
                background-image: url('../../../../img/abilityMonitor/card-tag-bg.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                background-position: center;
                display: grid;
                gap: 6px;
                grid-template-rows: 1fr 1fr 1fr;
                grid-template-columns: 1fr 1fr;
                &-active {
                    background-image: url('../../../../img/abilityMonitor/card-tag-bg-active.png');
                }
                &-title {
                    font-size: 14px;
                    font-weight: 500;
                    font-family: 'PingFangSC, PingFang SC';
                    color: #fff;
                    &-label {
                        font-family: DIN, DIN;
                        font-weight: 500;
                        font-size: 12px;
                        color: #ffffff;
                        text-align: center;
                        width: 45px;
                        border-radius: 8px;
                    }
                    &-value {
                        font-family: 'PingFangSC, PingFang SC';
                        font-weight: 500;
                        font-size: 14px;
                        color: #ffffff;
                        line-height: 16px;
                        text-align: left;
                    }
                }
                &-count {
                    &-label {
                        font-family: 'PingFangSC, PingFang SC';
                        font-weight: 400;
                        font-size: 12px;
                        color: #c9dfff;
                        line-height: 16px;
                        text-align: left;
                    }
                    &-value {
                        font-family: DIN, DIN;
                        font-weight: 500;
                        font-size: 14px;
                        color: #ffffff;
                        line-height: 16px;
                        text-align: left;
                    }
                }
                &-rate {
                    font-size: 14px;
                    font-weight: 500;
                    font-family: 'PingFangSC, PingFang SC';
                    color: #fff;
                    &-label {
                        font-family: 'PingFangSC, PingFang SC';
                        font-weight: 400;
                        font-size: 12px;
                        color: #c9dfff;
                        line-height: 16px;
                        text-align: left;
                    }
                    &-value {
                        font-family: DIN, DIN;
                        font-weight: 500;
                        font-size: 14px;
                        color: #ffffff;
                        line-height: 16px;
                        text-align: left;
                    }
                }
            }
        }
        .chart-box {
            width: 100%;
            height: 0;
            flex: 1;
        }
    }
    .right-chart-box {
        width: 100%;
        height: 0;
        flex: 1;
        padding-top: 10px;
    }
}
</style>
