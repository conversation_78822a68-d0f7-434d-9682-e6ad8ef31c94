<template>
    <el-dialog
        title="添加小组件"
        :visible.sync="visible"
        width="30%"
        :before-close="() => $emit('update:visible',false)"
    >
        <div class="dialog-body">
            <div class="list-item" v-for="(item,index) in list" :key="index" @click="addCom(item)">
                <div>
                    <img class="img" :src="require(`@/img/devopsScreen/${item.comp}.png`)" alt=""/>
                    <span class="name">{{item.name}}</span>
                </div>
                <i class="el-icon-circle-plus plus"></i>
            </div>
        </div>
    </el-dialog>
</template>

<script>
export default {
    name:'addWidget',
    props:{
        visible:{
            type:Boolean,
            default:false,
        }
    },
    data(){
        return{
            list:[
                {
                    name:'溯源质量',
                    comp:'traceabilityQuality',
                },
                {
                    name:'拉链质量',
                    comp:'zipperQuality',
                },
            ],
        };
    },
    methods:{
        addCom(item){
            if(item.disable){
                this.$message('快马加鞭开发中，敬请期待...');
                return;
            }
            this.$emit('addCom',item);
            this.$emit('update:visible',false);
        }
    }
};
</script>

<style lang="less" scoped>
/deep/.el-dialog{
    background: rgba(28,36,62,0.8);
    margin: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
/deep/.el-dialog__title{
    color: #fff;
}
/deep/.el-dialog__body{
    padding: .89rem .22rem 1.11rem 1.11rem;
}
.dialog-body{
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    .list-item{
        height: 2.78rem;
        background: #2C344D;
        opacity: 1;
        padding: .56rem;
        border-radius: .22rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: calc(50% - .89rem);
        margin-right: .89rem;
        margin-bottom: .89rem;
        cursor: pointer;
        .img{
            width: 1.78rem;
            height: 1.78rem;
        }
        .name{
            color: #fff;
            font-size: .78rem;
            padding-left: .56rem;
        }
        .plus{
            color:#67C23A;
            font-size: 1.11rem;
        }
    }
    .list-item:hover{
        background: #606266;
    }
}
</style>