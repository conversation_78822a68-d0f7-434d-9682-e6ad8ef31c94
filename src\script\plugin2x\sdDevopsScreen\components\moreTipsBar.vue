<template>
    <div v-show="isShow" class="more-tips-bar">
        <div class="bar-item" @click="refresh">
            <i class="el-icon-refresh-right icon"></i>
            <span>刷新</span>
        </div>
        <div class="bar-item" @click="close">
            <i class="el-icon-close icon"></i>
            <span>关闭</span>
        </div>
        <div class="bar-item"  @click="replace">
            <i class="el-icon-refresh icon"></i>
            <span>更换</span>
        </div>
        <div class="bar-item" @click="closeShow">
            <i class="el-icon-bottom-right icon"></i>
            <span>进入专题</span>
        </div>
        <div class="refresh">
            <el-input-number ref="inputNum" v-model="input" size="mini" controls-position="right" :min="1" :max="100" :precision="0" :step="1"></el-input-number>
            <span>分钟自动刷新</span>
        </div>
    </div>
</template>

<script>
export default {
    name:'moreTipsBar',
    props:{
        isShow:{
            type:Boolean,
            default:false,
        },
        widgetParams:{
            type:Object,
            default:() => ({}),
        },
    },
    data(){
        return{
            input:'',
        };
    },
    watch:{
        'widgetParams.refresh':{
            handler(newV){
                if(newV && this.input !== newV){
                    this.input = newV;
                }
            },
            deep:true,
        },
        input:{
            handler(newV){
                if(newV){
                    this.$emit('update:refresh',newV);
                }
            },
            deep:true,
        },
        isShow:{
            handler(newV){
                if(newV){
                    this.$nextTick(() => {
                        this.$refs.inputNum.focus();
                    });
                }
            }
        }
    },
    mounted(){
        if(this.widgetParams && this.widgetParams.refresh) {
            this.input = this.widgetParams.refresh;
        }
    },
    methods:{
        mouseleave(e){
            //解决edge浏览器点击元素会触发mouseleave事件问题
            if(e.relatedTarget._prevClass === 'pop-bg'){
                return;
            }
            this.closeShow();
        },
        closeShow(){
            this.$emit('update:isShow',false);
        },
        refresh(){
            this.$emit('refresh');
            this.closeShow();
        },
        close(){
            this.$emit('close');
            this.closeShow();
        },
        replace(){
            this.$emit('replace');
            this.closeShow();
        }
    }
};
</script>

<style lang="less" scoped>
.more-tips-bar{
    position: absolute;
    top:2.78rem;
    right:1.11rem;
    width: 8.89rem;
    height: 10.72rem;
    background: rgba(48,48,48,1);
    border-radius: .11rem .11rem .11rem .11rem;
    opacity: 1;
    z-index:20000;
    padding: .56rem 0px;
    display: flex;
    flex-direction: column;
    .bar-item{
        width: 100%;
        height: 1.78rem;
        padding-left: 1.11rem;
        display: flex;
        align-items: center;
        cursor:pointer;
        span{
            padding-left:1.11rem;
            font-size: .78rem;
            line-height: 1rem;
            color: RGBA(170, 171, 172, 1);
        }
    }
    .bar-item:hover{
        background: RGBA(63, 67, 68, 1);
        span{
            color:#fff;
        }
    }
    .refresh{
        width:calc(100% - .56rem);
        height: 2.78rem;
        margin:0px .28rem;
        border-top: .06rem solid RGBA(64, 66, 68, 1);
        display: flex;
        align-items: center;
        span{
            padding-left:.56rem;
            font-size: .78rem;
            line-height: 1rem;
            color: RGBA(170, 171, 172, 1);
        }
        .num-input{
            width: 1.78rem;
            height: 1.33rem;
            background: RGBA(66, 67, 69, 1);
            outline: none;
            border: none;
            color:RGBA(170, 171, 172, 1);
            font-size: .78rem;
            text-align: center;
            border-radius: .11rem;
        }
    }
    .icon{
        color: RGBA(170, 171, 172, 1);
        font-size: .89rem;
    }
}
/deep/.el-input-number--mini{
    width: 2.33rem;
    line-height: 1.44rem;
    background: #3f4344;
    color: #999;
}
/deep/.el-input-number.is-controls-right .el-input-number__decrease{
    background: #3f4344;
    color: #999;
    border:none;
    width:1.11rem;
}
/deep/.el-input-number.is-controls-right .el-input-number__increase{
    background: #3f4344;
    color: #999;
    width:1.11rem;
    border:none;
}
/deep/.el-input-number.is-controls-right .el-input__inner{
    padding-left: 0px;
    padding-right: 1.11rem;
    background: #3f4344;
    color: #999;
    border-color: #999;
}
</style>