.gis-com {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
    /deep/ .pcGis > .buttonControl {
        right: 10px;
        bottom: auto;
        top: 10px;
    }
    .mtv-gis {
    }
    .extend {
        width: 40px;
        height: 40px;
        position: absolute;
        top: 10px;
        right: 260px;
        z-index: 10;
        background-color: #fff;
    }
}
.gis-com__tools {
    display: flex;
    position: absolute;
    left: 380px;
    top: 10px;
    z-index: 10;
}

.btn-box1 {
    cursor: pointer;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: #666;
    height: 40px;
    font-weight: 500;
    font-size: 16px;
    img {
        width: 16px;
        height: 16px;
        display: inline-block;
        margin-right: 4px;
        vertical-align: middle;
    }
}
.gis-tool2 {
    display: flex;
    margin-left: 10px;
    padding: 0px 10px;
    border-radius: 4px;
    background-color: #fff;
    .tool-box {
        color: #666;
        font-weight: 500;
        font-size: 16px;
    }
}
.gis-tool {
    margin-left: 10px;
    padding: 0px 10px;
    border-radius: 4px;
    cursor: pointer;
    background-color: #fff;
}
// 输入坐标
.area-popover {
    .area-input-footer {
        text-align: right;
    }
}

// 新的工具栏样式
.gis-tool-modern {
    background: rgba(18, 61, 124, 0.9);
    border-radius: 2px;
    border: 1px solid rgba(0, 149, 255, 0.5);
    display: flex;
    align-items: center;
    height: 40px;

    .tool-item {
        display: flex;
        gap: 6px;
        align-items: center;
        justify-content: center;
        padding: 10px 12px;
        cursor: pointer;
        position: relative;

        &:not(:last-child)::after {
            content: '';
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            height: 20px;
            border-right: 1px dashed rgba(201, 223, 255, 0.5);
        }

        .tool-icon {
            width: 20px;
            height: 20px;
            background-image: var(--tool-icon);
            background-repeat: no-repeat;
            background-size: 100% 100%;
        }

        .tool-label {
            font-size: 12px;
            color: #c9dfff;
            line-height: 14px;
        }

        // &:hover {
        //     .tool-icon {
        //         background-image: var(--tool-icon-active);
        //     }
        //     .tool-label {
        //         color: #01e6fe;
        //     }
        // }

        // &.active {
        //     .tool-icon {
        //         background-image: var(--tool-icon-active);
        //     }
        //     .tool-label {
        //         color: #01e6fe;
        //     }
        // }
    }
}

// 下拉菜单样式
.gis-dropdown {
    .el-dropdown-link {
        display: flex;
        align-items: center;
        gap: 6px;
        color: #c9dfff;
        font-size: 12px;

        &:hover {
            color: #01e6fe;
        }
    }
}
