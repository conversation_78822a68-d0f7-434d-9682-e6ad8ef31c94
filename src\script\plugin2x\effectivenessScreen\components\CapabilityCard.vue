<template>
    <SectionCard class="capability-card" title="能力指标">
        <div class="capability-content" v-if="level === 'province'">
            <div class="capability-item">
                <div class="capability-icon">
                    <img src="../../../../img/effectivenessScreen/capability-icon.png" alt="能力" />
                </div>
                <div class="capability-info">
                    <div class="capability-label">能力数量(个)</div>
                    <div class="capability-value text-ellipsis" :title="usedCapabilities">
                        {{ usedCapabilities }}
                    </div>
                </div>
            </div>
            <div class="capability-item">
                <div class="capability-icon">
                    <img
                        src="../../../../img/effectivenessScreen/capability-usage-icon.png"
                        alt="能力使用"
                    />
                </div>
                <div class="capability-info">
                    <div class="capability-label">能力使用(次)</div>
                    <div class="capability-value text-ellipsis" :title="totalCapabilities">
                        {{ totalCapabilities }}
                    </div>
                </div>
            </div>
        </div>
        <div class="capability-content" v-else>
            <div class="capability-city-item">
                <div class="capability-icon">
                    <img
                        src="../../../../img/effectivenessScreen/capability-app-icon.png"
                        alt="能力"
                    />
                </div>
                <div class="capability-info">
                    <div class="capability-info-item">
                        <div class="capability-label">能力数量(个)</div>
                        <div class="capability-value text-ellipsis" :title="usedCapabilities">
                            {{ usedCapabilities }}
                        </div>
                    </div>
                    <div class="capability-info-item">
                        <div class="capability-label">能力使用(次)</div>
                        <div class="capability-value text-ellipsis" :title="totalCapabilities">
                            {{ totalCapabilities }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </SectionCard>
</template>

<script>
import SectionCard from './SectionCard.vue';
import commonMixins from '@/script/mixins/commonMixins.js';

export default {
    name: 'CapabilityCard',
    mixins: [commonMixins],
    props: {
        level: {
            type: String,
            default: 'province'
        }
    },
    components: {
        SectionCard
    },
    data() {
        return {
            totalCapabilities: 0,
            usedCapabilities: 0
        };
    },
    created() {
        this.getCapabilityCount();
    },
    methods: {
        getCapabilityCount() {
            this.getPost('post', 'capabilityCount', {}, '能力指标-能力数量', (res) => {
                this.totalCapabilities = res.totalCapabilities;
                this.usedCapabilities = res.usedCapabilities;
            });
        }
    }
};
</script>

<style lang="less" scoped>
.capability-content {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0.8889rem;
    padding-bottom: 0;

    .capability-item {
        flex: 1;
        height: 100%;
        display: flex;
        align-items: center;

        .capability-icon {
            img {
                width: 6rem;
                height: 6rem;
            }
        }

        .capability-info {
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 0.4444rem;

            .capability-label {
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 0.7778rem;
                color: #ffffff;
                line-height: 0.7778rem;
                text-shadow: 0px 0px 0.2778rem rgba(30, 198, 255, 0.8);
            }

            .capability-value {
                background: linear-gradient(180deg, #ffffff 0%, #9be5ff 50%, #0dcaf5 80%);
                -webkit-background-clip: text;
                background-clip: text;
                color: transparent;
                font-family: YouSheBiaoTiHei;
                font-size: 1.3333rem;
            }
        }
    }

    .capability-city-item {
        flex: 1;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.8889rem;

        .capability-icon {
            img {
                width: 9.7778rem;
                height: 9.7778rem;
            }
        }
        .capability-info {
            flex: 1;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 0.8889rem;
            &-item {
                height: 3.3333rem;
                background: linear-gradient(
                    270deg,
                    rgba(9, 155, 255, 0.04) 0%,
                    rgba(9, 118, 255, 0.21) 100%
                );
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                &:after {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: -0.1111rem;
                    width: 0.1111rem;
                    height: 100%;
                    background: linear-gradient(180deg, #0085ff 0%, #00d4ff 100%);
                }

                .capability-label {
                    width: 50%;
                    padding-left: 0.8889rem;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 0.7778rem;
                    color: #ffffff;
                    line-height: 0.7778rem;
                    text-shadow: 0px 0px 0.2778rem rgba(30, 198, 255, 0.8);
                }

                .capability-value {
                    width: 50%;
                    max-width: 50%;
                    overflow: hidden;
                    padding-left: 0.4444rem;
                    background: linear-gradient(180deg, #ffffff 0%, #9be5ff 50%, #0dcaf5 80%);
                    -webkit-background-clip: text;
                    background-clip: text;
                    color: transparent;
                    font-family: YouSheBiaoTiHei;
                    font-size: 1.3333rem;
                }
            }
        }
    }
}
</style>
