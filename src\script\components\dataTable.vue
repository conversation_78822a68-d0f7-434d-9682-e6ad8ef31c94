<!-- 数据表格 -->
<template>
  <div class="data-table" ref="tables">
    <el-table
      ref="commonTable"
      :data="tableData"
      :size="size"
      style="width: 100%"
      :max-height="maxHeight"
      :span-method="spanMethod"
      :header-cell-style="theadColor"
      :cell-class-name="handleCellStyle"
      :row-class-name="rowClassName"
      :row-key="rowKey"
      :border="border"
      @row-click="handleRowClick"
      @cell-click="cellClick"
      @select-all="handleSelectAll"
      @selection-change="handleSelectChange"
    >
      <el-table-column 
        v-if="isSelectable" 
        type="selection" 
        reserve-selection 
        width="36" 
      />
      <el-table-column
        v-for="item in columns"
        v-bind="item"
        v-slot="{ row, $index }"
        :key="item.prop"
      >
        <slot :name="item.prop" :row="row" :inx="$index">
          {{ row[item.prop] }}
        </slot>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div
      v-if="isNeedPagination && paginationData.totalCount > paginationData.pageSize"
      class="wrap-pagination"
    >
      <el-pagination
        class="pagination"
        :current-page="paginationData.curPage"
        :page-sizes="[10, 15, 30, 50, 100]"
        :page-size="paginationData.pageSize"
        :total="paginationData.totalCount"
        :layout="layout"
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'data-table',
  props: {
    columns: {
      type: Array,
      default: () => [],
      required: true,
    },
    tableData: {
      type: Array,
      default: () => [],
      required: true,
    },
    isNeedPagination: {
      type: Boolean,
      default: true,
    },
    spanMethod: {
      type: Function,
      default: () => {},
    },
    size: {
      type: String,
      default: 'small',
    },
    border: {
      type: Boolean,
      default: false,
    },
    theadStyle: {
      type: Object,
      default: () => ({}),
    },
    handleCellStyle: {
      type: Function,
      default: () => {},
    },
    paginationData: {
      type: Object,
      default: () => ({
        curPage: 1,
        pageSize: 10,
        totalCount: 0,
      }),
    },
    tableHeight: {
      type: [String, Number],
      default: '100%',
    },
    maxHeight: {
      type: Number,
    },
    layout: {
      type: String,
      default: 'total, prev, pager, next, sizes, jumper',
    },
    rowClassName: {
      type: [String, Function],
      default: '',
    },
    isSelectable: {
      type: Boolean,
      default: false,
    },
    rowKey: {
      type: Function,
      default: () => {},
    }
  },
  data() {
    return {
      // tableHeight:'90%',
    };
  },
  computed: {
    theadColor() {
      return {
        backgroundColor: '#F6F7FA',
        fontWeight: 'bold',
        color: '#4a4a4a',
        fontSize: '14px',
        ...this.theadStyle,
      };
    },
  },
  methods: {
    handleSizeChange(pageSize) {
      this.$emit('updateTable', {
        pageSize,
        curPage: 1,
      });
    },
    handleCurrentChange(curPage) {
      this.$emit('updateTable', { curPage });
    },
    handleRowClick(row, column, event) {
      this.$emit('rowClick', row, column, event);
    },
    cellClick(row, column, cell, event) {
      this.$emit('cellClick', row, column, cell, event);
    },
    handleSelectChange(selection) {
      this.$emit('selectChange', selection);
    },
    handleSelectAll(selection) {
      this.$emit('selectAll', selection);
    },
  },
};
</script>

<style lang="less" scoped>
.data-table {
  height: 100%;
  display: flex;
  flex-flow: column;
  .el-table {
    flex: 1;
    width: 100%;
    display: flex;
    flex-direction: column;
    /deep/ .el-table__row:hover > td {
      background-color: transparent !important;
      cursor: pointer;
    }
    /deep/ .success-row {
      background-color: #bfe3ff;
    }
  }
  /deep/ .el-table__header-wrapper .gutter {
    background-color: #f6f7fa;
  }
  /deep/.el-table--scrollable-y .el-table__body-wrapper {
    &::-webkit-scrollbar {
      width: 10px;
      height: 1px;
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
      background: #5c6f92;
    }
    &::-webkit-scrollbar-track {
      /* 滚动条里面轨道 */
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
      border-radius: 10px;
      background: transparent;
    }
  }

  /deep/ .custom-cell {
    padding: 4px 0;
    .cell {
      padding-left: 4px;
      padding-right: 4px;
      line-height: 20px;
    }
  }
  /deep/.el-table__body-wrapper {
    overflow-y: auto;
    flex: 1;
    &::-webkit-scrollbar {
      width: 10px;
      height: 1px;
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
      background: #5c6f92;
    }
    &::-webkit-scrollbar-track {
      /* 滚动条里面轨道 */
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
      border-radius: 10px;
      background: transparent;
    }
  }
  .wrap-pagination {
    margin-top: 13px;
    border-top: 1px solid #ddd;
    text-align: right;
    height: 40px;
    .pagination {
      margin: 8px 0;
      padding-right: 0;
      /deep/ .el-pagination__sizes {
        margin-right: 0;
      }
    }
  }
}
</style>
