/deep/ .searchlist-item {
    padding: 10px;
    .searchlist-item-title {
        font-size: 16px;
        font-weight: 600;
        position: relative;
        text-indent: 8px;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        &::before {
            height: 16px;
            position: absolute;
            left: 0px;
            content: '';
            border: 2px solid #b0d0f4;
        }
        i {
            display: inline-block;
            height: 1px;
            background: #ecf5ff;
            flex: 1;
            margin-left: 10px;
        }
    }
    .searchlist {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: flex-start;
        width: 100%;
        flex-wrap: wrap;
        // max-height: 300px;
        .item-tag {
            overflow: hidden;
            width: calc(50% - 20px);
            margin: 10px;
            padding: 0 10px;
            font-size: 14px;
            border-radius: 2px;
            overflow: hidden;
            height: 30px;
            line-height: 30px;
            text-overflow: ellipsis;
            cursor: pointer;
            &:hover {
                background: #ecf5ff;
            }
        }
        .item-tag-active {
            background: #ecf5ff;
            color: #409eff;
        }
    }
}
