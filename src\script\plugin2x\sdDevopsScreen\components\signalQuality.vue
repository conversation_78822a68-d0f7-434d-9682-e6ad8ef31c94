<template>
    <div class="signal-quality">
        <com-titled :title="name" :searchParams.sync="searchParams" @iconClick="isShowMore = !isShowMore" :selectList="selectList" 
        :widgetParams="widgetParams"
        @closeShowMore="closeShowMore"
        ></com-titled>
        <div class="charts">
           <template v-for="(list,listIndex) in configure">
                <component
                    class="charts-item"
                    :ref="uuid+listIndex"
                    :key="listIndex"
                    :is="list.comp"
                    :name="uuid+listIndex"
                    :data="list.data"
                    :type="list.type"
                    :lengendList="lengendList[listIndex] || []"
                />
            </template>
        </div>
        <more-tips-bar
            :isShow.sync="isShowMore"
            :widgetParams="widgetParams"
            :refresh.sync ="refresh"
            @close="closeCard(widgetId)"
            @replace="replaceCard(uuid)"
            @refresh="init"
        ></more-tips-bar>
    </div>
</template>

<script>
import comTitled from './comTitled.vue';
import screenPieEchart from '_com/charts/screenPieEchart.vue';
import lineEchart from '_com/charts/lineEchart.vue';
import moreTipsBar from './moreTipsBar.vue';
import screenCommon from '../mixins/commonMixin.js';
export default {
    name:'signalQuality',
    components:{
        comTitled,
        screenPieEchart,
        lineEchart,
        moreTipsBar
    },
    inject: ['closeCard','replaceCard'],
    props:{
        name:{
            type:String,
            default:'信令质量',
        },
        uuid:{
            type:String,
            default:'67bfd6-e75d-4bd1-8a9f-1a1922fb5c16a'
        },
        widgetParams:{
            type:Object,
            default:() => ({}),
        },
        widgetId:{
            type:String,
            default:'',
        }
    },
    mixins:[screenCommon],
    inject: ['closeCard','replaceCard','updateComList'],
    data(){
        return{
            configure:[
                {
                    type:'singleLineArea',
                    comp:'lineEchart',
                    data:{
                        title:'数据量',
                        xAxisData:[],
                        yAxis:['单位（亿行）'],
                        unit:['十亿','分'],
                        isNull:true,
                        granularity:[''],
                        minValue:[''],
                        minInterval:[''],
                        lineData:[
                            {
                                name:'记录数',
                                data:[],
                                color:'#6AAFE1',
                                areaStyle:{
                                    color:{
                                        type: 'linear',
                                        x: 0,
                                        y: 0,
                                        x2: 0,
                                        y2: 1,
                                        colorStops: [{
                                            offset: 0, color: 'rgba(106,175,225, 1)' // 0% 处的颜色
                                        }, {
                                            offset: 1, color: 'rgba(106,175,225, 0.20)' // 100% 处的颜色
                                        }],
                                        global: false // 缺省为 false
                                    }
                                },
                            },
                        ]
                    }
                },
                {
                    type:'pie',
                    comp:'screenPieEchart',
                    data:{
                        pieData: [
                        { value: 0, name: '<60 s' },
                        { value: 0, name: '60-120 s' },
                        { value: 0, name: '120-300 s' },
                        { value: 0, name: '>300 s' },
                        ],
                        percent: {
                        '<60 s': '0.0%',
                        '60-120 s': '0.0%',
                        '120-300 s': '0.0%',
                        '>300 s': '0.0%',
                        },
                        title: '时延',
                    }
                },
                {
                    type:'lineRightLegend',
                    comp:'lineEchart',
                    data:{
                        title:'用户完整性',
                        xAxisData:[],
                        yAxis:['单位（亿行）'],
                        unit:['十亿','分'],
                        isNull:true,
                        granularity:['',''],
                        minValue:['',''],
                        minInterval:['',''],
                        lineData:[
                            {
                                name:'IMSI 回填率',
                                data:[],
                                color:'#B67DFF',
                            },
                            {
                                name:'MSISDN 回填率',
                                data:[],
                                color:'#67D9D9',
                            }
                        ]
                    }
                },
                {
                    type:'lineRightLegend',
                    comp:'lineEchart',
                    data:{
                        title:'基站完整性',
                        xAxisData:[],
                        yAxis:['单位（亿行）'],
                        unit:['十亿','分'],
                        isNull:true,
                        granularity:['',''],
                        minValue:['',''],
                        minInterval:['',''],
                        lineData:[
                            {
                                name:'ITAC 回填率',
                                data:[],
                                color:'#91E52C',
                            },
                            {
                                name:'CELL 回填率',
                                data:[],
                                color:'#EC3DCB',
                            }
                        ]
                    }
                },
            ],
            isShowMore:false,
            selectList:{
                isShowtimePick:true,
                isShowindex:true,
                isShowGranularity:true,
            },
            searchParams:{},
            sinRefreshTimer:null,
        };
    },
    computed:{
        postParams(){
            let startDate = new Date();
            let endDate = new Date().format('yyyy-MM-dd HH:mm:00');
            if(this.searchParams.relativeTime){
                startDate = new Date(startDate.setTime(startDate.getTime() - 60 * 1000 * this.searchParams.relativeTime)).format('yyyy-MM-dd HH:mm:00');
            }else{
                startDate = this.searchParams.startTime;
                endDate = this.searchParams.endTime;
            }
            return {
                startTime:startDate || this.defaultStartTime,
                endTime:endDate || this.defaultNewDate,
                province:this.searchParams.province || 'QG',
                dataType:this.searchParams.dataType || 2,
                granularity:this.searchParams.granularity,
            };
        }
    },
    watch:{
        searchParams:{
            handler(newV,oldV){
                this.init();
                if(JSON.stringify(oldV) !=='{}'){
                    const params = Object.assign({},this.widgetParams,newV);
                    this.updateComList(2,{
                        uuid:this.uuid,
                        widgetParams:{
                            ...params,
                        }
                    });
                }
            },
            deep:true,
        },
        refresh:{
            handler(newV,oldV){
                this.setIntervalTimer();
                if(JSON.stringify(oldV) !=='{}'){
                    const params = Object.assign({},this.widgetParams,{refresh:newV});
                    this.updateComList(2,{
                        uuid:this.uuid,
                        widgetParams:{
                            ...params,
                        }
                    });
                }
            },
            deep:true,
        }
    },
    beforeDestroy() {
        this.clearRefreshTimer();
    },
    methods:{
        resize(){
            this.configure.forEach((ele,index) => {
                const name = this.uuid+index;
                this.$refs[name][0].resize();
            });
        },
        init(){
            this.getUserIntegrity();
            this.getCellIntegrity();
            this.getSignalTrendChart();
            this.getSignalDelayChart();
        },
        getUserIntegrity(){
            // this.getPost('getUserIntegrity',this.postParams,'信令质量-用户完整性',({data}) => {
            //     this.handlerLineData(data,2);
            // });
        },
        getCellIntegrity(){
            // this.getPost('getCellIntegrity',this.postParams,'信令质量-基站完整性',({data}) => {
            //     this.handlerLineData(data,3);
            // });
        },
        getSignalTrendChart(){
            // this.getPost('getSignalTrendChart',this.postParams,'信令质量-数据量趋势图',({data}) => {
            //     this.handlerLineData(data,0);
            // });
        },
        getSignalDelayChart(){
            // this.getPost('getSignalDelayChart',this.postParams,'信令质量-时延饼图',({data}) => {
            //     this.handlerPieData(data,1);
            // });
        },
        setIntervalTimer(){
            this.clearRefreshTimer();
            const that = this;
            this.sinRefreshTimer = setInterval(() => {
                that.init();
            },that.refresh * 60000);
        },
        clearRefreshTimer(){
            this.sinRefreshTimer && clearInterval(this.sinRefreshTimer);
        },
    },

};
</script>

<style lang="less" scoped>
.signal-quality{
    width:calc(50% - .89rem);
    height: 25.11rem;
    background: linear-gradient(116deg, #23474F 0%, #1D2E3B 100%);
    border-radius: .67rem .67rem .67rem .67rem;
    position: relative;
}
.charts{
    width: 100%;
    height:calc(100% - 3.11rem);
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    .charts-item{
        width:50%;
        height:50%;
    }
}
</style>