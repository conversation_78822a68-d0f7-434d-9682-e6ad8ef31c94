import { loadView } from './plugin';
export default [
    { path: 'electronEventSub/index' }, //电子围栏实时事件订阅
    { path: 'realtimeRegionEvent/index' }, //实时区域事件
    { path: 'sdPersonHeatMap/index' }, //山东城市人员分布热力图
    { path: 'sdElectricFenceMap/index' }, //电子围栏分布热力图
    { path: 'sdDevopsScreen/index' }, //精准位置能力处理指标统计及可视化
    {
        path: 'sdPositionVisual/index',
        component: loadView('sdPositionVisual/index', false)
    }, //用户精准位置定位可视化
    { path: 'regionConfig/index' }, // 区域配置管理
    { path: 'sdTaskList/index' }, // 任务清单
    {
        path: 'sdLocAnalysis/index',
        component: loadView('main/index', false)
    }, //位置定位分析平台
    { path: 'sdPositionRealLoc/realLocation' }, // 实时位置定位
    { path: 'sdPositionRealLoc/show-realLocation' }, // 实时位置定位（明文查询）
    { path: 'sdPositionBaseStation/baseStation' }, // 历史轨迹查询（基站级）
    { path: 'sdPositionRoadNetworkFit/roadNetworkFit' }, // 历史轨迹查询（路网拟合）
    { path: 'sdPositionLoc/Location' }, // 常驻地查询
    { path: 'abilityMonitor/index' }, // 系统成效
    { path: 'sd45gFingerprintAssess/index' }, // 4、5G指纹库评估
    { path: 'effectivenessScreen/index' } // 成效大屏
];
