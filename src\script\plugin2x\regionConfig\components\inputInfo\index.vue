<template>
    <div class="input-info">
        <!-- 录入 -->
        <el-radio-group class="input-info__tab" v-model="curType" size="small">
            <el-radio-button label="singleEntry">单个录入</el-radio-button>
            <el-radio-button label="bulkEntry">批量录入</el-radio-button>
        </el-radio-group>
        <!-- 基础信息 -->
        <div class="input-info__main">
            <Keep-alive>
                <component :is="curType" :ref="`${curType}Ref`" v-bind="$attrs" v-on="$listeners" />
            </Keep-alive>
        </div>
        <div class="input-info__footer">
            <el-button
                v-if="curType === 'bulkEntry'"
                class="w-25 reset-btn"
                size="small"
                @click="cancel"
            >
                取消
            </el-button>
            <el-button type="primary" size="small" class="w-25" @click="createResource"
                >创建</el-button
            >
        </div>
    </div>
</template>

<script>
export default {
    name: 'input-info',
    components: {
        singleEntry: () => import('./components/singleEntry.vue'),
        bulkEntry: () => import('./components/bulkEntry.vue')
    },
    model: {
        prop: 'value',
        event: 'change'
    },
    props: {
        value: {
            type: String,
            default: 'singleEntry'
        }
    },
    data() {
        return {
            // curType: 'singleEntry',
        };
    },
    computed: {
        curType: {
            get() {
                return this.value;
            },
            set(newVal) {
                this.$emit('change', newVal);
            }
        }
    },
    methods: {
        async createResource() {
            if (this.curType === 'singleEntry') {
                const validRes = await this.$refs.singleEntryRef.validate();
                if (!validRes) {
                    this.$message.warning('存在必填项未填写');
                    return;
                }
                this.$emit('create', 'singleEntry');
            } else {
                const file = this.$refs.bulkEntryRef.file;
                if (!file) {
                    this.$message.warning('请先上传文件');
                    return;
                }
                this.$emit('create', 'bulkEntry', { file });
            }
        },
        cancel() {}
    }
};
</script>

<style lang="less" scoped>
.input-info {
    display: flex;
    flex-direction: column;
    padding: 10px 12px;
    height: 100%;

    &__tab {
        .el-radio-button {
            /deep/ .el-radio-button__orig-radio:checked + .el-radio-button__inner {
                background-color: transparent;
                color: #409eff;
                border-color: #409eff;
                font-weight: 500;
            }
        }
    }

    &__main {
        flex: 1;
        height: 0;
        width: 100%;
        background-color: transparent;
        // overflow-y: auto;
    }
    &__footer {
        margin-top: 16px;
        padding-right: 4px;
        display: flex;
        justify-content: end;
    }
}

.w-full {
    width: 100%;
}
.w-25 {
    width: 25%;
}
</style>
